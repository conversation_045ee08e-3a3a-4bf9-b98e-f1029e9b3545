/**
 * Reusable Input Component
 * Standardized input implementation with consistent styling and behavior
 */

import React, { forwardRef } from 'react';
import { InputProps } from '@/components/common/types';

export const Input = forwardRef<HTMLInputElement, InputProps>(({
  type = 'text',
  size = 'md',
  name,
  label,
  placeholder,
  required = false,
  disabled = false,
  error,
  helperText,
  value,
  onChange,
  onBlur,
  onFocus,
  leftIcon,
  rightIcon,
  autoComplete,
  autoFocus = false,
  maxLength,
  minLength,
  pattern,
  className = '',
  'data-testid': testId,
  id,
  ...props
}, ref) => {
  const inputId = id || `input-${name}`;
  
  const baseClasses = 'block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-600 disabled:cursor-not-allowed disabled:bg-gray-50 disabled:text-gray-500 disabled:ring-gray-200 dark:bg-gray-900 dark:text-white dark:ring-gray-600 dark:placeholder:text-gray-400 dark:focus:ring-blue-500 dark:disabled:bg-gray-800';
  
  const sizeClasses = {
    xs: 'px-2 py-1 text-xs',
    sm: 'px-2.5 py-1.5 text-sm',
    md: 'px-3 py-2 text-sm',
    lg: 'px-3.5 py-2.5 text-base',
    xl: 'px-4 py-3 text-lg'
  };

  const errorClasses = error 
    ? 'ring-red-300 focus:ring-red-500 dark:ring-red-600 dark:focus:ring-red-500' 
    : '';

  const iconPaddingClasses = {
    left: leftIcon ? 'pl-10' : '',
    right: rightIcon ? 'pr-10' : ''
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (onChange) {
      onChange(e.target.value);
    }
  };

  const inputClasses = `
    ${baseClasses}
    ${sizeClasses[size]}
    ${errorClasses}
    ${iconPaddingClasses.left}
    ${iconPaddingClasses.right}
    ${className}
  `.trim();

  return (
    <div className="w-full">
      {/* Label */}
      {label && (
        <label 
          htmlFor={inputId}
          className={`block text-sm font-medium leading-6 text-gray-900 dark:text-white mb-2 ${
            required ? "after:content-['*'] after:ml-0.5 after:text-red-500" : ''
          }`}
        >
          {label}
        </label>
      )}

      {/* Input Container */}
      <div className="relative">
        {/* Left Icon */}
        {leftIcon && (
          <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
            <div className="h-5 w-5 text-gray-400">
              {leftIcon}
            </div>
          </div>
        )}

        {/* Input */}
        <input
          ref={ref}
          type={type}
          id={inputId}
          name={name}
          value={value}
          onChange={handleChange}
          onBlur={onBlur}
          onFocus={onFocus}
          placeholder={placeholder}
          required={required}
          disabled={disabled}
          autoComplete={autoComplete}
          autoFocus={autoFocus}
          maxLength={maxLength}
          minLength={minLength}
          pattern={pattern}
          className={inputClasses}
          data-testid={testId}
          aria-invalid={error ? 'true' : 'false'}
          aria-describedby={
            error ? `${inputId}-error` : 
            helperText ? `${inputId}-helper` : undefined
          }
          {...props}
        />

        {/* Right Icon */}
        {rightIcon && (
          <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
            <div className="h-5 w-5 text-gray-400">
              {rightIcon}
            </div>
          </div>
        )}
      </div>

      {/* Error Message */}
      {error && (
        <p 
          id={`${inputId}-error`}
          className="mt-2 text-sm text-red-600 dark:text-red-400"
        >
          {error}
        </p>
      )}

      {/* Helper Text */}
      {helperText && !error && (
        <p 
          id={`${inputId}-helper`}
          className="mt-2 text-sm text-gray-500 dark:text-gray-400"
        >
          {helperText}
        </p>
      )}
    </div>
  );
});

Input.displayName = 'Input';

// Specialized input components for common use cases

// Search Input with built-in search icon
export interface SearchInputProps extends Omit<InputProps, 'leftIcon' | 'type'> {
  onSearch?: (value: string) => void;
  onClear?: () => void;
  allowClear?: boolean;
  enterButton?: boolean | string | React.ReactNode;
}

export const SearchInput: React.FC<SearchInputProps> = ({
  onSearch,
  onClear,
  allowClear = true,
  enterButton = false,
  value,
  onChange,
  placeholder = 'Search...',
  className = '',
  ...props
}) => {
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' && onSearch && value) {
      onSearch(value);
    }
  };

  const handleClear = () => {
    if (onChange) {
      onChange('');
    }
    if (onClear) {
      onClear();
    }
  };

  const SearchIcon = () => (
    <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
    </svg>
  );

  const ClearIcon = () => (
    <button
      type="button"
      onClick={handleClear}
      className="h-5 w-5 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
    >
      <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
      </svg>
    </button>
  );

  return (
    <div className={`flex ${className}`}>
      <Input
        type="search"
        leftIcon={<SearchIcon />}
        rightIcon={allowClear && value ? <ClearIcon /> : undefined}
        value={value}
        onChange={onChange}
        placeholder={placeholder}
        onKeyDown={handleKeyDown}
        className="flex-1"
        {...props}
      />
      
      {enterButton && (
        <button
          type="button"
          onClick={() => onSearch && value && onSearch(value)}
          className="ml-2 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
          disabled={!value}
        >
          {typeof enterButton === 'string' ? enterButton : 'Search'}
        </button>
      )}
    </div>
  );
};

// Password Input with show/hide toggle
export interface PasswordInputProps extends Omit<InputProps, 'type' | 'rightIcon'> {
  showToggle?: boolean;
}

export const PasswordInput: React.FC<PasswordInputProps> = ({
  showToggle = true,
  ...props
}) => {
  const [showPassword, setShowPassword] = React.useState(false);

  const ToggleIcon = () => (
    <button
      type="button"
      onClick={() => setShowPassword(!showPassword)}
      className="h-5 w-5 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
    >
      {showPassword ? (
        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
        </svg>
      ) : (
        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
        </svg>
      )}
    </button>
  );

  return (
    <Input
      type={showPassword ? 'text' : 'password'}
      rightIcon={showToggle ? <ToggleIcon /> : undefined}
      {...props}
    />
  );
};

// Textarea Component
export const Textarea = forwardRef<HTMLTextAreaElement, import('../common/types').TextareaProps>(({
  rows = 4,
  cols,
  resize = 'vertical',
  autoResize = false,
  name,
  label,
  placeholder,
  required = false,
  disabled = false,
  error,
  helperText,
  value,
  onChange,
  onBlur,
  onFocus,
  className = '',
  'data-testid': testId,
  id,
  ...props
}, ref) => {
  const textareaId = id || `textarea-${name}`;

  const baseClasses = 'block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-600 disabled:cursor-not-allowed disabled:bg-gray-50 disabled:text-gray-500 disabled:ring-gray-200 dark:bg-gray-900 dark:text-white dark:ring-gray-600 dark:placeholder:text-gray-400 dark:focus:ring-blue-500 dark:disabled:bg-gray-800';

  const errorClasses = error
    ? 'ring-red-300 focus:ring-red-500 dark:ring-red-600 dark:focus:ring-red-500'
    : '';

  const resizeClasses = {
    none: 'resize-none',
    vertical: 'resize-y',
    horizontal: 'resize-x',
    both: 'resize'
  };

  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    if (onChange) {
      onChange(e.target.value);
    }

    // Auto-resize functionality
    if (autoResize && e.target) {
      e.target.style.height = 'auto';
      e.target.style.height = `${e.target.scrollHeight}px`;
    }
  };

  const textareaClasses = `
    ${baseClasses}
    ${errorClasses}
    ${resizeClasses[resize]}
    ${className}
  `.trim();

  return (
    <div className="w-full">
      {/* Label */}
      {label && (
        <label
          htmlFor={textareaId}
          className={`block text-sm font-medium leading-6 text-gray-900 dark:text-white mb-2 ${
            required ? "after:content-['*'] after:ml-0.5 after:text-red-500" : ''
          }`}
        >
          {label}
        </label>
      )}

      {/* Textarea */}
      <textarea
        ref={ref}
        id={textareaId}
        name={name}
        rows={rows}
        cols={cols}
        value={value}
        onChange={handleChange}
        onBlur={onBlur}
        onFocus={onFocus}
        placeholder={placeholder}
        required={required}
        disabled={disabled}
        className={textareaClasses}
        data-testid={testId}
        aria-invalid={error ? 'true' : 'false'}
        aria-describedby={
          error ? `${textareaId}-error` :
          helperText ? `${textareaId}-helper` : undefined
        }
        {...props}
      />

      {/* Error Message */}
      {error && (
        <p
          id={`${textareaId}-error`}
          className="mt-2 text-sm text-red-600 dark:text-red-400"
        >
          {error}
        </p>
      )}

      {/* Helper Text */}
      {helperText && !error && (
        <p
          id={`${textareaId}-helper`}
          className="mt-2 text-sm text-gray-500 dark:text-gray-400"
        >
          {helperText}
        </p>
      )}
    </div>
  );
});

Textarea.displayName = 'Textarea';
