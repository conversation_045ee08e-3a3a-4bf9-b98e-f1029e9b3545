#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const filePath = path.join(__dirname, '../src/services/analyticsService.ts');

console.log('Fixing syntax errors in analyticsService.ts...');

let content = fs.readFileSync(filePath, 'utf8');

// Fix common syntax errors introduced by the automated script
const fixes = [
  // Fix awaits -> await getDocs
  { pattern: /awaits\(([^)]+)\)/g, replacement: 'await getDocs($1)' },
  
  // Fix .s. -> .docs.
  { pattern: /\.s\./g, replacement: '.docs.' },
  
  // Fix constof -> const doc of
  { pattern: /constof /g, replacement: 'const doc of ' },
  
  // Fix =.data() -> = doc.data()
  { pattern: /=\.data\(\)/g, replacement: '= doc.data()' },
  
  // Fix .id, -> doc.id,
  { pattern: /(\s+)id:\.id,/g, replacement: '$1id: doc.id,' },
  
  // Fix constumentsSnapshot -> const documentsSnapshot
  { pattern: /constumentsSnapshot/g, replacement: 'const documentsSnapshot' },
  
  // Fix umentsSnapshot -> documentsSnapshot
  { pattern: /umentsSnapshot/g, replacement: 'documentsSnapshot' },
  
  // Fix umentsQuery -> documentsQuery
  { pattern: /umentsQuery/g, replacement: 'documentsQuery' },
  
  // Fix ument -> document
  { pattern: /(\s+)ument([^a-zA-Z])/g, replacement: '$1document$2' },
  
  // Fix .forEach(=> { -> .forEach(doc => {
  { pattern: /\.forEach\(=>\s*\{/g, replacement: '.forEach(doc => {' },
  
  // Fix .map(=> -> .map(doc =>
  { pattern: /\.map\(=>/g, replacement: '.map(doc =>' },
  
  // Fix constument = -> const document =
  { pattern: /constument\s*=/g, replacement: 'const document =' },
  
  // Fix promptStats[.id] -> promptStats[doc.id]
  { pattern: /promptStats\[\.id\]/g, replacement: 'promptStats[doc.id]' },
  
  // Fix id:.id -> id: doc.id
  { pattern: /id:\.id/g, replacement: 'id: doc.id' },
  
  // Fix .data(); -> doc.data();
  { pattern: /(\s+)\.data\(\);/g, replacement: '$1doc.data();' },
  
  // Fix =>.data() -> doc => doc.data()
  { pattern: /=>\s*\.data\(\)/g, replacement: 'doc => doc.data()' }
];

let fixCount = 0;
fixes.forEach(fix => {
  const matches = content.match(fix.pattern);
  if (matches) {
    fixCount += matches.length;
    content = content.replace(fix.pattern, fix.replacement);
  }
});

// Write the fixed content back
fs.writeFileSync(filePath, content, 'utf8');

console.log(`✅ Fixed ${fixCount} syntax errors in analyticsService.ts`);
