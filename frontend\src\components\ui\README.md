# UI Component Library

A comprehensive collection of reusable UI components built with React, TypeScript, and Tailwind CSS. All components follow consistent design patterns, accessibility standards, and provide excellent developer experience.

## Features

- 🎨 **Consistent Design**: All components follow the same design system
- ♿ **Accessible**: Built with accessibility in mind (ARIA attributes, keyboard navigation)
- 🌙 **Dark Mode**: Full dark mode support
- 📱 **Responsive**: Mobile-first responsive design
- 🔧 **TypeScript**: Full TypeScript support with comprehensive type definitions
- 🎯 **Flexible**: Highly customizable with sensible defaults
- 🧪 **Testable**: Built with testing in mind (data-testid attributes)

## Components

### Layout Components

#### Modal
Overlay dialogs with accessibility support and focus management.

```tsx
import { Modal, ModalHeader, ModalBody, ModalFooter, useModal } from '@/components/ui';

const MyComponent = () => {
  const { isOpen, open, close } = useModal();
  
  return (
    <>
      <button onClick={open}>Open Modal</button>
      <Modal isOpen={isOpen} onClose={close}>
        <ModalHeader>
          <h2>Modal Title</h2>
        </ModalHeader>
        <ModalBody>
          <p>Modal content goes here</p>
        </ModalBody>
        <ModalFooter>
          <button onClick={close}>Close</button>
        </ModalFooter>
      </Modal>
    </>
  );
};
```

#### Card
Flexible card components for content organization.

```tsx
import { Card, CardHeader, CardBody, CardFooter, StatsCard } from '@/components/ui';

// Basic Card
<Card>
  <CardHeader>
    <h3>Card Title</h3>
  </CardHeader>
  <CardBody>
    <p>Card content</p>
  </CardBody>
  <CardFooter>
    <button>Action</button>
  </CardFooter>
</Card>

// Stats Card
<StatsCard
  title="Total Users"
  value="1,234"
  change={12.5}
  changeType="increase"
  icon={<UserIcon />}
/>
```

### Form Components

#### Input
Standardized input fields with consistent styling and validation.

```tsx
import { Input, SearchInput, PasswordInput } from '@/components/ui';

// Basic Input
<Input
  label="Email"
  type="email"
  placeholder="Enter your email"
  required
  error="Please enter a valid email"
/>

// Search Input
<SearchInput
  placeholder="Search..."
  onSearch={(value) => console.log('Searching:', value)}
  allowClear
  enterButton="Search"
/>

// Password Input
<PasswordInput
  label="Password"
  placeholder="Enter password"
  showToggle
/>
```

#### Textarea
Multi-line text input with auto-resize capability.

```tsx
import { Textarea } from '@/components/ui';

<Textarea
  label="Description"
  rows={4}
  autoResize
  placeholder="Enter description..."
/>
```

#### Select
Dropdown selection with search and grouping support.

```tsx
import { Select } from '@/components/ui';

const options = [
  { value: 'option1', label: 'Option 1' },
  { value: 'option2', label: 'Option 2', group: 'Group A' },
  { value: 'option3', label: 'Option 3', group: 'Group B' },
];

<Select
  label="Choose Option"
  options={options}
  searchable
  clearable
  placeholder="Select an option..."
/>
```

#### Checkbox & Radio
Form controls with group management.

```tsx
import { Checkbox, Radio, CheckboxGroup, RadioGroup } from '@/components/ui';

// Individual Checkbox
<Checkbox
  label="Accept terms"
  checked={accepted}
  onChange={setAccepted}
/>

// Checkbox Group
<CheckboxGroup
  label="Select features"
  options={[
    { value: 'feature1', label: 'Feature 1' },
    { value: 'feature2', label: 'Feature 2' },
  ]}
  value={selectedFeatures}
  onChange={setSelectedFeatures}
/>

// Radio Group
<RadioGroup
  label="Choose plan"
  name="plan"
  options={[
    { value: 'basic', label: 'Basic Plan' },
    { value: 'premium', label: 'Premium Plan' },
  ]}
  value={selectedPlan}
  onChange={setSelectedPlan}
/>
```

### Data Display Components

#### Table
Feature-rich data tables with sorting, pagination, and selection.

```tsx
import { Table } from '@/components/ui';

const columns = [
  {
    key: 'name',
    title: 'Name',
    dataIndex: 'name',
    sortable: true,
  },
  {
    key: 'email',
    title: 'Email',
    dataIndex: 'email',
  },
  {
    key: 'actions',
    title: 'Actions',
    render: (_, record) => (
      <button onClick={() => handleEdit(record)}>Edit</button>
    ),
  },
];

<Table
  columns={columns}
  data={users}
  loading={loading}
  pagination={{
    current: page,
    pageSize: 10,
    total: totalUsers,
    onChange: (page, pageSize) => setPage(page),
  }}
  selection={{
    selectedRowKeys: selectedUsers,
    onChange: (keys, rows) => setSelectedUsers(keys),
  }}
/>
```

### Button
Enhanced button component with variants and states.

```tsx
import { Button } from '@/components/ui';

<Button
  variant="primary"
  size="md"
  loading={isLoading}
  leftIcon={<PlusIcon />}
  onClick={handleClick}
>
  Add Item
</Button>
```

## Design System

### Sizes
- `xs`: Extra small
- `sm`: Small
- `md`: Medium (default)
- `lg`: Large
- `xl`: Extra large

### Variants
- `primary`: Primary action (blue)
- `secondary`: Secondary action (gray)
- `outline`: Outlined style
- `ghost`: Minimal style
- `danger`: Destructive action (red)
- `success`: Success action (green)
- `warning`: Warning action (yellow)

### Colors
All components support dark mode automatically through Tailwind CSS dark mode classes.

## Accessibility

All components are built with accessibility in mind:

- **Keyboard Navigation**: Full keyboard support
- **Screen Readers**: Proper ARIA attributes and labels
- **Focus Management**: Logical focus flow and visible focus indicators
- **Color Contrast**: WCAG compliant color combinations
- **Semantic HTML**: Proper HTML semantics

## Testing

Components include `data-testid` attributes for easy testing:

```tsx
<Input data-testid="email-input" />
<Button data-testid="submit-button">Submit</Button>
```

## Customization

All components accept a `className` prop for custom styling:

```tsx
<Button className="my-custom-class">Custom Button</Button>
```

Components are built with Tailwind CSS and can be easily customized through the Tailwind configuration.

## TypeScript Support

Full TypeScript support with comprehensive type definitions:

```tsx
import type { ButtonProps, InputProps, TableColumn } from '@/components/ui';

const MyButton: React.FC<ButtonProps> = (props) => {
  return <Button {...props} />;
};
```

## Best Practices

1. **Use semantic HTML**: Components use proper HTML elements
2. **Provide labels**: Always include labels for form elements
3. **Handle errors**: Use error props for validation feedback
4. **Test accessibility**: Use screen readers and keyboard navigation
5. **Follow naming conventions**: Use descriptive names for props and handlers

## Migration Guide

When migrating existing components to use this library:

1. Replace custom components with library components
2. Update prop names to match the standardized interface
3. Remove custom styling in favor of variant props
4. Add proper labels and error handling
5. Test accessibility and keyboard navigation
