import React, { useState, useEffect } from 'react';
import type { UserPlusIcon, UserGroupIcon, EnvelopeIcon, EllipsisVerticalIcon, CheckCircleIcon, ClockIcon, XCircleIcon } from '@heroicons/react/24/outline';
import { functions } from '@/config/firebase';
import { httpsCallable } from 'firebase/functions';

interface Member {
  user_id: string;
  email: string;
  display_name: string;
  role: string;
  joined_at: any;
  last_active: any;
  invitation_status: string;
}

interface MemberManagementProps {
  workspaceId: string;
  userRole: string;
  onMemberUpdate?: () => void;
}

const MemberManagement: React.FC<MemberManagementProps> = ({
  workspaceId,
  userRole,
  onMemberUpdate
}) => {
  const [members, setMembers] = useState<Member[]>([]);
  const [loading, setLoading] = useState(true);
  const [inviting, setInviting] = useState(false);
  const [showInviteForm, setShowInviteForm] = useState(false);
  const [inviteForm, setInviteForm] = useState({
    email: '',
    role: 'viewer'
  });

  const canManageMembers = ['owner', 'admin'].includes(userRole);

  useEffect(() => {
    if (workspaceId) {
      loadMembers();
    }
  }, [workspaceId]);

  const loadMembers = async () => {
    try {
      setLoading(true);
      // This would be a new Cloud Function to get workspace members
      // For now, we'll simulate the data structure
      setMembers([]);
    } catch (error) {
      console.error('Error loading members:', error);
    } finally {
      setLoading(false);
    }
  };

  // TODO: Add try-catch error handling to this async handler
  const handleInviteMember = async () => {
    if (!inviteForm.email.trim()) return;
    
    try {
      setInviting(true);
      const inviteWorkspaceMember = httpsCallable(functions, 'invite_workspace_member');
      
      const result = await inviteWorkspaceMember({
        workspace_id: workspaceId,
        email: inviteForm.email,
        role: inviteForm.role
      });
      
      const data = result.data as any;
      
      if (data.success) {
        setInviteForm({ email: '', role: 'viewer' });
        setShowInviteForm(false);
        loadMembers();
        if (onMemberUpdate) {
          onMemberUpdate();
        }
      } else {
        throw new Error(data.error || 'Failed to invite member');
      }
    } catch (error) {
      console.error('Error inviting member:', error);
      alert('Failed to invite member. Please try again.');
    } finally {
      setInviting(false);
    }
  };

  const getRoleColor = (role: string) => {
    const colors = {
      owner: 'bg-purple-100 text-purple-800 border-purple-200',
      admin: 'bg-blue-100 text-blue-800 border-blue-200',
      editor: 'bg-green-100 text-green-800 border-green-200',
      viewer: 'bg-gray-100 text-gray-800 border-gray-200'
    };
    return colors[role as keyof typeof colors] || colors.viewer;
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'accepted':
        return <CheckCircleIcon className="h-4 w-4 text-green-500" />;
      case 'pending':
        return <ClockIcon className="h-4 w-4 text-yellow-500" />;
      case 'declined':
        return <XCircleIcon className="h-4 w-4 text-red-500" />;
      default:
        return null;
    }
  };

  const formatDate = (timestamp: any) => {
    if (!timestamp) return 'Never';
    const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
    return date.toLocaleDateString();
  };

  return (
    <div className="bg-white shadow rounded-lg">
      {/* Header */}
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <UserGroupIcon className="h-6 w-6 text-gray-400" />
            <div>
              <h3 className="text-lg font-medium text-gray-900">Team Members</h3>
              <p className="text-sm text-gray-500">
                Manage workspace members and their permissions
              </p>
            </div>
          </div>
          
          {canManageMembers && (
            <button
              onClick={() => setShowInviteForm(!showInviteForm)}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <UserPlusIcon className="h-4 w-4 mr-2" />
              Invite Member
            </button>
          )}
        </div>
      </div>

      {/* Invite Form */}
      {showInviteForm && canManageMembers && (
        <div className="px-6 py-4 bg-gray-50 border-b border-gray-200">
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="md:col-span-2">
                <label htmlFor="invite-email" className="block text-sm font-medium text-gray-700">
                  Email Address
                </label>
                <input
                  type="email"
                  id="invite-email"
                  value={inviteForm.email}
                  onChange={(e) => setInviteForm({ ...inviteForm, email: e.target.value })}
                  className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  placeholder="<EMAIL>"
                />
              </div>
              
              <div>
                <label htmlFor="invite-role" className="block text-sm font-medium text-gray-700">
                  Role
                </label>
                <select
                  id="invite-role"
                  value={inviteForm.role}
                  onChange={(e) => setInviteForm({ ...inviteForm, role: e.target.value })}
                  className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                >
                  <option value="viewer">Viewer</option>
                  <option value="editor">Editor</option>
                  <option value="admin">Admin</option>
                </select>
              </div>
            </div>
            
            <div className="flex items-center space-x-3">
              <button
                onClick={handleInviteMember}
                disabled={inviting || !inviteForm.email.trim()}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {inviting ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Sending...
                  </>
                ) : (
                  <>
                    <EnvelopeIcon className="h-4 w-4 mr-2" />
                    Send Invitation
                  </>
                )}
              </button>
              
              <button
                onClick={() => setShowInviteForm(false)}
                className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Members List */}
      <div className="divide-y divide-gray-200">
        {loading ? (
          <div className="px-6 py-8 text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-2 text-sm text-gray-500">Loading members...</p>
          </div>
        ) : members.length === 0 ? (
          <div className="px-6 py-8 text-center">
            <UserGroupIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No members yet</h3>
            <p className="mt-1 text-sm text-gray-500">
              {canManageMembers 
                ? 'Invite team members to start collaborating.' 
                : 'Members will appear here once they join the workspace.'
              }
            </p>
          </div>
        ) : (
          members.map((member) => (
            <div key={member.user_id} className="px-6 py-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className="flex-shrink-0">
                    <div className="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                      <span className="text-sm font-medium text-gray-700">
                        {member.display_name?.charAt(0)?.toUpperCase() || member.email.charAt(0).toUpperCase()}
                      </span>
                    </div>
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2">
                      <p className="text-sm font-medium text-gray-900 truncate">
                        {member.display_name || member.email}
                      </p>
                      {getStatusIcon(member.invitation_status)}
                    </div>
                    <p className="text-sm text-gray-500 truncate">{member.email}</p>
                    <div className="flex items-center space-x-4 mt-1">
                      <span className="text-xs text-gray-400">
                        Joined {formatDate(member.joined_at)}
                      </span>
                      <span className="text-xs text-gray-400">
                        Last active {formatDate(member.last_active)}
                      </span>
                    </div>
                  </div>
                </div>
                
                <div className="flex items-center space-x-3">
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${getRoleColor(member.role)}`}>
                    {member.role}
                  </span>
                  
                  {canManageMembers && member.role !== 'owner' && (
                    <button className="text-gray-400 hover:text-gray-600">
                      <EllipsisVerticalIcon className="h-5 w-5" />
                    </button>
                  )}
                </div>
              </div>
            </div>
          ))
        )}
      </div>

      {/* Role Descriptions */}
      <div className="px-6 py-4 bg-gray-50 border-t border-gray-200">
        <h4 className="text-sm font-medium text-gray-900 mb-3">Role Permissions</h4>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 text-xs">
          <div>
            <span className="font-medium text-purple-800">Owner</span>
            <p className="text-gray-600 mt-1">Full access including billing and workspace deletion</p>
          </div>
          <div>
            <span className="font-medium text-blue-800">Admin</span>
            <p className="text-gray-600 mt-1">Manage members, settings, and all content</p>
          </div>
          <div>
            <span className="font-medium text-green-800">Editor</span>
            <p className="text-gray-600 mt-1">Create, edit, and share prompts and documents</p>
          </div>
          <div>
            <span className="font-medium text-gray-800">Viewer</span>
            <p className="text-gray-600 mt-1">View and execute shared prompts only</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export { MemberManagement };
export default MemberManagement;
