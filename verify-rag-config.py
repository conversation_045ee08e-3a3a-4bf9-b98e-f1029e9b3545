#!/usr/bin/env python3
"""
RAG Pipeline Configuration Verification
This script verifies that the RAG pipeline is properly configured
"""

import os
import sys
import asyncio
from datetime import datetime

# Add the functions directory to the path
sys.path.append('functions')

async def verify_rag_components():
    """Verify all RAG components are properly configured"""
    print("🔍 Verifying RAG Pipeline Configuration...")
    print("=" * 50)
    
    verification_results = {}
    
    try:
        # Test 1: Import core RAG components
        print("\n📦 Testing RAG Component Imports...")
        
        try:
            from functions.src.rag.document_processor import DocumentProcessor
            print("✅ DocumentProcessor: Imported successfully")
            verification_results['document_processor'] = True
        except Exception as e:
            print(f"❌ DocumentProcessor: Import failed - {e}")
            verification_results['document_processor'] = False
        
        try:
            from functions.src.rag.embedding_service import EmbeddingService
            print("✅ EmbeddingService: Imported successfully")
            verification_results['embedding_service'] = True
        except Exception as e:
            print(f"❌ EmbeddingService: Import failed - {e}")
            verification_results['embedding_service'] = False
        
        try:
            from functions.src.rag.vector_store import VectorStore
            print("✅ VectorStore: Imported successfully")
            verification_results['vector_store'] = True
        except Exception as e:
            print(f"❌ VectorStore: Import failed - {e}")
            verification_results['vector_store'] = False
        
        try:
            from functions.src.rag.semantic_search import SemanticSearch
            print("✅ SemanticSearch: Imported successfully")
            verification_results['semantic_search'] = True
        except Exception as e:
            print(f"❌ SemanticSearch: Import failed - {e}")
            verification_results['semantic_search'] = False
        
        try:
            from functions.src.rag.hybrid_search import HybridSearch
            print("✅ HybridSearch: Imported successfully")
            verification_results['hybrid_search'] = True
        except Exception as e:
            print(f"❌ HybridSearch: Import failed - {e}")
            verification_results['hybrid_search'] = False
        
        try:
            from functions.src.rag.context_retriever import ContextRetriever
            print("✅ ContextRetriever: Imported successfully")
            verification_results['context_retriever'] = True
        except Exception as e:
            print(f"❌ ContextRetriever: Import failed - {e}")
            verification_results['context_retriever'] = False
        
        # Test 2: Verify AI Service RAG Integration
        print("\n🤖 Testing AI Service RAG Integration...")
        
        try:
            from functions.src.ai_service import ai_service
            
            # Check if RAG methods exist
            if hasattr(ai_service, 'generate_rag_response'):
                print("✅ AI Service: generate_rag_response method available")
                verification_results['rag_method'] = True
            else:
                print("❌ AI Service: generate_rag_response method missing")
                verification_results['rag_method'] = False
            
            # Check if context retriever is initialized
            if hasattr(ai_service, 'context_retriever'):
                print("✅ AI Service: context_retriever initialized")
                verification_results['context_retriever_init'] = True
            else:
                print("❌ AI Service: context_retriever not initialized")
                verification_results['context_retriever_init'] = False
            
        except Exception as e:
            print(f"❌ AI Service RAG Integration: Failed - {e}")
            verification_results['ai_service_rag'] = False
        
        # Test 3: Check Firebase Functions RAG endpoints
        print("\n🔗 Checking Firebase Functions RAG Endpoints...")
        
        try:
            # Check if main.py has RAG-related functions
            with open('functions/main.py', 'r') as f:
                main_content = f.read()
            
            rag_functions = [
                'rag_chat',
                'upload_document',
                'search_documents'
            ]
            
            for func_name in rag_functions:
                if func_name in main_content:
                    print(f"✅ Firebase Function: {func_name} found")
                    verification_results[f'function_{func_name}'] = True
                else:
                    print(f"❌ Firebase Function: {func_name} missing")
                    verification_results[f'function_{func_name}'] = False
        
        except Exception as e:
            print(f"❌ Firebase Functions RAG Check: Failed - {e}")
            verification_results['firebase_functions_rag'] = False
        
        # Test 4: Verify environment variables for embeddings
        print("\n🔧 Checking Environment Variables for RAG...")
        
        embedding_vars = [
            'OPENAI_API_KEY',  # For OpenAI embeddings
            'GOOGLE_API_KEY',  # For Google embeddings
            'OPENROUTER_API_KEY'  # Fallback for embeddings
        ]
        
        available_embedding_providers = 0
        for var in embedding_vars:
            if os.getenv(var):
                print(f"✅ {var}: Available for embeddings")
                available_embedding_providers += 1
            else:
                print(f"⚠️  {var}: Not set")
        
        if available_embedding_providers > 0:
            print(f"✅ Embedding providers: {available_embedding_providers} available")
            verification_results['embedding_providers'] = True
        else:
            print("❌ Embedding providers: None available")
            verification_results['embedding_providers'] = False
        
        # Test 5: Check document storage configuration
        print("\n📁 Checking Document Storage Configuration...")
        
        try:
            # Check if Firebase Storage is configured
            storage_configured = True  # Assume configured since we have Firebase
            print("✅ Firebase Storage: Configured for document storage")
            verification_results['storage_config'] = True
        except Exception as e:
            print(f"❌ Firebase Storage: Configuration issue - {e}")
            verification_results['storage_config'] = False
        
        # Summary
        print("\n" + "=" * 50)
        print("📊 RAG CONFIGURATION SUMMARY")
        print("=" * 50)
        
        categories = {
            "Core Components": [
                'document_processor', 'embedding_service', 'vector_store',
                'semantic_search', 'hybrid_search', 'context_retriever'
            ],
            "AI Service Integration": [
                'rag_method', 'context_retriever_init'
            ],
            "Firebase Functions": [
                'function_rag_chat', 'function_upload_document', 'function_search_documents'
            ],
            "Configuration": [
                'embedding_providers', 'storage_config'
            ]
        }
        
        total_passed = 0
        total_tests = 0
        
        for category, tests in categories.items():
            print(f"\n{category}:")
            category_passed = 0
            for test in tests:
                status = "✅ PASS" if verification_results.get(test, False) else "❌ FAIL"
                test_name = test.replace('_', ' ').replace('function ', '').title()
                print(f"  {test_name:.<25} {status}")
                if verification_results.get(test, False):
                    category_passed += 1
                total_tests += 1
            
            total_passed += category_passed
            print(f"  Category Score: {category_passed}/{len(tests)}")
        
        print(f"\n🎯 Overall RAG Configuration Score: {total_passed}/{total_tests}")
        
        if total_passed >= total_tests * 0.8:  # 80% pass rate
            print("🎉 RAG pipeline is well configured!")
            return True
        else:
            print("⚠️  RAG pipeline needs attention. Check failed components above.")
            return False
            
    except Exception as e:
        print(f"❌ Critical error during RAG verification: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_rag_dependencies():
    """Check if RAG dependencies are installed"""
    print("📦 Checking RAG Dependencies...")
    print("-" * 30)
    
    required_packages = [
        'numpy',
        'scikit-learn',
        'sentence-transformers',
        'faiss-cpu',
        'nltk',
        'spacy'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"✅ {package}: Installed")
        except ImportError:
            print(f"❌ {package}: Missing")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n⚠️  Missing packages: {missing_packages}")
        print("Install them with: pip install " + " ".join(missing_packages))
        return False
    
    print("\n✅ All RAG dependencies are installed")
    return True

async def main():
    """Main verification function"""
    print("🔍 RAG Pipeline Configuration Verification")
    print("=" * 50)
    print(f"Timestamp: {datetime.now().isoformat()}")
    print()
    
    # Check dependencies first
    deps_ok = check_rag_dependencies()
    print()
    
    # Verify RAG configuration
    config_ok = await verify_rag_components()
    
    if deps_ok and config_ok:
        print("\n🎉 RAG pipeline verification completed successfully!")
        print("\n📋 Next steps:")
        print("1. Deploy Firebase Functions with RAG support")
        print("2. Upload test documents to verify document processing")
        print("3. Test RAG functionality through the frontend")
        sys.exit(0)
    else:
        print("\n❌ RAG pipeline verification failed!")
        print("\n🔧 Recommended actions:")
        if not deps_ok:
            print("1. Install missing Python packages")
        if not config_ok:
            print("2. Fix configuration issues identified above")
        print("3. Re-run this verification script")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
