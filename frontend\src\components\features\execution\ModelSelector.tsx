import React, { useState, useEffect } from 'react';
import type { ChevronDownIcon, CheckIcon, CpuChipIcon, CurrencyDollarIcon } from '@heroicons/react/24/outline';
import { functions } from '@/config/firebase';
import { httpsCallable } from 'firebase/functions';

interface LLMModel {
  key: string;
  provider: string;
  model_name: string;
  cost_per_1k_tokens: number;
  context_window: number;
  configured: boolean;
}

interface ModelSelectorProps {
  selectedModels: string[];
  onModelSelect: (models: string[]) => void;
  showComparison: boolean;
  onComparisonToggle: (enabled: boolean) => void;
}

const ModelSelector: React.FC<ModelSelectorProps> = ({
  selectedModels,
  onModelSelect,
  showComparison,
  onComparisonToggle
}) => {
  const [availableModels, setAvailableModels] = useState<Record<string, LLMModel>>({});
  const [apiKeysStatus, setApiKeysStatus] = useState<Record<string, boolean>>({});
  const [isOpen, setIsOpen] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadAvailableModels();
  }, []);

  const loadAvailableModels = async () => {
    try {
      const getModels = httpsCallable(functions, 'get_available_models');
      const result = await getModels();
      const data = result.data as any;
      
      if (data.success) {
        setAvailableModels(data.models);
        setApiKeysStatus(data.apiKeysConfigured);
      }
    } catch (error) {
      console.error('Error loading models:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleModelToggle = (modelKey: string) => {
    if (showComparison) {
      // Multi-select mode
      if (selectedModels.includes(modelKey)) {
        onModelSelect(selectedModels.filter(m => m !== modelKey));
      } else {
        onModelSelect([...selectedModels, modelKey]);
      }
    } else {
      // Single select mode
      onModelSelect([modelKey]);
      setIsOpen(false);
    }
  };

  const getProviderColor = (provider: string) => {
    const colors = {
      openrouter: 'bg-blue-100 text-blue-800',
      openai: 'bg-green-100 text-green-800',
      anthropic: 'bg-purple-100 text-purple-800',
      cohere: 'bg-orange-100 text-orange-800'
    };
    return colors[provider as keyof typeof colors] || 'bg-gray-100 text-gray-800';
  };

  const getSelectedModelNames = () => {
    if (selectedModels.length === 0) return 'Select models';
    if (selectedModels.length === 1) {
      const model = availableModels[selectedModels[0]];
      return model ? `${model.provider}/${model.model_name.split('/').pop()}` : selectedModels[0];
    }
    return `${selectedModels.length} models selected`;
  };

  const configuredModels = Object.entries(availableModels).filter(([__, model]) => model.configured);
  const unconfiguredModels = Object.entries(availableModels).filter(([__, model]) => !model.configured);

  if (loading) {
    return (
      <div className="animate-pulse">
        <div className="h-10 bg-gray-200 rounded-md"></div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Comparison Mode Toggle */}
      <div className="flex items-center justify-between">
        <label htmlFor="comparison-toggle" className="flex items-center space-x-2">
          <input
            id="comparison-toggle"
            type="checkbox"
            checked={showComparison}
            onChange={(e) => onComparisonToggle(e.target.checked)}
            className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
          />
          <span className="text-sm font-medium text-gray-700">
            Compare multiple models
          </span>
        </label>
        
        {showComparison && selectedModels.length > 1 && (
          <span className="text-xs text-gray-500">
            Total cost will be multiplied by {selectedModels.length}
          </span>
        )}
      </div>

      {/* Model Selector */}
      <div className="relative">
        <label htmlFor="model-selector" className="block text-sm font-medium text-gray-700 mb-1">
          Model Selection
        </label>
        <button
          id="model-selector"
          type="button"
          onClick={() => setIsOpen(!isOpen)}
          aria-expanded={isOpen}
          aria-haspopup="listbox"
          aria-label="Select AI models"
          className="relative w-full bg-white border border-gray-300 rounded-md shadow-sm pl-3 pr-10 py-2 text-left cursor-default focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
        >
          <span className="block truncate">{getSelectedModelNames()}</span>
          <span className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
            <ChevronDownIcon className="h-5 w-5 text-gray-400" />
          </span>
        </button>

        {isOpen && (
          <div
            role="listbox"
            aria-label="Available AI models"
            className="absolute z-10 mt-1 w-full bg-white shadow-lg max-h-96 rounded-md py-1 text-base ring-1 ring-black ring-opacity-5 overflow-auto focus:outline-none sm:text-sm"
          >
            {/* Configured Models */}
            {configuredModels.length > 0 && (
              <>
                <div className="px-3 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wide bg-gray-50">
                  Available Models
                </div>
                {configuredModels.map(([key, model]) => (
                  <div
                    key={key}
                    role="option"
                    aria-selected={selectedModels.includes(key)}
                    onClick={() => handleModelToggle(key)}
                    className="cursor-pointer select-none relative py-3 px-3 hover:bg-gray-50"
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        {showComparison && (
                          <div className="flex-shrink-0">
                            {selectedModels.includes(key) ? (
                              <CheckIcon className="h-5 w-5 text-blue-600" />
                            ) : (
                              <div className="h-5 w-5 border border-gray-300 rounded"></div>
                            )}
                          </div>
                        )}
                        
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center space-x-2">
                            <span className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${getProviderColor(model.provider)}`}>
                              {model.provider}
                            </span>
                            <span className="text-sm font-medium text-gray-900 truncate">
                              {model.model_name.split('/').pop()}
                            </span>
                          </div>
                          
                          <div className="flex items-center space-x-4 mt-1 text-xs text-gray-500">
                            <div className="flex items-center space-x-1">
                              <CurrencyDollarIcon className="h-3 w-3" />
                              <span>${model.cost_per_1k_tokens}/1K tokens</span>
                            </div>
                            <div className="flex items-center space-x-1">
                              <CpuChipIcon className="h-3 w-3" />
                              <span>{model.context_window.toLocaleString()} tokens</span>
                            </div>
                          </div>
                        </div>
                      </div>
                      
                      {!showComparison && selectedModels.includes(key) && (
                        <CheckIcon className="h-5 w-5 text-blue-600" />
                      )}
                    </div>
                  </div>
                ))}
              </>
            )}

            {/* Unconfigured Models */}
            {unconfiguredModels.length > 0 && (
              <>
                <div className="px-3 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wide bg-gray-50 border-t">
                  Requires API Key Configuration
                </div>
                {unconfiguredModels.map(([key, model]) => (
                  <div
                    key={key}
                    className="select-none relative py-3 px-3 opacity-50"
                  >
                    <div className="flex items-center space-x-3">
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-2">
                          <span className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${getProviderColor(model.provider)}`}>
                            {model.provider}
                          </span>
                          <span className="text-sm font-medium text-gray-900 truncate">
                            {model.model_name.split('/').pop()}
                          </span>
                        </div>
                        
                        <div className="flex items-center space-x-4 mt-1 text-xs text-gray-500">
                          <div className="flex items-center space-x-1">
                            <CurrencyDollarIcon className="h-3 w-3" />
                            <span>${model.cost_per_1k_tokens}/1K tokens</span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <CpuChipIcon className="h-3 w-3" />
                            <span>{model.context_window.toLocaleString()} tokens</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </>
            )}

            {configuredModels.length === 0 && (
              <div className="px-3 py-4 text-sm text-gray-500 text-center">
                No models configured. Please add API keys in settings.
              </div>
            )}
          </div>
        )}
      </div>

      {/* API Keys Status */}
      <div className="text-xs text-gray-500">
        <div className="flex items-center space-x-4">
          <span>API Keys:</span>
          {Object.entries(apiKeysStatus).map(([provider, configured]) => (
            <span
              key={provider}
              className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${
                configured 
                  ? 'bg-green-100 text-green-800' 
                  : 'bg-red-100 text-red-800'
              }`}
            >
              {provider} {configured ? '✓' : '✗'}
            </span>
          ))}
        </div>
      </div>
    </div>
  );
};

export default ModelSelector;
