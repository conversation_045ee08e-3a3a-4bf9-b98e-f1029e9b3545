/**
 * Bundle Size Performance Tests
 * 
 * Monitors and validates bundle sizes to ensure they stay within performance budgets.
 * Tracks bundle size trends and detects regressions.
 * 
 * @module BundleSizeTests
 * @version 1.0.0
 */

import { describe, it, expect, beforeAll } from 'vitest';
import { readFileSync, existsSync, readdirSync, statSync } from 'fs';
import { join, extname } from 'path';
import { gzipSync } from 'zlib';

// Performance budget configuration
const performanceBudget = JSON.parse(
  readFileSync(join(__dirname, '../../../performance-budget.json'), 'utf-8')
);

interface BundleAnalysis {
  files: {
    name: string;
    size: number;
    gzipSize: number;
    type: 'script' | 'stylesheet' | 'image' | 'font' | 'other';
  }[];
  totals: {
    totalSize: number;
    totalGzipSize: number;
    scriptSize: number;
    stylesheetSize: number;
    imageSize: number;
    fontSize: number;
  };
}

interface BundleSizeThresholds {
  script: number;
  stylesheet: number;
  image: number;
  font: number;
  total: number;
}

describe('Bundle Size Performance Tests', () => {
  let bundleAnalysis: BundleAnalysis;
  let thresholds: BundleSizeThresholds;

  beforeAll(() => {
    // Extract thresholds from performance budget
    const budgetConfig = performanceBudget.budgets[0].resourceSizes;
    thresholds = {
      script: budgetConfig.find((r: any) => r.resourceType === 'script')?.maximumSizeKb * 1024 || 500 * 1024,
      stylesheet: budgetConfig.find((r: any) => r.resourceType === 'stylesheet')?.maximumSizeKb * 1024 || 100 * 1024,
      image: budgetConfig.find((r: any) => r.resourceType === 'image')?.maximumSizeKb * 1024 || 1000 * 1024,
      font: budgetConfig.find((r: any) => r.resourceType === 'font')?.maximumSizeKb * 1024 || 200 * 1024,
      total: budgetConfig.find((r: any) => r.resourceType === 'total')?.maximumSizeKb * 1024 || 2000 * 1024,
    };

    // Analyze bundle
    bundleAnalysis = analyzeBundleSize();
  });

  const analyzeBundleSize = (): BundleAnalysis => {
    const distPath = join(__dirname, '../../../dist');
    
    if (!existsSync(distPath)) {
      throw new Error('Build output not found. Please run "npm run build" first.');
    }

    const files: BundleAnalysis['files'] = [];
    
    const analyzeDirectory = (dirPath: string, relativePath = '') => {
      const items = readdirSync(dirPath);
      
      for (const item of items) {
        const fullPath = join(dirPath, item);
        const relativeFilePath = join(relativePath, item);
        const stats = statSync(fullPath);
        
        if (stats.isDirectory()) {
          analyzeDirectory(fullPath, relativeFilePath);
        } else if (stats.isFile()) {
          const content = readFileSync(fullPath);
          const gzipSize = gzipSync(content).length;
          
          files.push({
            name: relativeFilePath,
            size: stats.size,
            gzipSize,
            type: getFileType(item),
          });
        }
      }
    };

    analyzeDirectory(distPath);

    // Calculate totals
    const totals = files.reduce(
      (acc, file) => {
        acc.totalSize += file.size;
        acc.totalGzipSize += file.gzipSize;
        
        switch (file.type) {
          case 'script':
            acc.scriptSize += file.size;
            break;
          case 'stylesheet':
            acc.stylesheetSize += file.size;
            break;
          case 'image':
            acc.imageSize += file.size;
            break;
          case 'font':
            acc.fontSize += file.size;
            break;
        }
        
        return acc;
      },
      {
        totalSize: 0,
        totalGzipSize: 0,
        scriptSize: 0,
        stylesheetSize: 0,
        imageSize: 0,
        fontSize: 0,
      }
    );

    return { files, totals };
  };

  const getFileType = (filename: string): BundleAnalysis['files'][0]['type'] => {
    const ext = extname(filename).toLowerCase();
    
    if (['.js', '.mjs', '.ts'].includes(ext)) return 'script';
    if (['.css', '.scss', '.sass', '.less'].includes(ext)) return 'stylesheet';
    if (['.png', '.jpg', '.jpeg', '.gif', '.svg', '.webp', '.avif'].includes(ext)) return 'image';
    if (['.woff', '.woff2', '.ttf', '.otf', '.eot'].includes(ext)) return 'font';
    
    return 'other';
  };

  const formatBytes = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`;
  };

  describe('Total Bundle Size Validation', () => {
    it('should not exceed total bundle size threshold', () => {
      console.log(`Total Bundle Size: ${formatBytes(bundleAnalysis.totals.totalSize)} (threshold: ${formatBytes(thresholds.total)})`);
      console.log(`Total Gzipped Size: ${formatBytes(bundleAnalysis.totals.totalGzipSize)}`);
      
      expect(bundleAnalysis.totals.totalSize).toBeLessThanOrEqual(thresholds.total);
    });

    it('should have reasonable compression ratio', () => {
      const compressionRatio = bundleAnalysis.totals.totalGzipSize / bundleAnalysis.totals.totalSize;
      
      console.log(`Compression Ratio: ${(compressionRatio * 100).toFixed(1)}%`);
      
      // Expect at least 30% compression (compression ratio should be <= 0.7)
      expect(compressionRatio).toBeLessThanOrEqual(0.7);
    });
  });

  describe('JavaScript Bundle Size Validation', () => {
    it('should not exceed JavaScript bundle size threshold', () => {
      console.log(`JavaScript Bundle Size: ${formatBytes(bundleAnalysis.totals.scriptSize)} (threshold: ${formatBytes(thresholds.script)})`);
      
      expect(bundleAnalysis.totals.scriptSize).toBeLessThanOrEqual(thresholds.script);
    });

    it('should have main bundle within reasonable size', () => {
      const mainBundle = bundleAnalysis.files.find(file => 
        file.type === 'script' && file.name.includes('index') && !file.name.includes('legacy')
      );
      
      if (mainBundle) {
        console.log(`Main Bundle Size: ${formatBytes(mainBundle.size)}`);
        console.log(`Main Bundle Gzipped: ${formatBytes(mainBundle.gzipSize)}`);
        
        // Main bundle should be under 300KB
        expect(mainBundle.size).toBeLessThanOrEqual(300 * 1024);
      }
    });

    it('should have vendor chunks properly split', () => {
      const vendorChunks = bundleAnalysis.files.filter(file => 
        file.type === 'script' && (file.name.includes('vendor') || file.name.includes('chunk'))
      );
      
      console.log(`Vendor Chunks: ${vendorChunks.length}`);
      
      if (vendorChunks.length > 0) {
        const largestVendorChunk = Math.max(...vendorChunks.map(chunk => chunk.size));
        console.log(`Largest Vendor Chunk: ${formatBytes(largestVendorChunk)}`);
        
        // No single vendor chunk should be larger than 200KB
        expect(largestVendorChunk).toBeLessThanOrEqual(200 * 1024);
      }
    });
  });

  describe('CSS Bundle Size Validation', () => {
    it('should not exceed CSS bundle size threshold', () => {
      console.log(`CSS Bundle Size: ${formatBytes(bundleAnalysis.totals.stylesheetSize)} (threshold: ${formatBytes(thresholds.stylesheet)})`);
      
      expect(bundleAnalysis.totals.stylesheetSize).toBeLessThanOrEqual(thresholds.stylesheet);
    });

    it('should have CSS properly minified', () => {
      const cssFiles = bundleAnalysis.files.filter(file => file.type === 'stylesheet');
      
      for (const cssFile of cssFiles) {
        const compressionRatio = cssFile.gzipSize / cssFile.size;
        
        console.log(`${cssFile.name}: ${formatBytes(cssFile.size)} -> ${formatBytes(cssFile.gzipSize)} (${(compressionRatio * 100).toFixed(1)}%)`);
        
        // CSS should compress well (expect at least 60% compression)
        expect(compressionRatio).toBeLessThanOrEqual(0.4);
      }
    });
  });

  describe('Asset Size Validation', () => {
    it('should not exceed image bundle size threshold', () => {
      console.log(`Image Bundle Size: ${formatBytes(bundleAnalysis.totals.imageSize)} (threshold: ${formatBytes(thresholds.image)})`);
      
      expect(bundleAnalysis.totals.imageSize).toBeLessThanOrEqual(thresholds.image);
    });

    it('should not exceed font bundle size threshold', () => {
      console.log(`Font Bundle Size: ${formatBytes(bundleAnalysis.totals.fontSize)} (threshold: ${formatBytes(thresholds.font)})`);
      
      expect(bundleAnalysis.totals.fontSize).toBeLessThanOrEqual(thresholds.font);
    });

    it('should have optimized image formats', () => {
      const imageFiles = bundleAnalysis.files.filter(file => file.type === 'image');
      
      for (const imageFile of imageFiles) {
        console.log(`${imageFile.name}: ${formatBytes(imageFile.size)}`);
        
        // Individual images should not be too large
        expect(imageFile.size).toBeLessThanOrEqual(100 * 1024); // 100KB per image
      }
    });
  });

  describe('Bundle Analysis and Optimization', () => {
    it('should identify largest files for optimization', () => {
      const sortedFiles = [...bundleAnalysis.files]
        .sort((a, b) => b.size - a.size)
        .slice(0, 10);
      
      console.log('\nLargest Files:');
      sortedFiles.forEach((file, index) => {
        console.log(`${index + 1}. ${file.name}: ${formatBytes(file.size)} (${file.type})`);
      });
      
      // Ensure no single file is excessively large
      const largestFile = sortedFiles[0];
      if (largestFile) {
        expect(largestFile.size).toBeLessThanOrEqual(400 * 1024); // 400KB max for any single file
      }
    });

    it('should validate chunk splitting effectiveness', () => {
      const scriptFiles = bundleAnalysis.files.filter(file => file.type === 'script');
      
      console.log(`\nJavaScript Chunks: ${scriptFiles.length}`);
      
      if (scriptFiles.length > 1) {
        const sizes = scriptFiles.map(file => file.size);
        const averageSize = sizes.reduce((a, b) => a + b, 0) / sizes.length;
        const maxSize = Math.max(...sizes);
        const minSize = Math.min(...sizes);
        
        console.log(`Average Chunk Size: ${formatBytes(averageSize)}`);
        console.log(`Size Range: ${formatBytes(minSize)} - ${formatBytes(maxSize)}`);
        
        // Chunks should be reasonably balanced (largest shouldn't be more than 5x smallest)
        expect(maxSize / minSize).toBeLessThanOrEqual(5);
      }
    });

    it('should track bundle size trends', () => {
      // In a real implementation, this would compare against historical data
      const currentSizes = {
        total: bundleAnalysis.totals.totalSize,
        script: bundleAnalysis.totals.scriptSize,
        stylesheet: bundleAnalysis.totals.stylesheetSize,
        timestamp: new Date().toISOString(),
      };
      
      console.log('\nBundle Size Snapshot:');
      console.log(JSON.stringify(currentSizes, null, 2));
      
      // Store this data for trend analysis (in real implementation)
      // This test ensures the data structure is correct
      expect(currentSizes.total).toBeGreaterThan(0);
      expect(currentSizes.script).toBeGreaterThan(0);
    });
  });

  describe('Performance Budget Compliance', () => {
    it('should comply with Vite-specific performance budget', () => {
      const viteBudget = performanceBudget.vite;
      
      if (viteBudget) {
        const chunkSizeWarningLimit = viteBudget.chunkSizeWarningLimit * 1024; // Convert KB to bytes
        
        const scriptFiles = bundleAnalysis.files.filter(file => file.type === 'script');
        
        for (const scriptFile of scriptFiles) {
          console.log(`${scriptFile.name}: ${formatBytes(scriptFile.size)} (limit: ${formatBytes(chunkSizeWarningLimit)})`);
          expect(scriptFile.size).toBeLessThanOrEqual(chunkSizeWarningLimit);
        }
      }
    });

    it('should validate asset inlining threshold', () => {
      const viteBudget = performanceBudget.vite;
      
      if (viteBudget && viteBudget.assetsInlineLimit) {
        const inlineLimit = viteBudget.assetsInlineLimit;
        
        // Small assets should be inlined (this test verifies the configuration is working)
        const smallAssets = bundleAnalysis.files.filter(file => 
          file.type === 'image' && file.size <= inlineLimit
        );
        
        console.log(`Small assets (≤${inlineLimit}B): ${smallAssets.length}`);
        
        // This is more of a configuration validation than a strict test
        expect(inlineLimit).toBeGreaterThan(0);
      }
    });
  });
});
