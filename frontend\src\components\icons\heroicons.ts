// Auto-generated optimized Heroicons exports
// This file exports only the icons used in the application

// Outline icons
export {
  AcademicCapIcon,
  ArrowDownTrayIcon,
  ArrowTrendingDownIcon,
  ArrowTrendingUpIcon,
  BeakerIcon,
  BellIcon,
  BoltIcon,
  BookOpenIcon,
  BrainIcon,
  BriefcaseIcon,
  BugAntIcon,
  BuildingOfficeIcon,
  CalendarIcon,
  ChartBarIcon,
  ChatBubbleLeftIcon,
  ChatBubbleLeftRightIcon,
  CheckCircleIcon,
  CheckIcon,
  ChevronDownIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  ChevronUpIcon,
  ClipboardIcon,
  ClockIcon,
  Cog6ToothIcon,
  CogIcon,
  ComputerDesktopIcon,
  CpuChipIcon,
  CreditCardIcon,
  CubeIcon,
  CurrencyDollarIcon,
  DevicePhoneMobileIcon,
  DocumentArrowDownIcon,
  DocumentDuplicateIcon,
  DocumentTextIcon,
  EllipsisVerticalIcon,
  EnvelopeIcon,
  ExclamationTriangleIcon,
  EyeIcon,
  FireIcon,
  FunnelIcon,
  GlobeAltIcon,
  HeartIcon,
  InformationCircleIcon,
  KeyIcon,
  LightBulbIcon,
  LinkIcon,
  LockClosedIcon,
  MagnifyingGlassIcon,
  MegaphoneIcon,
  PaperAirplaneIcon,
  PauseIcon,
  PencilIcon,
  PlayCircleIcon,
  PlayIcon,
  PlusIcon,
  QuestionMarkCircleIcon,
  ReplyIcon,
  RocketLaunchIcon,
  ScaleIcon,
  ServerIcon,
  ShareIcon,
  ShieldCheckIcon,
  ShieldExclamationIcon,
  SignalIcon,
  SparklesIcon,
  StarIcon,
  StopIcon,
  TagIcon,
  TrashIcon,
  TrendingDownIcon,
  TrendingUpIcon,
  TrophyIcon,
  UserGroupIcon,
  UserIcon,
  UserPlusIcon,
  VideoCameraIcon,
  XCircleIcon,
  XMarkIcon
} from '@heroicons/react/24/outline';

// Solid icons (add as needed)
export {
  StarIcon as StarIconSolid
} from '@heroicons/react/24/solid';
