import React from 'react';
import { NavLink } from 'react-router-dom';
import {
  Home,
  FileText,
  Database,
  Play,
  BarChart3,
  Users,
  Settings,
  X,
  HelpCircle,
  Store
} from 'lucide-react';

interface SidebarProps {
  isOpen: boolean;
  onClose: () => void;
}

const navigation = [
  { name: 'Dashboard', href: '/', icon: Home, helpId: 'nav-dashboard' },
  { name: 'Prompts', href: '/prompts', icon: FileText, helpId: 'nav-prompts' },
  { name: 'Documents', href: '/documents', icon: Database, helpId: 'nav-documents' },
  { name: 'Executions', href: '/executions', icon: Play, helpId: 'nav-executions' },
  { name: 'Marketplace', href: '/marketplace', icon: Store, helpId: 'nav-marketplace' },
  { name: 'Analytics', href: '/analytics', icon: BarChart3, helpId: 'nav-analytics' },
  { name: 'Workspaces', href: '/workspaces', icon: Users, helpId: 'nav-workspaces' },
  { name: 'Help', href: '/help', icon: HelpCircle, helpId: 'nav-help' },
  { name: 'Settings', href: '/settings', icon: Settings, helpId: 'nav-settings' },
];

export const Sidebar: React.FC<SidebarProps> = ({ isOpen, onClose }) => {
  return (
    <>
      {/* Mobile overlay */}
      {isOpen && (
        <div 
          className="fixed inset-0 bg-gray-600 bg-opacity-75 transition-opacity lg:hidden z-40"
          onClick={onClose}
        />
      )}

      {/* Sidebar */}
      <div className={`
        fixed inset-y-0 left-0 z-50 w-64 bg-white dark:bg-gray-800 shadow-lg transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0
        ${isOpen ? 'translate-x-0' : '-translate-x-full'}
      `}>
        <div className="flex items-center justify-between h-16 px-4 border-b border-gray-200 dark:border-gray-700 lg:hidden">
          <span className="text-lg font-semibold text-gray-900 dark:text-white">Menu</span>
          <button
            onClick={onClose}
            className="p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 dark:hover:bg-gray-700"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        <nav className="mt-5 px-2 space-y-1" role="navigation" aria-label="Main navigation">
          {navigation.map((item) => (
            <NavLink
              key={item.name}
              to={item.href}
              onClick={() => window.innerWidth < 1024 && onClose()}
              data-help={item.helpId}
              className={({ isActive }) =>
                `group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors ${
                  isActive
                    ? 'bg-blue-100 dark:bg-blue-900 text-blue-900 dark:text-blue-100'
                    : 'text-gray-600 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-white'
                }`
              }
              aria-current={({ isActive }) => isActive ? 'page' : undefined}
            >
              <item.icon
                className="mr-3 h-5 w-5 flex-shrink-0"
                aria-hidden="true"
              />
              {item.name}
            </NavLink>
          ))}
        </nav>

        {/* Bottom section */}
        <div className="absolute bottom-0 left-0 right-0 p-4 border-t border-gray-200 dark:border-gray-700">
          <div className="text-xs text-gray-500 dark:text-gray-400">
            <p>Version 1.0.0</p>
            <p className="mt-1">© 2025 PromptLibrary</p>
          </div>
        </div>
      </div>
    </>
  );
};
