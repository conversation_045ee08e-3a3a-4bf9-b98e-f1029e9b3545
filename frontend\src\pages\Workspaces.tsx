import React, { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useWorkspace } from '@/contexts/WorkspaceContext';
import { useWorkspaceOperations } from '@/contexts/WorkspaceOperationsContext';
import { useWorkspacePermissions } from '@/contexts/WorkspacePermissionsContext';
import type { LoadingSpinner } from '@/components/common/LoadingSpinner';
import type { PlusIcon, UserGroupIcon, CogIcon, ShareIcon, LockClosedIcon, GlobeAltIcon, ExclamationTriangleIcon, BuildingOfficeIcon, ChartBarIcon, BookOpenIcon, EnvelopeIcon } from '@heroicons/react/24/outline';
import type { CreateWorkspaceModal } from '@/components/features/workspaces/CreateWorkspaceModal';
import type { WorkspaceSettings  } from '@/components/features/workspaces/WorkspaceSettings';
import type { WorkspaceAnalytics } from '@/components/features/workspaces/WorkspaceAnalytics';
import SharedPromptLibrary from '@/components/features/workspaces/SharedPromptLibrary';
import InvitationFlow from '@/components/features/workspaces/InvitationFlow';

interface Workspace {
  id: string;
  name: string;
  description: string;
  memberCount: number;
  role: 'owner' | 'admin' | 'editor' | 'viewer';
  isPublic: boolean;
  createdAt: string;
  lastActivity: string;
}

export const Workspaces: React.FC = () => {
  const { user } = useAuth();
  const {
    workspaces,
    loading,
    error,
    selectWorkspace,
    clearError
  } = useWorkspace();
  const { createWorkspace } = useWorkspaceOperations();
  const { canUserManage } = useWorkspacePermissions();
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [selectedId, setSelectedId] = useState<string | null>(null);
  const [activeView, setActiveView] = useState<'overview' | 'settings' | 'analytics' | 'library' | 'invitations'>('overview');
  const [showSettings, setShowSettings] = useState(false);


  // TODO: Add try-catch error handling to this async handler
  const handleClick = async (workspaceId: string) => {
    try {
      await selectWorkspace(workspaceId);
      setSelectedId(workspaceId);
      setActiveView('overview');
    } catch (error) {
      console.error('Failed to select workspace:', error);
    }
  };

  const handleShowSettings = (workspaceId: string) => {
    setSelectedId(workspaceId);
    setShowSettings(true);
  };

  const handleCloseSettings = () => {
    setShowSettings(false);
    setSelectedId(null);
  };

  const handleViewChange = (view: 'overview' | 'settings' | 'analytics' | 'library' | 'invitations') => {
    setActiveView(view);
  };

  const handleShowCreateModal = () => {
    setShowCreateModal(true);
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'owner': return 'bg-purple-100 text-purple-800';
      case 'admin': return 'bg-blue-100 text-blue-800';
      case 'editor': return 'bg-green-100 text-green-800';
      case 'viewer': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="md:flex md:items-center md:justify-between">
        <div className="flex-1 min-w-0">
          <h2 className="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
            {selectedId && activeView !== 'overview'
              ? `${workspaces.find(w => w.id === selectedId)?.name || ''}`
              : 'Workspaces'
            }
          </h2>
          <p className="mt-1 text-sm text-gray-500">
            {selectedId && activeView !== 'overview'
              ? 'Manage your workspace and collaborate with your team'
              : 'Collaborate with your team on prompts and documents'
            }
          </p>
        </div>
        <div className="mt-4 flex space-x-3 md:mt-0 md:ml-4">
          {selectedId && activeView !== 'overview' && (
            <div className="flex space-x-2">
              <button
                onClick={() => handleViewChange('library')}
                className={`inline-flex items-center px-3 py-2 border text-sm font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 ${
                  activeView === 'library'
                    ? 'border-transparent text-white bg-blue-600 hover:bg-blue-700'
                    : 'border-gray-300 text-gray-700 bg-white hover:bg-gray-50'
                }`}
              >
                <BookOpenIcon className="h-4 w-4 mr-2" />
                Library
              </button>
              <button
                onClick={() => handleViewChange('analytics')}
                className={`inline-flex items-center px-3 py-2 border text-sm font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 ${
                  activeView === 'analytics'
                    ? 'border-transparent text-white bg-blue-600 hover:bg-blue-700'
                    : 'border-gray-300 text-gray-700 bg-white hover:bg-gray-50'
                }`}
              >
                <ChartBarIcon className="h-4 w-4 mr-2" />
                Analytics
              </button>
              <button
                onClick={() => handleViewChange('invitations')}
                className={`inline-flex items-center px-3 py-2 border text-sm font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 ${
                  activeView === 'invitations'
                    ? 'border-transparent text-white bg-blue-600 hover:bg-blue-700'
                    : 'border-gray-300 text-gray-700 bg-white hover:bg-gray-50'
                }`}
              >
                <EnvelopeIcon className="h-4 w-4 mr-2" />
                Invitations
              </button>
            </div>
          )}
          <button
            onClick={handleShowCreateModal}
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            <PlusIcon className="-ml-1 mr-2 h-5 w-5" />
            Create</button>
        </div>
      </div>

      {/* Workspaces Grid */}
      {workspaces.length > 0 ? (
        <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
          {workspaces.map((workspace) => (
            <div
              key={workspace.id}
              className="bg-white overflow-hidden shadow rounded-lg hover:shadow-md transition-shadow cursor-pointer"
            >
              <div className="p-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <BuildingOfficeIcon className="h-8 w-8 text-gray-400" />
                    <div className="ml-3">
                      <h3 className="text-lg font-medium text-gray-900">
                        {workspace.name}
                      </h3>
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getRoleColor(workspace.role)}`}>
                        {workspace.role}
                      </span>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    {workspace.isPublic ? (
                      <GlobeAltIcon className="h-5 w-5 text-green-500" title="Public workspace" />
                    ) : (
                      <LockClosedIcon className="h-5 w-5 text-gray-400" title="Private workspace" />
                    )}
                    {canUserManage(workspace) && (
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleShowSettings(workspace.id);
                        }}
                        className="p-1 text-gray-400 hover:text-gray-600"
                        title="Settings"
                      >
                        <CogIcon className="h-5 w-5" />
                      </button>
                    )}
                  </div>
                </div>
                
                <p className="mt-3 text-sm text-gray-500">
                  {workspace.description}
                </p>
                
                <div className="mt-4 flex items-center justify-between text-sm text-gray-500">
                  <div className="flex items-center">
                    <UserGroupIcon className="h-4 w-4 mr-1" />
                    {workspace.memberCount} member{workspace.memberCount !== 1 ? 's' : ''}
                  </div>
                  <div>
                    Last activity: {new Date(workspace.lastActivity).toLocaleDateString()}
                  </div>
                </div>
                
                <div className="mt-4 flex space-x-2">
                  <button
                    onClick={() => handleClick(workspace.id)}
                    className="flex-1 bg-indigo-600 text-white px-3 py-2 rounded-md text-sm font-medium hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                  >
                    Open</button>
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      // Handle share workspace
                      console.log('Share workspace:', workspace.id);
                    }}
                    className="px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                    title="Share"
                  >
                    <ShareIcon className="h-4 w-4" />
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <BuildingOfficeIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No workspaces</h3>
          <p className="mt-1 text-sm text-gray-500">
            Get started by creating your first workspace.
          </p>
          <div className="mt-6">
            <button
              onClick={handleShowCreateModal}
              className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700"
            >
              <PlusIcon className="-ml-1 mr-2 h-5 w-5" />
              Create</button>
          </div>
        </div>
      )}

      {/*Detail View */}
      {selectedId && activeView !== 'overview' && (
        <div className="bg-white shadow rounded-lg">
          {/* Navigation Tabs */}
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8 px-6">
              {[
                { id: 'library', name: 'Prompt Library', icon: BookOpenIcon },
                { id: 'analytics', name: 'Analytics', icon: ChartBarIcon },
                { id: 'invitations', name: 'Invitations', icon: EnvelopeIcon }
              ].map((tab) => {
                const Icon = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => handleViewChange(tab.id as any)}
                    className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${
                      activeView === tab.id
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    <Icon className="h-4 w-4" />
                    <span>{tab.name}</span>
                  </button>
                );
              })}
            </nav>
          </div>

          {/* Tab Content */}
          <div className="p-6">
            {activeView === 'library' && (
              <SharedPromptLibrary workspaceId={selectedId} />
            )}
            {activeView === 'analytics' && (
              <WorkspaceAnalytics workspaceId={selectedId} />
            )}
            {activeView === 'invitations' && (
              <InvitationFlow onComplete={() => setActiveView('overview')} />
            )}
          </div>

          {/* Back to Overview */}
          <div className="px-6 py-4 bg-gray-50 border-t border-gray-200">
            <button
              onClick={() => {
                setActiveView('overview');
                setSelectedId(null);
              }}
              className="text-sm text-gray-600 hover:text-gray-900"
            >
              ← Back to Workspaces
            </button>
          </div>
        </div>
      )}

      {/* Feature Preview */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
        <div className="flex">
          <div className="flex-shrink-0">
            <UserGroupIcon className="h-6 w-6 text-blue-600" />
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-blue-800">
              Team Collaboration Features
            </h3>
            <div className="mt-2 text-sm text-blue-700">
              <ul className="list-disc list-inside space-y-1">
                <li>Real-time collaborative editing</li>
                <li>Role-based permissions (Owner, Admin, Editor, Viewer)</li>
                <li>Shared prompt libraries and document collections</li>
                <li>Team activity feeds and notifications</li>
                <li>Workspace-level analytics and insights</li>
                <li>Integration with popular team tools</li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      {/* Beta Notice */}
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <div className="flex">
          <div className="flex-shrink-0">
            <ExclamationTriangleIcon className="h-5 w-5 text-yellow-400" />
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-yellow-800">
              Beta Feature
            </h3>
            <div className="mt-2 text-sm text-yellow-700">
              <p>Workspaces are currently in beta. Full collaboration features will be available soon.
                Your feedback helps us improve the experience.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* CreateWorkspaceModal */}
      <CreateWorkspaceModal
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        onCreated={(workspace) => {
          setShowCreateModal(false);
          // Optionally select the new workspace
          handleClick(workspace.id);
        }}
      />

      {/* Settings Modal */}
      {showSettings && selectedId && (
        <WorkspaceSettings
          workspaceId={selectedId}
          onClose={handleCloseSettings}
        />
      )}
    </div>
  );
};
