import React, { useEffect, useState } from 'react';
import { productionErrorTracker } from '@/utils/productionErrorTracking';
import type { ErrorAlert } from '@/utils/productionErrorTracking';
import { errorHandler } from '@/utils/errorHandler';

interface ProductionErrorMonitorProps {
  /** Whether to show development dashboard */
  showDashboard?: boolean;
  /** User ID for tracking */
  userId?: string;
  /** Custom error handler for critical alerts */
  onCriticalAlert?: (alert: ErrorAlert) => void;
}

/**
 * Production error monitoring component
 * Runs in background to track errors and send alerts
 * Shows development dashboard in non-production environments
 */
export const ProductionErrorMonitor: React.FC<ProductionErrorMonitorProps> = ({
  showDashboard = process.env.NODE_ENV === 'development',
  userId,
  onCriticalAlert
}) => {
  const [criticalAlerts, setCriticalAlerts] = useState<ErrorAlert[]>([]);
  const [isInitialized, setIsInitialized] = useState(false);

  useEffect(() => {
    // Set user ID if provided
    if (userId) {
      productionErrorTracker.setUserId(userId);
    }

    // Subscribe to alerts
    const unsubscribe = productionErrorTracker.onAlert((alert) => {
      // Handle critical alerts
      if (alert.severity === 'critical') {
        setCriticalAlerts(prev => [alert, ...prev.slice(0, 4)]); // Keep last 5
        
        // Call custom handler if provided
        if (onCriticalAlert) {
          onCriticalAlert(alert);
        }

        // Show browser notification for critical alerts (if permission granted)
        if ('Notification' in window && Notification.permission === 'granted') {
          new Notification('Critical Error Detected', {
            body: `${alert.error.name}: ${alert.error.message}`,
            icon: '/favicon.ico',
            tag: 'critical-error',
            requireInteraction: true
          });
        }
      }
    });

    // Set up global error handlers if not already done
    if (!isInitialized) {
      setupGlobalErrorHandlers();
      setIsInitialized(true);
    }

    return unsubscribe;
  }, [userId, onCriticalAlert, isInitialized]);

  // Request notification permission on mount
  useEffect(() => {
    if ('Notification' in window && Notification.permission === 'default') {
      Notification.requestPermission();
    }
  }, []);

  /**
   * Set up global error handlers to catch unhandled errors
   */
  const setupGlobalErrorHandlers = () => {
    // Global JavaScript errors
    const handleGlobalError = (event: ErrorEvent) => {
      errorHandler.handleError(event.error || new Error(event.message), {
        context: {
          component: 'Global',
          action: 'unhandled_error',
          metadata: {
            filename: event.filename,
            lineno: event.lineno,
            colno: event.colno,
            source: 'window.onerror'
          }
        },
        showToast: false, // Don't show toast for global errors
        reportToService: true
      });
    };

    // Unhandled promise rejections
    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      const error = event.reason instanceof Error ? event.reason : new Error(String(event.reason));
      
      errorHandler.handleError(error, {
        context: {
          component: 'Global',
          action: 'unhandled_promise_rejection',
          metadata: {
            reason: String(event.reason),
            source: 'window.onunhandledrejection'
          }
        },
        showToast: false, // Don't show toast for promise rejections
        reportToService: true
      });
    };

    // Resource loading errors
    const handleResourceError = (event: Event) => {
      const target = event.target as HTMLElement;
      const tagName = target?.tagName?.toLowerCase();
      const src = (target as any)?.src || (target as any)?.href;
      
      if (tagName && src) {
        errorHandler.handleError(new Error(`Failed to load ${tagName}: ${src}`), {
          context: {
            component: 'Global',
            action: 'resource_load_error',
            metadata: {
              tagName,
              src,
              source: 'resource_error'
            }
          },
          showToast: false,
          reportToService: true
        });
      }
    };

    // Add event listeners
    window.addEventListener('error', handleGlobalError);
    window.addEventListener('unhandledrejection', handleUnhandledRejection);
    
    // Capture resource loading errors
    window.addEventListener('error', handleResourceError, true);

    // Performance monitoring - long tasks
    if ('PerformanceObserver' in window) {
      try {
        const observer = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            if (entry.duration > 100) { // Tasks longer than 100ms
              errorHandler.handleError(new Error('Long task detected'), {
                context: {
                  component: 'Performance',
                  action: 'long_task',
                  metadata: {
                    duration: entry.duration,
                    startTime: entry.startTime,
                    name: entry.name
                  }
                },
                showToast: false,
                reportToService: process.env.NODE_ENV === 'production'
              });
            }
          }
        });

        observer.observe({ entryTypes: ['longtask'] });
      } catch (e) {
        // PerformanceObserver not supported or failed to initialize
        console.debug('PerformanceObserver for long tasks not available:', e);
      }
    }

    // Memory pressure monitoring (if available)
    if ('memory' in performance) {
      const checkMemoryPressure = () => {
        const memory = (performance as any).memory;
        const usedRatio = memory.usedJSHeapSize / memory.jsHeapSizeLimit;
        
        if (usedRatio > 0.9) { // 90% memory usage
          errorHandler.handleError(new Error('High memory usage detected'), {
            context: {
              component: 'Performance',
              action: 'memory_pressure',
              metadata: {
                usedJSHeapSize: memory.usedJSHeapSize,
                totalJSHeapSize: memory.totalJSHeapSize,
                jsHeapSizeLimit: memory.jsHeapSizeLimit,
                usedRatio: usedRatio
              }
            },
            showToast: false,
            reportToService: true
          });
        }
      };

      // Check memory every 30 seconds
      setInterval(checkMemoryPressure, 30000);
    }
  };

  // Development dashboard
  if (showDashboard && process.env.NODE_ENV === 'development') {
    return (
      <div className="fixed bottom-4 right-4 z-50">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-4 max-w-sm">
          <div className="flex items-center justify-between mb-3">
            <h3 className="font-semibold text-sm text-gray-900 dark:text-white">Error Monitor</h3>
            <div className={`w-2 h-2 rounded-full ${
              criticalAlerts.length > 0 ? 'bg-red-500' : 'bg-green-500'
            }`} />
          </div>
          
          {criticalAlerts.length > 0 ? (
            <div className="space-y-2">
              <div className="text-xs text-red-600 font-medium">
                🚨 {criticalAlerts.length} Critical Alert{criticalAlerts.length > 1 ? 's' : ''}
              </div>
              {criticalAlerts.slice(0, 2).map((alert) => (
                <div key={alert.id} className="text-xs p-2 bg-red-50 border border-red-200 rounded">
                  <div className="font-medium text-red-800">{alert.error.name}</div>
                  <div className="text-red-600 truncate">{alert.error.message}</div>
                  <div className="text-red-500 mt-1">
                    {new Date(alert.timestamp).toLocaleTimeString()}
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-xs text-green-600">
              ✅ No critical errors detected
            </div>
          )}
          
          <div className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-700">
            <div className="text-xs text-gray-500">
              Monitoring: {isInitialized ? 'Active' : 'Initializing...'}
            </div>
            {userId && (
              <div className="text-xs text-gray-500">
                User: {userId.slice(0, 8)}...
              </div>
            )}
          </div>
        </div>
      </div>
    );
  }

  // Production mode - no visible UI
  return null;
};

export default ProductionErrorMonitor;
