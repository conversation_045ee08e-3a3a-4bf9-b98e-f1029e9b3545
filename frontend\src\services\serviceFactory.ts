/**
 * Service Factory and Configuration
 * Provides centralized service instantiation and dependency injection
 */

import { 
  BaseService, 
  ServiceConfig, 
  ServiceDependencies,
  CacheService,
  LoggerService,
  MetricsService,
  AuthService
} from './baseService';

// Default implementations of service dependencies
class DefaultCacheService implements CacheService {
  private cache = new Map<string, { value: any; expires: number }>();

  async get<T>(key: string): Promise<T | null> {
    const entry = this.cache.get(key);
    if (!entry) return null;
    
    if (Date.now() > entry.expires) {
      this.cache.delete(key);
      return null;
    }
    
    return entry.value;
  }

  async set<T>(key: string, value: T, ttl: number = 300000): Promise<void> {
    this.cache.set(key, {
      value,
      expires: Date.now() + ttl
    });
  }

  async delete(key: string): Promise<void> {
    this.cache.delete(key);
  }

  async clear(): Promise<void> {
    this.cache.clear();
  }

  async has(key: string): Promise<boolean> {
    const entry = this.cache.get(key);
    if (!entry) return false;
    
    if (Date.now() > entry.expires) {
      this.cache.delete(key);
      return false;
    }
    
    return true;
  }
}

class DefaultLoggerService implements LoggerService {
  debug(message: string, context?: any): void {
    if (process.env.NODE_ENV === 'development') {
      console.debug(`[DEBUG] ${message}`, context);
    }
  }

  info(message: string, context?: any): void {
    console.info(`[INFO] ${message}`, context);
  }

  warn(message: string, context?: any): void {
    console.warn(`[WARN] ${message}`, context);
  }

  error(message: string, error?: Error, context?: any): void {
    console.error(`[ERROR] ${message}`, { error, context });
  }
}

class DefaultMetricsService implements MetricsService {
  private metrics = new Map<string, any>();

  recordOperation(operation: string, duration: number, success: boolean): void {
    const key = `operation:${operation}`;
    const existing = this.metrics.get(key) || { count: 0, totalDuration: 0, successCount: 0 };
    
    this.metrics.set(key, {
      count: existing.count + 1,
      totalDuration: existing.totalDuration + duration,
      successCount: existing.successCount + (success ? 1 : 0),
      lastExecuted: new Date()
    });
  }

  recordError(operation: string, error: any): void {
    const key = `error:${operation}`;
    const existing = this.metrics.get(key) || { count: 0, errors: [] };
    
    this.metrics.set(key, {
      count: existing.count + 1,
      errors: [...existing.errors.slice(-9), { // Keep last 10 errors
        message: error.message,
        code: error.code,
        timestamp: new Date()
      }]
    });
  }

  recordCacheHit(operation: string): void {
    const key = `cache:${operation}`;
    const existing = this.metrics.get(key) || { hits: 0, misses: 0 };
    
    this.metrics.set(key, {
      ...existing,
      hits: existing.hits + 1
    });
  }

  recordCacheMiss(operation: string): void {
    const key = `cache:${operation}`;
    const existing = this.metrics.get(key) || { hits: 0, misses: 0 };
    
    this.metrics.set(key, {
      ...existing,
      misses: existing.misses + 1
    });
  }

  getMetrics(): Map<string, any> {
    return new Map(this.metrics);
  }
}

class DefaultAuthService implements AuthService {
  async getCurrentUser(): Promise<any> {
    // This would integrate with your actual auth system
    // For now, return null or implement based on your auth context
    return null;
  }

  async getAuthToken(): Promise<string | null> {
    // This would integrate with your actual auth system
    return null;
  }

  isAuthenticated(): boolean {
    // This would integrate with your actual auth system
    return false;
  }
}

// Service factory configuration
export interface ServiceFactoryConfig {
  cache?: CacheService;
  logger?: LoggerService;
  metrics?: MetricsService;
  auth?: AuthService;
  defaultServiceConfig?: ServiceConfig;
}

// Service factory class
export class ServiceFactory {
  private static instance: ServiceFactory;
  private dependencies: ServiceDependencies;
  private defaultConfig: ServiceConfig;

  private constructor(config: ServiceFactoryConfig = {}) {
    this.dependencies = {
      cache: config.cache || new DefaultCacheService(),
      logger: config.logger || new DefaultLoggerService(),
      metrics: config.metrics || new DefaultMetricsService(),
      auth: config.auth || new DefaultAuthService()
    };

    this.defaultConfig = {
      enableCaching: true,
      defaultTTL: 300000, // 5 minutes
      enableRetry: true,
      maxRetries: 3,
      enableMetrics: true,
      enableLogging: process.env.NODE_ENV === 'development',
      ...config.defaultServiceConfig
    };
  }

  public static getInstance(config?: ServiceFactoryConfig): ServiceFactory {
    if (!ServiceFactory.instance) {
      ServiceFactory.instance = new ServiceFactory(config);
    }
    return ServiceFactory.instance;
  }

  public static configure(config: ServiceFactoryConfig): void {
    ServiceFactory.instance = new ServiceFactory(config);
  }

  public createService<T extends BaseService>(
    ServiceClass: new (serviceName: string, config: ServiceConfig, dependencies: ServiceDependencies) => T,
    serviceName: string,
    config?: Partial<ServiceConfig>
  ): T {
    const mergedConfig = { ...this.defaultConfig, ...config };
    return new ServiceClass(serviceName, mergedConfig, this.dependencies);
  }

  public getDependencies(): ServiceDependencies {
    return this.dependencies;
  }

  public getDefaultConfig(): ServiceConfig {
    return this.defaultConfig;
  }

  public updateDependency<K extends keyof ServiceDependencies>(
    key: K,
    service: ServiceDependencies[K]
  ): void {
    this.dependencies[key] = service;
  }

  // Convenience methods for getting individual services
  public getCacheService(): CacheService | undefined {
    return this.dependencies.cache;
  }

  public getLoggerService(): LoggerService | undefined {
    return this.dependencies.logger;
  }

  public getMetricsService(): MetricsService | undefined {
    return this.dependencies.metrics;
  }

  public getAuthService(): AuthService | undefined {
    return this.dependencies.auth;
  }
}

// Global service factory instance
export const serviceFactory = ServiceFactory.getInstance();

// Utility functions for service creation
export function createService<T extends BaseService>(
  ServiceClass: new (serviceName: string, config: ServiceConfig, dependencies: ServiceDependencies) => T,
  serviceName: string,
  config?: Partial<ServiceConfig>
): T {
  return serviceFactory.createService(ServiceClass, serviceName, config);
}

// Service configuration presets
export const ServicePresets = {
  // High performance preset with aggressive caching
  highPerformance: {
    enableCaching: true,
    defaultTTL: 600000, // 10 minutes
    enableRetry: true,
    maxRetries: 5,
    enableMetrics: true,
    enableLogging: false
  } as ServiceConfig,

  // Development preset with detailed logging
  development: {
    enableCaching: true,
    defaultTTL: 60000, // 1 minute for faster development
    enableRetry: true,
    maxRetries: 2,
    enableMetrics: true,
    enableLogging: true
  } as ServiceConfig,

  // Production preset with balanced settings
  production: {
    enableCaching: true,
    defaultTTL: 300000, // 5 minutes
    enableRetry: true,
    maxRetries: 3,
    enableMetrics: true,
    enableLogging: false
  } as ServiceConfig,

  // Minimal preset for simple operations
  minimal: {
    enableCaching: false,
    enableRetry: false,
    enableMetrics: false,
    enableLogging: false
  } as ServiceConfig
};

// Initialize service factory with environment-specific configuration
export function initializeServiceFactory(): void {
  const config: ServiceFactoryConfig = {
    defaultServiceConfig: process.env.NODE_ENV === 'development' 
      ? ServicePresets.development 
      : ServicePresets.production
  };

  ServiceFactory.configure(config);
}
