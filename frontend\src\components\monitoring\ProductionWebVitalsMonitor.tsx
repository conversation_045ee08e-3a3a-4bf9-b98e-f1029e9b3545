import React, { useEffect, useState, useCallback } from 'react';
import { webVitalsTracker } from '@/utils/webVitals';
import type { WebVitalMetric } from '@/utils/webVitals';

interface ProductionWebVitalsMonitorProps {
  enabled?: boolean;
  reportingEndpoint?: string;
  alertThresholds?: {
    LCP: number;
    FID: number;
    CLS: number;
    FCP: number;
    TTFB: number;
  };
  onAlert?: (metric: WebVitalMetric) => void;
}

interface AlertData {
  metric: WebVitalMetric;
  severity: 'warning' | 'critical';
  timestamp: number;
  acknowledged: boolean;
}

/**
 * Production-ready Web Vitals monitoring component
 * Provides real-time monitoring, alerting, and reporting for Core Web Vitals
 */
export const ProductionWebVitalsMonitor: React.FC<ProductionWebVitalsMonitorProps> = ({
  enabled = process.env.NODE_ENV === 'production',
  reportingEndpoint = process.env.VITE_PERFORMANCE_ENDPOINT,
  alertThresholds = {
    LCP: 2500, // 2.5s
    FID: 100,  // 100ms
    CLS: 0.1,  // 0.1
    FCP: 1800, // 1.8s
    TTFB: 600  // 600ms
  },
  onAlert
}) => {
  const [alerts, setAlerts] = useState<AlertData[]>([]);
  const [metrics, setMetrics] = useState<WebVitalMetric[]>([]);
  const [isReporting, setIsReporting] = useState(false);

  // Handle new metrics and check for alerts
  const handleMetric = useCallback((metric: WebVitalMetric) => {
    setMetrics(prev => [...prev, metric].slice(-100)); // Keep last 100 metrics

    // Check alert thresholds
    const threshold = alertThresholds[metric.name as keyof typeof alertThresholds];
    if (threshold && metric.value > threshold) {
      const severity = metric.rating === 'poor' ? 'critical' : 'warning';
      const alert: AlertData = {
        metric,
        severity,
        timestamp: Date.now(),
        acknowledged: false
      };

      setAlerts(prev => [...prev, alert].slice(-20)); // Keep last 20 alerts
      
      // Trigger callback
      onAlert?.(metric);

      // Log to console for immediate visibility
      console.warn(`🚨 Web Vitals Alert: ${metric.name} = ${metric.value.toFixed(2)} (threshold: ${threshold})`);
    }
  }, [alertThresholds, onAlert]);

  // Report metrics to external endpoint
  const reportMetrics = useCallback(async (metricsToReport: WebVitalMetric[]) => {
    if (!reportingEndpoint || metricsToReport.length === 0) return;

    setIsReporting(true);
    try {
      const response = await fetch(reportingEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          timestamp: Date.now(),
          url: window.location.href,
          userAgent: navigator.userAgent,
          metrics: metricsToReport.map(metric => ({
            name: metric.name,
            value: metric.value,
            rating: metric.rating,
            timestamp: metric.timestamp,
            id: metric.id,
            navigationType: metric.navigationType
          }))
        })
      });

      if (!response.ok) {
        throw new Error(`Reporting failed: ${response.status}`);
      }

      console.log(`📊 Reported ${metricsToReport.length} Web Vitals metrics`);
    } catch (error) {
      console.error('Failed to report Web Vitals metrics:', error);
    } finally {
      setIsReporting(false);
    }
  }, [reportingEndpoint]);

  // Batch reporting every 30 seconds
  useEffect(() => {
    if (!enabled) return;

    const interval = setInterval(() => {
      const unreportedMetrics = metrics.filter(m => !m.reported);
      if (unreportedMetrics.length > 0) {
        reportMetrics(unreportedMetrics);
        // Mark as reported
        setMetrics(prev => prev.map(m => ({ ...m, reported: true })));
      }
    }, 30000);

    return () => clearInterval(interval);
  }, [enabled, metrics, reportMetrics]);

  // Subscribe to Web Vitals tracker
  useEffect(() => {
    if (!enabled) return;

    const unsubscribe = webVitalsTracker.onMetric(handleMetric);
    return unsubscribe;
  }, [enabled, handleMetric]);

  // Send critical alerts immediately
  useEffect(() => {
    const criticalAlerts = alerts.filter(a => a.severity === 'critical' && !a.acknowledged);
    if (criticalAlerts.length > 0 && reportingEndpoint) {
      // Send immediate alert
      fetch(`${reportingEndpoint}/alerts`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          alerts: criticalAlerts.map(alert => ({
            metric: alert.metric.name,
            value: alert.metric.value,
            severity: alert.severity,
            timestamp: alert.timestamp,
            url: window.location.href
          }))
        })
      }).catch(error => console.error('Failed to send critical alert:', error));
    }
  }, [alerts, reportingEndpoint]);

  // Auto-acknowledge alerts after 5 minutes
  useEffect(() => {
    const timeout = setTimeout(() => {
      setAlerts(prev => prev.map(alert => ({ ...alert, acknowledged: true })));
    }, 5 * 60 * 1000);

    return () => clearTimeout(timeout);
  }, [alerts]);

  // Don't render anything in production (headless monitoring)
  if (enabled && process.env.NODE_ENV === 'production') {
    return null;
  }

  // Development dashboard
  if (process.env.NODE_ENV === 'development') {
    return (
      <div className="fixed bottom-4 right-4 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg p-4 max-w-sm z-50">
        <div className="flex items-center justify-between mb-3">
          <h3 className="font-semibold text-sm">Web Vitals Monitor</h3>
          <div className="flex items-center space-x-2">
            {isReporting && (
              <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse" title="Reporting metrics" />
            )}
            <div className={`w-2 h-2 rounded-full ${enabled ? 'bg-green-500' : 'bg-gray-400'}`} />
          </div>
        </div>

        {/* Recent Metrics */}
        <div className="space-y-2 mb-3">
          {metrics.slice(-3).map((metric, index) => (
            <div key={`${metric.name}-${index}`} className="flex justify-between text-xs">
              <span className="font-medium">{metric.name}</span>
              <span className={`${
                metric.rating === 'good' ? 'text-green-600' :
                metric.rating === 'needs-improvement' ? 'text-yellow-600' :
                'text-red-600'
              }`}>
                {metric.value.toFixed(2)}
              </span>
            </div>
          ))}
        </div>

        {/* Active Alerts */}
        {alerts.filter(a => !a.acknowledged).length > 0 && (
          <div className="border-t border-gray-200 dark:border-gray-700 pt-2">
            <div className="text-xs font-medium text-red-600 mb-1">
              Active Alerts ({alerts.filter(a => !a.acknowledged).length})
            </div>
            {alerts.filter(a => !a.acknowledged).slice(-2).map((alert, index) => (
              <div key={index} className="text-xs text-red-600 truncate">
                {alert.metric.name}: {alert.metric.value.toFixed(2)}
              </div>
            ))}
          </div>
        )}
      </div>
    );
  }

  return null;
};

// Export for use in App.tsx
export default ProductionWebVitalsMonitor;
