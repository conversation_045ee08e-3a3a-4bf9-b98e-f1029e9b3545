import React, { createContext, useContext, useState, useEffect, useMemo, useCallback } from 'react';
import type { ReactNode } from 'react';
import { useAuth } from './AuthContext';
import { workspaceService } from '@/services/workspaceService';
import type { Workspace } from '@/services/workspaceService';

// Simplified WorkspaceContext that only handles workspace state
interface WorkspaceContextType {
  workspaces: Workspace[];
  currentWorkspace: Workspace | null;
  loading: boolean;
  error: string | null;

  // State operations
  selectWorkspace: (workspaceId: string) => Promise<void>;
  refreshWorkspaces: () => Promise<void>;
  clearError: () => void;
}

const WorkspaceContext = createContext<WorkspaceContextType | undefined>(undefined);

export const useWorkspace = (): WorkspaceContextType => {
  const context = useContext(WorkspaceContext);
  if (!context) {
    throw new Error('useWorkspace must be used within a WorkspaceProvider');
  }
  return context;
};

interface WorkspaceProviderProps {
  children: ReactNode;
}

export const WorkspaceProvider: React.FC<WorkspaceProviderProps> = ({ children }) => {
  const { currentUser } = useAuth();
  const [workspaces, setWorkspaces] = useState<Workspace[]>([]);
  const [currentWorkspace, setCurrentWorkspace] = useState<Workspace | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Load user's workspaces
  const loadWorkspaces = useCallback(async () => {
    if (!currentUser) return;

    setLoading(true);
    setError(null);

    try {
      const userWorkspaces = await workspaceService.getUserWorkspaces(currentUser.uid);
      setWorkspaces(userWorkspaces);

      // Set default workspace if none selected
      if (!currentWorkspace && userWorkspaces.length > 0) {
        setCurrentWorkspace(userWorkspaces[0]);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load workspaces');
    } finally {
      setLoading(false);
    }
  }, [currentUser, currentWorkspace]);

  // Select workspace
  const selectWorkspace = useCallback(async (workspaceId: string): Promise<void> => {
    setLoading(true);
    setError(null);

    try {
      const workspace = await workspaceService.getWorkspace(workspaceId);
      if (!workspace) {
        throw new Error('Workspace not found');
      }
      setCurrentWorkspace(workspace);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to select workspace');
    } finally {
      setLoading(false);
    }
  }, []);

  const refreshWorkspaces = useCallback(async (): Promise<void> => {
    await loadWorkspaces();
  }, [loadWorkspaces]);

  const clearError = useCallback((): void => {
    setError(null);
  }, []);

  // Load workspaces when user changes
  useEffect(() => {
    if (currentUser) {
      loadWorkspaces();
    } else {
      setWorkspaces([]);
      setCurrentWorkspace(null);
    }
  }, [currentUser, loadWorkspaces]);

  // Memoize the context value to prevent unnecessary re-renders
  const contextValue = useMemo(() => ({
    workspaces,
    currentWorkspace,
    loading,
    error,
    selectWorkspace,
    refreshWorkspaces,
    clearError
  }), [workspaces, currentWorkspace, loading, error, selectWorkspace, refreshWorkspaces, clearError]);

  // Load workspaces when user changes
  useEffect(() => {
    if (currentUser) {
      loadWorkspaces();
    } else {
      setWorkspaces([]);
      setCurrentWorkspace(null);
    }
  }, [currentUser, loadWorkspaces]);

  return (
    <WorkspaceContext.Provider value={contextValue}>
      {children}
    </WorkspaceContext.Provider>
  );
};
