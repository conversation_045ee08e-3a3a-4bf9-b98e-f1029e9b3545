import React, { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import './index.css'
import App from './App.tsx'
// Lazy load monitoring to reduce initial bundle size
import { initializeLCPOptimization } from './utils/lcpOptimization';

// Register service worker for offline support and caching
if ('serviceWorker' in navigator && import.meta.env.PROD) {
  window.addEventListener('load', () => {
    navigator.serviceWorker.register('/sw.js')
      .then((registration) => {
        console.log('SW registered: ', registration);
      })
      .catch((registrationError) => {
        console.log('SW registration failed: ', registrationError);
      });
  });
}

// Initialize LCP optimization immediately for better performance
initializeLCPOptimization();

// Initialize monitoring system lazily
if (import.meta.env.PROD || import.meta.env.VITE_ENABLE_MONITORING === 'true') {
  import('./utils/monitoring').then(({ initializeMonitoring }) => {
    initializeMonitoring();
  }).catch(error => console.error('Promise rejection:', error));
}

createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <App />
  </StrictMode>,
)
