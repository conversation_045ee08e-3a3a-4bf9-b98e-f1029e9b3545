import React, { useState, useEffect } from 'react';
import type { ChartBarIcon, UserGroupIcon, DocumentTextIcon, ClockIcon, ArrowTrendingUpIcon, ArrowTrendingDownIcon, EyeIcon, PlayIcon } from '@heroicons/react/24/outline';
import { useWorkspace } from '@/contexts/WorkspaceContext';

interface WorkspaceAnalyticsProps {
  workspaceId: string;
}

interface AnalyticsData {
  overview: {
    totalPrompts: number;
    totalMembers: number;
    totalExecutions: number;
    totalViews: number;
    promptsThisMonth: number;
    executionsThisMonth: number;
    activeMembers: number;
  };
  trends: {
    promptsGrowth: number;
    executionsGrowth: number;
    membersGrowth: number;
  };
  topPrompts: Array<{
    id: string;
    title: string;
    executions: number;
    views: number;
    author: string;
  }>;
  memberActivity: Array<{
    userId: string;
    name: string;
    email: string;
    promptsCreated: number;
    executionsRun: number;
    lastActive: Date;
  }>;
  timeSeriesData: Array<{
    date: string;
    prompts: number;
    executions: number;
    views: number;
  }>;
}

export const WorkspaceAnalytics: React.FC<WorkspaceAnalyticsProps> = ({
  workspaceId
}) => {
  const { currentWorkspace, getUserRole } = useWorkspace();
  const [analytics, setAnalytics] = useState<AnalyticsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [timeRange, setTimeRange] = useState('30d');

  const userRole = getUserRole(workspaceId);
  const canViewAnalytics = ['owner', 'admin'].includes(userRole || '');

  useEffect(() => {
    if (workspaceId && canViewAnalytics) {
      loadAnalytics();
    }
  }, [workspaceId, timeRange, canViewAnalytics]);

  const loadAnalytics = async () => {
    setLoading(true);
    try {
      // Simulate API call - replace with actual analytics service
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Mock data - replace with real analytics data
      const mockData: AnalyticsData = {
        overview: {
          totalPrompts: 156,
          totalMembers: 8,
          totalExecutions: 2847,
          totalViews: 5632,
          promptsThisMonth: 23,
          executionsThisMonth: 456,
          activeMembers: 6
        },
        trends: {
          promptsGrowth: 15.2,
          executionsGrowth: 28.5,
          membersGrowth: 12.5
        },
        topPrompts: [
          {
            id: '1',
            title: 'Customer Support Response Generator',
            executions: 234,
            views: 567,
            author: 'John Doe'
          },
          {
            id: '2',
            title: 'Product Description Writer',
            executions: 189,
            views: 423,
            author: 'Jane Smith'
          },
          {
            id: '3',
            title: 'Email Subject Line Creator',
            executions: 156,
            views: 389,
            author: 'Mike Johnson'
          }
        ],
        memberActivity: [
          {
            userId: '1',
            name: 'John Doe',
            email: '<EMAIL>',
            promptsCreated: 23,
            executionsRun: 145,
            lastActive: new Date()
          },
          {
            userId: '2',
            name: 'Jane Smith',
            email: '<EMAIL>',
            promptsCreated: 18,
            executionsRun: 98,
            lastActive: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000)
          }
        ],
        timeSeriesData: Array.from({ length: 30 }, (_, i) => ({
          date: new Date(Date.now() - (29 - i) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          prompts: Math.floor(Math.random() * 10) + 1,
          executions: Math.floor(Math.random() * 50) + 10,
          views: Math.floor(Math.random() * 100) + 20
        }))
      };

      setAnalytics(mockData);
    } catch (error) {
      console.error('Failed to load analytics:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatNumber = (num: number) => {
    if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'k';
    }
    return num.toString();
  };

  const formatGrowth = (growth: number) => {
    const isPositive = growth >= 0;
    return (
      <span className={`inline-flex items-center ${isPositive ? 'text-green-600' : 'text-red-600'}`}>
        {isPositive ? (
          <ArrowTrendingUpIcon className="h-4 w-4 mr-1" />
        ) : (
          <ArrowTrendingDownIcon className="h-4 w-4 mr-1" />
        )}
        {Math.abs(growth).toFixed(1)}%
      </span>
    );
  };

  if (!canViewAnalytics) {
    return (
      <div className="text-center py-12">
        <ChartBarIcon className="mx-auto h-12 w-12 text-gray-400" />
        <h3 className="mt-2 text-sm font-medium text-gray-900">Access Restricted</h3>
        <p className="mt-1 text-sm text-gray-500">
          You need admin or owner permissions to view workspace analytics.
        </p>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-sm text-gray-500">Loading analytics...</p>
        </div>
      </div>
    );
  }

  if (!analytics) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-500">No analytics data available.</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Workspace Analytics</h2>
          <p className="text-sm text-gray-500">
            Insights and metrics for {currentWorkspace?.name}
          </p>
        </div>
        <div>
          <select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="7d">Last 7 days</option>
            <option value="30d">Last 30 days</option>
            <option value="90d">Last 90 days</option>
            <option value="1y">Last year</option>
          </select>
        </div>
      </div>

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <DocumentTextIcon className="h-6 w-6 text-gray-400" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    Total Prompts
                  </dt>
                  <dd className="flex items-baseline">
                    <div className="text-2xl font-semibold text-gray-900">
                      {analytics.overview.totalPrompts}
                    </div>
                    <div className="ml-2 flex items-baseline text-sm">
                      {formatGrowth(analytics.trends.promptsGrowth)}
                    </div>
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <UserGroupIcon className="h-6 w-6 text-gray-400" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    Team Members
                  </dt>
                  <dd className="flex items-baseline">
                    <div className="text-2xl font-semibold text-gray-900">
                      {analytics.overview.totalMembers}
                    </div>
                    <div className="ml-2 flex items-baseline text-sm">
                      {formatGrowth(analytics.trends.membersGrowth)}
                    </div>
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <PlayIcon className="h-6 w-6 text-gray-400" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    Total Executions
                  </dt>
                  <dd className="flex items-baseline">
                    <div className="text-2xl font-semibold text-gray-900">
                      {formatNumber(analytics.overview.totalExecutions)}
                    </div>
                    <div className="ml-2 flex items-baseline text-sm">
                      {formatGrowth(analytics.trends.executionsGrowth)}
                    </div>
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <EyeIcon className="h-6 w-6 text-gray-400" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    Total Views
                  </dt>
                  <dd className="flex items-baseline">
                    <div className="text-2xl font-semibold text-gray-900">
                      {formatNumber(analytics.overview.totalViews)}
                    </div>
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Top Prompts and Member Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Top Prompts */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">Top Performing Prompts</h3>
          </div>
          <div className="p-6">
            <div className="space-y-4">
              {analytics.topPrompts.map((prompt, index) => (
                <div key={prompt.id} className="flex items-center justify-between">
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-3">
                      <span className="flex-shrink-0 w-6 h-6 bg-blue-100 text-blue-800 text-xs font-medium rounded-full flex items-center justify-center">
                        {index + 1}
                      </span>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-900 truncate">
                          {prompt.title}
                        </p>
                        <p className="text-xs text-gray-500">
                          by {prompt.author}
                        </p>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-4 text-sm text-gray-500">
                    <div className="flex items-center">
                      <PlayIcon className="h-4 w-4 mr-1" />
                      {prompt.executions}
                    </div>
                    <div className="flex items-center">
                      <EyeIcon className="h-4 w-4 mr-1" />
                      {prompt.views}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Member Activity */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">Member Activity</h3>
          </div>
          <div className="p-6">
            <div className="space-y-4">
              {analytics.memberActivity.map((member) => (
                <div key={member.userId} className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="flex-shrink-0">
                      <div className="h-8 w-8 rounded-full bg-gray-300 flex items-center justify-center">
                        <span className="text-xs font-medium text-gray-700">
                          {member.name.charAt(0).toUpperCase()}
                        </span>
                      </div>
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900">
                        {member.name}
                      </p>
                      <p className="text-xs text-gray-500">
                        Last active: {member.lastActive.toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-4 text-sm text-gray-500">
                    <div className="text-center">
                      <div className="font-medium text-gray-900">{member.promptsCreated}</div>
                      <div className="text-xs">Prompts</div>
                    </div>
                    <div className="text-center">
                      <div className="font-medium text-gray-900">{member.executionsRun}</div>
                      <div className="text-xs">Executions</div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Activity Chart Placeholder */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">Activity Over Time</h3>
        </div>
        <div className="p-6">
          <div className="h-64 flex items-center justify-center bg-gray-50 rounded-lg">
            <div className="text-center">
              <ChartBarIcon className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">Chart Coming Soon</h3>
              <p className="mt-1 text-sm text-gray-500">
                Interactive charts will be available in the next update.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
        <div className="flex">
          <div className="flex-shrink-0">
            <ClockIcon className="h-6 w-6 text-blue-600" />
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-blue-800">
              This Month's Activity
            </h3>
            <div className="mt-2 text-sm text-blue-700">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <span className="font-medium">{analytics.overview.promptsThisMonth}</span> new prompts created
                </div>
                <div>
                  <span className="font-medium">{analytics.overview.executionsThisMonth}</span> prompts executed
                </div>
                <div>
                  <span className="font-medium">{analytics.overview.activeMembers}</span> active members
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default WorkspaceAnalytics;