# Firebase Configuration - Template
# Replace these with your actual Firebase project values
VITE_FIREBASE_API_KEY=your_firebase_api_key_here
VITE_FIREBASE_AUTH_DOMAIN=your-project.firebaseapp.com
VITE_FIREBASE_PROJECT_ID=your-project-id
VITE_FIREBASE_STORAGE_BUCKET=your-project.firebasestorage.app
VITE_FIREBASE_MESSAGING_SENDER_ID=123456789012
VITE_FIREBASE_APP_ID=1:123456789012:web:abcdef123456
VITE_FIREBASE_MEASUREMENT_ID=G-ABCDEF1234

# Development Environment
# Set to true to use Firebase emulators for local development
VITE_USE_EMULATORS=false

# API Configuration
# OpenRouter API for LLM integration
VITE_OPENROUTER_API_KEY=your_openrouter_api_key_here

# Application Configuration
VITE_APP_NAME=RAG Prompt Library
VITE_APP_VERSION=1.0.0
VITE_APP_ENVIRONMENT=production

# Feature Flags
VITE_ENABLE_ANALYTICS=true
VITE_ENABLE_ERROR_REPORTING=true
VITE_ENABLE_PERFORMANCE_MONITORING=true
VITE_ENABLE_WEB_VITALS=true
VITE_ENABLE_WEB_VITALS_ALERTS=true

# Performance Monitoring Endpoints
VITE_PERFORMANCE_ENDPOINT=https://your-monitoring-endpoint.com/api/metrics
VITE_WEBHOOK_URL=https://your-webhook-endpoint.com/alerts
VITE_EMAIL_ENDPOINT=https://your-email-service.com/send
VITE_SLACK_WEBHOOK=https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK
VITE_ALERT_EMAIL=<EMAIL>

# Error Tracking and Alerting
VITE_ENABLE_ERROR_ALERTS=true
VITE_ERROR_WEBHOOK_URL=https://your-error-webhook.com/alerts
VITE_ERROR_EMAIL_ENDPOINT=https://your-email-service.com/error-alerts
VITE_ERROR_SLACK_WEBHOOK=https://hooks.slack.com/services/YOUR/ERROR/WEBHOOK

# Debug Settings
VITE_DEBUG_MODE=false
VITE_LOG_LEVEL=info
