import React, { useState, useEffect } from 'react';
import {
  EnvelopeIcon,
  CheckCircleIcon,
  XCircleIcon,
  ClockIcon,
  UserPlusIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon
} from '@heroicons/react/24/outline';
import { useAuth } from '@/contexts/AuthContext';
import { useWorkspace } from '@/contexts/WorkspaceContext';

interface Invitation {
  id: string;
  workspaceId: string;
  workspaceName: string;
  inviterName: string;
  inviterEmail: string;
  role: string;
  message?: string;
  status: 'pending' | 'accepted' | 'declined' | 'expired';
  createdAt: Date;
  expiresAt: Date;
}

interface InvitationFlowProps {
  invitationId?: string;
  onComplete?: () => void;
}

export const InvitationFlow: React.FC<InvitationFlowProps> = ({
  invitationId,
  onComplete
}) => {
  const { user } = useAuth();
  const { workspaces, refreshWorkspaces } = useWorkspace();
  const [invitation, setInvitation] = useState<Invitation | null>(null);
  const [loading, setLoading] = useState(true);
  const [processing, setProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (invitationId) {
      loadInvitation();
    } else {
      loadPendingInvitations();
    }
  }, [invitationId]);

  const loadInvitation = async () => {
    setLoading(true);
    setError(null);
    try {
      // Simulate API call to load specific invitation
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Mock invitation data
      const mockInvitation: Invitation = {
        id: invitationId || '1',
        workspaceId: 'workspace-1',
        workspaceName: 'Acme Corp Marketing Team',
        inviterName: 'John Doe',
        inviterEmail: '<EMAIL>',
        role: 'editor',
        message: 'Join our marketing team workspace to collaborate on prompt templates and campaigns!',
        status: 'pending',
        createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
        expiresAt: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000)
      };

      setInvitation(mockInvitation);
    } catch (error) {
      console.error('Failed to load invitation:', error);
      setError('Failed to load invitation details.');
    } finally {
      setLoading(false);
    }
  };

  const loadPendingInvitations = async () => {
    setLoading(true);
    setError(null);
    try {
      // Simulate API call to load user's pending invitations
      await new Promise(resolve => setTimeout(resolve, 1000));

      // For demo, show first pending invitation if any
      const mockInvitations: Invitation[] = [
        {
          id: '1',
          workspaceId: 'workspace-1',
          workspaceName: 'Acme Corp Marketing Team',
          inviterName: 'John Doe',
          inviterEmail: '<EMAIL>',
          role: 'editor',
          message: 'Join our marketing team workspace!',
          status: 'pending',
          createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
          expiresAt: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000)
        }
      ];

      if (mockInvitations.length > 0) {
        setInvitation(mockInvitations[0]);
      }
    } catch (error) {
      console.error('Failed to load invitations:', error);
      setError('Failed to load invitations.');
    } finally {
      setLoading(false);
    }
  };

  // TODO: Add try-catch error handling to this async handler
  const handleAcceptInvitation = async () => {
    if (!invitation) return;

    setProcessing(true);
    setError(null);
    try {
      // Simulate API call to accept invitation
      await new Promise(resolve => setTimeout(resolve, 1500));

      setInvitation(prev => prev ? { ...prev, status: 'accepted' } : null);

      // Refresh workspaces to include the new one
      await refreshWorkspaces();

      if (onComplete) {
        onComplete();
      }
    } catch (error) {
      console.error('Failed to accept invitation:', error);
      setError('Failed to accept invitation. Please try again.');
    } finally {
      setProcessing(false);
    }
  };

  // TODO: Add try-catch error handling to this async handler
  const handleDeclineInvitation = async () => {
    if (!invitation) return;

    setProcessing(true);
    setError(null);
    try {
      // Simulate API call to decline invitation
      await new Promise(resolve => setTimeout(resolve, 1000));

      setInvitation(prev => prev ? { ...prev, status: 'declined' } : null);

      if (onComplete) {
        onComplete();
      }
    } catch (error) {
      console.error('Failed to decline invitation:', error);
      setError('Failed to decline invitation. Please try again.');
    } finally {
      setProcessing(false);
    }
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'owner': return 'bg-purple-100 text-purple-800';
      case 'admin': return 'bg-blue-100 text-blue-800';
      case 'editor': return 'bg-green-100 text-green-800';
      case 'viewer': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getRoleDescription = (role: string) => {
    switch (role) {
      case 'owner': return 'Full access to workspace settings and all content';
      case 'admin': return 'Manage members and workspace settings';
      case 'editor': return 'Create, edit, and share prompts and documents';
      case 'viewer': return 'View and execute shared prompts';
      default: return 'Basic access to workspace content';
    }
  };

  const isExpired = invitation && new Date() > invitation.expiresAt;
  const daysUntilExpiry = invitation ? Math.ceil((invitation.expiresAt.getTime() - Date.now()) / (1000 * 60 * 60 * 24)) : 0;

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-sm text-gray-500">Loading invitation...</p>
        </div>
      </div>
    );
  }

  if (!invitation) {
    return (
      <div className="text-center py-12">
        <EnvelopeIcon className="mx-auto h-12 w-12 text-gray-400" />
        <h3 className="mt-2 text-sm font-medium text-gray-900">No Invitations</h3>
        <p className="mt-1 text-sm text-gray-500">
          You don't have any pending workspace invitations.
        </p>
      </div>
    );
  }

  if (invitation.status === 'accepted') {
    return (
      <div className="bg-green-50 border border-green-200 rounded-lg p-6">
        <div className="flex">
          <div className="flex-shrink-0">
            <CheckCircleIcon className="h-6 w-6 text-green-600" />
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-green-800">
              Invitation Accepted!
            </h3>
            <div className="mt-2 text-sm text-green-700">
              <p>
                You've successfully joined <strong>{invitation.workspaceName}</strong>.
                You can now access shared prompts and collaborate with your team.
              </p>
            </div>
            <div className="mt-4">
              <button
                onClick={() => {
                  // Navigate to workspace
                  console.log('Navigate to workspace:', invitation.workspaceId);
                }}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
              >
                Go to Workspace
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (invitation.status === 'declined') {
    return (
      <div className="bg-gray-50 border border-gray-200 rounded-lg p-6">
        <div className="flex">
          <div className="flex-shrink-0">
            <XCircleIcon className="h-6 w-6 text-gray-600" />
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-gray-800">
              Invitation Declined
            </h3>
            <div className="mt-2 text-sm text-gray-700">
              <p>
                You've declined the invitation to join <strong>{invitation.workspaceName}</strong>.
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (isExpired) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-6">
        <div className="flex">
          <div className="flex-shrink-0">
            <ExclamationTriangleIcon className="h-6 w-6 text-red-600" />
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-red-800">
              Invitation Expired
            </h3>
            <div className="mt-2 text-sm text-red-700">
              <p>
                This invitation to join <strong>{invitation.workspaceName}</strong> has expired.
                Please contact {invitation.inviterName} for a new invitation.
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white shadow rounded-lg overflow-hidden">
      {/* Header */}
      <div className="px-6 py-4 bg-blue-50 border-b border-blue-200">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <UserPlusIcon className="h-6 w-6 text-blue-600" />
          </div>
          <div className="ml-3">
            <h3 className="text-lg font-medium text-blue-900">
              Workspace Invitation
            </h3>
            <p className="text-sm text-blue-700">
              You've been invited to join a workspace
            </p>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="px-6 py-6">
        {error && (
          <div className="mb-4 bg-red-50 border border-red-200 rounded-md p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <ExclamationTriangleIcon className="h-5 w-5 text-red-400" />
              </div>
              <div className="ml-3">
                <p className="text-sm text-red-800">{error}</p>
              </div>
            </div>
          </div>
        )}

        {/* Workspace Info */}
        <div className="mb-6">
          <h4 className="text-xl font-semibold text-gray-900 mb-2">
            {invitation.workspaceName}
          </h4>
          <div className="flex items-center text-sm text-gray-500 mb-3">
            <span>Invited by <strong>{invitation.inviterName}</strong></span>
            <span className="mx-2">•</span>
            <span>{invitation.createdAt.toLocaleDateString()}</span>
          </div>

          {invitation.message && (
            <div className="bg-gray-50 rounded-lg p-4 mb-4">
              <p className="text-sm text-gray-700">{invitation.message}</p>
            </div>
          )}
        </div>

        {/* Role Info */}
        <div className="mb-6">
          <div className="flex items-center mb-2">
            <span className="text-sm font-medium text-gray-700 mr-2">Your role:</span>
            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getRoleColor(invitation.role)}`}>
              {invitation.role.charAt(0).toUpperCase() + invitation.role.slice(1)}
            </span>
          </div>
          <p className="text-sm text-gray-600">{getRoleDescription(invitation.role)}</p>
        </div>

        {/* Expiry Warning */}
        {daysUntilExpiry <= 2 && (
          <div className="mb-6 bg-yellow-50 border border-yellow-200 rounded-md p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <ClockIcon className="h-5 w-5 text-yellow-400" />
              </div>
              <div className="ml-3">
                <p className="text-sm text-yellow-800">
                  This invitation expires in {daysUntilExpiry} day{daysUntilExpiry !== 1 ? 's' : ''}.
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Actions */}
        <div className="flex space-x-3">
          <button
            onClick={handleAcceptInvitation}
            disabled={processing}
            className="flex-1 inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {processing ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Accepting...
              </>
            ) : (
              <>
                <CheckCircleIcon className="h-4 w-4 mr-2" />
                Accept Invitation
              </>
            )}
          </button>
          <button
            onClick={handleDeclineInvitation}
            disabled={processing}
            className="flex-1 inline-flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {processing ? (
              'Processing...'
            ) : (
              <>
                <XCircleIcon className="h-4 w-4 mr-2" />
                Decline
              </>
            )}
          </button>
        </div>

        {/* Info */}
        <div className="mt-4 bg-blue-50 border border-blue-200 rounded-md p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <InformationCircleIcon className="h-5 w-5 text-blue-400" />
            </div>
            <div className="ml-3">
              <p className="text-sm text-blue-700">
                By accepting this invitation, you'll gain access to shared prompts, documents,
                and collaboration features within this workspace.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default InvitationFlow;