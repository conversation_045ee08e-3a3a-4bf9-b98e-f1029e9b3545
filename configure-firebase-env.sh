#!/bin/bash

# Configure Firebase Functions Environment Variables
# This script sets up the environment variables for AI providers

echo "🔧 Configuring Firebase Functions Environment Variables..."

# Set OpenRouter API Key
echo "Setting OPENROUTER_API_KEY..."
firebase functions:config:set openrouter.api_key="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"

# Set OpenRouter RAG API Key
echo "Setting OPENROUTER_API_KEY_RAG..."
firebase functions:config:set openrouter.api_key_rag="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"

# Set Google API Key
echo "Setting GOOGLE_API_KEY..."
firebase functions:config:set google.api_key="AIzaSyCXVU58BoDu5hwa4Ppz3R8tV3GpWmWEq7g"

# Set Model Configuration
echo "Setting model configurations..."
firebase functions:config:set models.prompt_generation="nvidia/llama-3.1-nemotron-ultra-253b-v1:free"
firebase functions:config:set models.rag_processing="nvidia/llama-3.1-nemotron-ultra-253b-v1:free"
firebase functions:config:set models.chat="nvidia/llama-3.1-nemotron-ultra-253b-v1:free"

# Set Default Provider
echo "Setting default provider..."
firebase functions:config:set llm.default_provider="openrouter"

# Set OpenRouter Model
echo "Setting OpenRouter model..."
firebase functions:config:set openrouter.model="nvidia/llama-3.1-nemotron-ultra-253b-v1:free"

# Set Environment
echo "Setting environment..."
firebase functions:config:set app.environment="production"

echo "✅ Environment variables configured successfully!"
echo ""
echo "📋 Current Firebase Functions configuration:"
firebase functions:config:get

echo ""
echo "🚀 Ready to deploy Firebase Functions with AI integration!"
