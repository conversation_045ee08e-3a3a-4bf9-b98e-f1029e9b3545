# Service Layer Architecture Migration Guide

## Overview

The service layer has been refactored to implement a unified architecture with standardized error handling, caching, metrics, and dependency injection. This guide explains the changes and how to migrate existing code.

## Key Changes

### 1. Unified Base Service Class

All services now extend from `BaseService` which provides:
- Standardized error handling with `StandardError`
- Unified caching with configurable TTL
- Metrics collection and monitoring
- Dependency injection support
- Retry mechanisms with exponential backoff
- Request/response logging

### 2. Service Factory Pattern

Services are now created through `ServiceFactory` which:
- Manages service dependencies
- Provides environment-specific configurations
- Enables dependency injection
- Supports service presets (development, production, etc.)

### 3. Service Registry

Central registry for managing service instances:
- Service registration and discovery
- Health monitoring
- Lifecycle management
- Event-driven service status notifications

### 4. Standardized Interfaces

All services implement standard interfaces:
- `CRUDService` for basic CRUD operations
- `SearchService` for search functionality
- `DocumentService` for file operations
- `AuthenticationService` for auth operations

## Migration Steps

### Step 1: Update Service Imports

**Before:**
```typescript
import { DocumentService } from '../services/documentService';

// Direct static method calls
const documents = await DocumentService.getUserDocuments(userId);
```

**After:**
```typescript
import { DocumentService, serviceFactory } from '../services';

// Option 1: Use service factory (recommended)
const documentService = serviceFactory.createService(DocumentService, 'DocumentService');
const response = await documentService.getUserDocuments(userId);
const documents = response.data;

// Option 2: Static methods still work for backward compatibility
const documents = await DocumentService.getUserDocuments(userId);
```

### Step 2: Handle Service Responses

**Before:**
```typescript
try {
  const documents = await DocumentService.getUserDocuments(userId);
  setDocuments(documents);
} catch (error) {
  console.error('Error:', error);
  setError(error.message);
}
```

**After:**
```typescript
const documentService = serviceFactory.createService(DocumentService, 'DocumentService');
const response = await documentService.getUserDocuments(userId);

if (response.success) {
  setDocuments(response.data);
  console.log('Cache hit:', response.metadata.cacheHit);
  console.log('Processing time:', response.metadata.processingTime);
} else {
  console.error('Error:', response.error);
  setError(response.error?.userMessage || 'An error occurred');
}
```

### Step 3: Use Service Registry (Optional)

**For applications that need centralized service management:**
```typescript
import { serviceRegistry, DocumentService } from '../services';

// Register service
serviceRegistry.register('documents', new DocumentService());

// Get service
const documentService = serviceRegistry.get<DocumentService>('documents');

// Auto-register with monitoring
const documentService = serviceRegistry.autoRegisterService(
  DocumentService, 
  'documents',
  { enableMetrics: true }
);

// Start health monitoring
serviceRegistry.startMonitoring('documents', 30000); // Check every 30 seconds
```

### Step 4: Configure Services

**Environment-specific configuration:**
```typescript
import { ServiceFactory, ServicePresets } from '../services';

// Configure for development
ServiceFactory.configure({
  defaultServiceConfig: ServicePresets.development
});

// Or custom configuration
ServiceFactory.configure({
  defaultServiceConfig: {
    enableCaching: true,
    defaultTTL: 60000,
    enableRetry: true,
    maxRetries: 2,
    enableMetrics: true,
    enableLogging: true
  }
});
```

## New Features Available

### 1. Advanced Error Handling

```typescript
import { useErrorHandler } from '../hooks/useErrorHandler';

const { handleError } = useErrorHandler();

const response = await documentService.getUserDocuments(userId);
if (!response.success) {
  handleError(response.error); // Automatically shows user-friendly messages
}
```

### 2. Service Metrics

```typescript
import { serviceRegistry } from '../services';

// Get service metrics
const metrics = serviceRegistry.getServiceMetrics();
console.log('Document service access count:', metrics.DocumentService.accessCount);
console.log('Average response time:', metrics.DocumentService.averageResponseTime);
```

### 3. Health Monitoring

```typescript
import { serviceRegistry } from '../services';

// Monitor service health
serviceRegistry.onServiceDown('documents', (error) => {
  console.error('Document service is down:', error);
  // Show user notification or fallback UI
});

serviceRegistry.onServiceUp('documents', () => {
  console.log('Document service is back online');
  // Hide error notifications
});
```

### 4. Custom Service Operations

```typescript
import { ServiceOperation, BaseService } from '../services';

class CustomDocumentOperation implements ServiceOperation<Document[]> {
  constructor(private filters: any) {}

  async execute(): Promise<Document[]> {
    // Custom logic here
    return [];
  }

  getCacheKey(): string {
    return `custom-docs:${JSON.stringify(this.filters)}`;
  }

  getContext(): string {
    return 'customDocumentOperation';
  }

  validate(): boolean {
    return !!this.filters;
  }
}

// Use in service
const response = await documentService.executeOperation(new CustomDocumentOperation(filters));
```

## Backward Compatibility

All existing static method calls continue to work:

```typescript
// These still work without changes
const documents = await DocumentService.getUserDocuments(userId);
const document = await DocumentService.getDocument(documentId);
await DocumentService.updateDocumentStatus(documentId, 'completed');
```

## Performance Benefits

1. **Caching**: Automatic request deduplication and response caching
2. **Retry Logic**: Automatic retry with exponential backoff for failed requests
3. **Metrics**: Performance monitoring and optimization insights
4. **Error Handling**: Reduced error handling boilerplate code

## Testing

Services can be easily mocked for testing:

```typescript
import { ServiceFactory } from '../services';

// Mock service factory for tests
const mockDocumentService = {
  getUserDocuments: jest.fn().mockResolvedValue({
    success: true,
    data: [],
    metadata: { timestamp: new Date(), requestId: 'test', processingTime: 100 }
  })
};

ServiceFactory.configure({
  // Inject mock services
});
```

## Troubleshooting

### Common Issues

1. **Service not found**: Ensure service is registered in service registry
2. **Configuration errors**: Validate service configuration using `validateServiceConfig()`
3. **Cache issues**: Clear cache using `serviceRegistry.getCacheService()?.clear()`

### Debug Mode

Enable debug logging in development:

```typescript
ServiceFactory.configure({
  defaultServiceConfig: {
    ...ServicePresets.development,
    enableLogging: true
  }
});
```

## Next Steps

1. Gradually migrate components to use the new service architecture
2. Implement custom service operations for complex business logic
3. Add health monitoring for critical services
4. Configure environment-specific service settings
5. Add service metrics to monitoring dashboards

## Support

For questions or issues with the migration, refer to:
- Service architecture documentation
- Error handling guide
- Performance optimization guide
- Testing best practices
