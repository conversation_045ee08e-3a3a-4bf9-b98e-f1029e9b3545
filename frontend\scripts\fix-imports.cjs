/**
 * Import Path Fixer Script
 * Automatically converts relative imports to alias-based imports
 * for better maintainability and build compatibility
 */

const fs = require('fs');
const path = require('path');

class ImportFixer {
  constructor() {
    this.projectRoot = path.resolve(__dirname, '..');
    this.srcDir = path.join(this.projectRoot, 'src');
    
    // Mapping of relative paths to alias paths
    this.aliasMap = {
      // Core directories
      '../../../contexts/': '@/contexts/',
      '../../contexts/': '@/contexts/',
      '../contexts/': '@/contexts/',
      '../../../services/': '@/services/',
      '../../services/': '@/services/',
      '../services/': '@/services/',
      '../../../types': '@/types',
      '../../types': '@/types',
      '../types': '@/types',
      '../../../utils/': '@/utils/',
      '../../utils/': '@/utils/',
      '../utils/': '@/utils/',
      '../../../hooks/': '@/hooks/',
      '../../hooks/': '@/hooks/',
      '../hooks/': '@/hooks/',
      '../../../config/': '@/config/',
      '../../config/': '@/config/',
      '../config/': '@/config/',
      '../../../lib/': '@/lib/',
      '../../lib/': '@/lib/',
      '../lib/': '@/lib/',
      
      // Component directories
      '../../../components/': '@/components/',
      '../../components/': '@/components/',
      '../components/': '@/components/',
      '../common/': '@/components/common/',
      '../../common/': '@/components/common/',
      '../../../common/': '@/components/common/',
      '../features/': '@/components/features/',
      '../../features/': '@/components/features/',
      '../../../features/': '@/components/features/',
      '../ui/': '@/components/ui/',
      '../../ui/': '@/components/ui/',
      '../../../ui/': '@/components/ui/',
      
      // Pages
      '../../../pages/': '@/pages/',
      '../../pages/': '@/pages/',
      '../pages/': '@/pages/',
    };
  }

  /**
   * Fix all import paths in the project
   */
  async fixAllImports() {
    console.log('🔧 Fixing import paths...');
    
    const files = this.findTypeScriptFiles(this.srcDir);
    let fixedCount = 0;
    
    for (const file of files) {
      const fixed = await this.fixImportsInFile(file);
      if (fixed) {
        fixedCount++;
      }
    }
    
    console.log(`✅ Fixed imports in ${fixedCount} files`);
  }

  /**
   * Fix imports in a single file
   */
  async fixImportsInFile(filePath) {
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      let newContent = content;
      let hasChanges = false;

      // Process each alias mapping
      for (const [relativePath, aliasPath] of Object.entries(this.aliasMap)) {
        const regex = new RegExp(`from ['"]${this.escapeRegex(relativePath)}([^'"]*?)['"]`, 'g');
        const replacement = `from '${aliasPath}$1'`;
        
        if (regex.test(newContent)) {
          newContent = newContent.replace(regex, replacement);
          hasChanges = true;
        }
      }

      // Also fix import statements (not just from statements)
      for (const [relativePath, aliasPath] of Object.entries(this.aliasMap)) {
        const regex = new RegExp(`import ['"]${this.escapeRegex(relativePath)}([^'"]*?)['"]`, 'g');
        const replacement = `import '${aliasPath}$1'`;
        
        if (regex.test(newContent)) {
          newContent = newContent.replace(regex, replacement);
          hasChanges = true;
        }
      }

      if (hasChanges) {
        fs.writeFileSync(filePath, newContent);
        console.log(`✅ Fixed imports in ${path.relative(this.projectRoot, filePath)}`);
        return true;
      }
      
      return false;
    } catch (error) {
      console.error(`❌ Error processing ${filePath}:`, error.message);
      return false;
    }
  }

  /**
   * Find all TypeScript/JavaScript files in a directory
   */
  findTypeScriptFiles(dir) {
    const files = [];
    
    if (!fs.existsSync(dir)) {
      return files;
    }
    
    const entries = fs.readdirSync(dir, { withFileTypes: true });
    
    for (const entry of entries) {
      const fullPath = path.join(dir, entry.name);
      
      if (entry.isDirectory()) {
        // Skip node_modules and dist directories
        if (!['node_modules', 'dist', '.git'].includes(entry.name)) {
          files.push(...this.findTypeScriptFiles(fullPath));
        }
      } else if (this.isTypeScriptFile(entry.name)) {
        files.push(fullPath);
      }
    }
    
    return files;
  }

  /**
   * Check if file is a TypeScript/JavaScript file
   */
  isTypeScriptFile(filename) {
    const extensions = ['.ts', '.tsx', '.js', '.jsx'];
    const ext = path.extname(filename);
    return extensions.includes(ext);
  }

  /**
   * Escape special regex characters
   */
  escapeRegex(string) {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  }
}

// Run the fixer if called directly
if (require.main === module) {
  const fixer = new ImportFixer();
  fixer.fixAllImports().catch(console.error);
}

module.exports = { ImportFixer };
