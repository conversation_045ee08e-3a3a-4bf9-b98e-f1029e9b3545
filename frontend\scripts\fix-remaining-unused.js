#!/usr/bin/env node

/**
 * Targeted Unused Variables Fixer
 * Fixes remaining unused variables with more precision
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class TargetedUnusedFixer {
  constructor() {
    this.projectRoot = path.resolve(__dirname, '..');
    this.fixedCount = 0;
  }

  /**
   * Fix specific unused variable patterns
   */
  async fixSpecificPatterns() {
    console.log('🎯 Fixing specific unused variable patterns...\n');

    // Fix unused imports that are clearly not needed
    await this.fixUnusedImports();
    
    // Fix unused destructured variables
    await this.fixUnusedDestructuring();
    
    // Fix unused function parameters
    await this.fixUnusedParameters();
    
    // Fix unused assignments
    await this.fixUnusedAssignments();

    console.log(`\n✅ Fixed ${this.fixedCount} unused variable issues`);
  }

  /**
   * Fix unused imports
   */
  async fixUnusedImports() {
    const patterns = [
      // Remove unused icon imports
      { file: 'src/components/features/prompts/AIEnhancedPromptEditor.tsx', remove: ['PromptVariable', 'RefreshCw', 'AlertCircle', 'ArrowLeft'] },
      { file: 'src/components/features/prompts/PromptQualityAssistant.tsx', remove: ['X'] },
      { file: 'src/components/features/security/SecurityDashboard.tsx', remove: ['EyeIcon', 'LockClosedIcon'] },
      { file: 'src/components/features/security/SecurityTestingDashboard.tsx', remove: ['XCircleIcon', 'ClockIcon'] },
      { file: 'src/components/features/testing/ABTestingDashboard.tsx', remove: ['ExclamationTriangleIcon', 'UserGroupIcon'] },
      { file: 'src/pages/Workspaces.tsx', remove: ['DocumentTextIcon', 'ClockIcon', 'Workspace'] },
      { file: 'src/components/features/profile/UserProfile.tsx', remove: ['CogIcon'] },
      { file: 'src/services/analyticsService.ts', remove: ['Timestamp', 'doc', 'getDoc'] },
      { file: 'src/services/marketplaceService.ts', remove: ['deleteDoc'] },
      { file: 'src/services/optimizedFirestore.ts', remove: ['getDoc', 'connectFirestoreEmulator'] },
      { file: 'src/services/settingsService.ts', remove: ['collection', 'query', 'where', 'getDocs'] },
      { file: 'src/components/monitoring/TestPerformanceDashboard.tsx', remove: ['performanceMonitor', 'apiTracker'] },
      { file: 'src/components/monitoring/PerformanceMonitoringDashboard.tsx', remove: ['TooltipResponsiveContainer'] },
    ];

    for (const pattern of patterns) {
      await this.removeUnusedImports(pattern.file, pattern.remove);
    }
  }

  /**
   * Remove unused imports from a file
   */
  async removeUnusedImports(filePath, unusedImports) {
    const fullPath = path.join(this.projectRoot, filePath);
    if (!fs.existsSync(fullPath)) return;

    try {
      let content = fs.readFileSync(fullPath, 'utf8');
      let modified = false;

      for (const unusedImport of unusedImports) {
        // Remove from destructured imports
        const destructuredPattern = new RegExp(`\\s*,?\\s*${unusedImport}\\s*,?\\s*`, 'g');
        const beforeContent = content;
        content = content.replace(destructuredPattern, '');
        
        // Clean up empty destructuring
        content = content.replace(/{\s*,/g, '{');
        content = content.replace(/,\s*}/g, '}');
        content = content.replace(/{\s*}/g, '{}');
        
        // Remove entire import line if it becomes empty
        content = content.replace(/import\s+{}\s+from\s+[^;]+;?\s*\n/g, '');
        
        if (content !== beforeContent) {
          modified = true;
          this.fixedCount++;
          console.log(`  ✅ Removed unused import '${unusedImport}' from ${filePath}`);
        }
      }

      if (modified) {
        fs.writeFileSync(fullPath, content);
      }
    } catch (error) {
      console.error(`  ❌ Error fixing ${filePath}: ${error.message}`);
    }
  }

  /**
   * Fix unused destructuring
   */
  async fixUnusedDestructuring() {
    const patterns = [
      { file: 'src/components/features/api/APIKeyManager.tsx', remove: ['className', 'dataTestId', 'id'] },
      { file: 'src/components/monitoring/DevPerformanceDashboard.tsx', remove: ['className', 'dataTestId', 'id'] },
    ];

    for (const pattern of patterns) {
      await this.removeUnusedDestructuring(pattern.file, pattern.remove);
    }
  }

  /**
   * Remove unused destructuring variables
   */
  async removeUnusedDestructuring(filePath, unusedVars) {
    const fullPath = path.join(this.projectRoot, filePath);
    if (!fs.existsSync(fullPath)) return;

    try {
      let content = fs.readFileSync(fullPath, 'utf8');
      let modified = false;

      for (const unusedVar of unusedVars) {
        const beforeContent = content;
        // Remove from destructuring patterns
        content = content.replace(new RegExp(`\\s*${unusedVar}\\s*,?\\s*`, 'g'), '');
        content = content.replace(/{\s*,/g, '{');
        content = content.replace(/,\s*}/g, '}');
        
        if (content !== beforeContent) {
          modified = true;
          this.fixedCount++;
          console.log(`  ✅ Removed unused destructured var '${unusedVar}' from ${filePath}`);
        }
      }

      if (modified) {
        fs.writeFileSync(fullPath, content);
      }
    } catch (error) {
      console.error(`  ❌ Error fixing ${filePath}: ${error.message}`);
    }
  }

  /**
   * Fix unused parameters by prefixing with underscore
   */
  async fixUnusedParameters() {
    const files = [
      'src/components/features/admin/MonitoringDashboard.tsx',
      'src/components/monitoring/PerformanceMonitoringDashboard.tsx',
      'src/components/ui/Select.tsx',
      'src/hooks/usePerformanceMonitoring.ts',
      'src/hooks/useServiceWorker.ts',
    ];

    for (const file of files) {
      await this.prefixUnusedParams(file);
    }
  }

  /**
   * Prefix unused parameters with underscore
   */
  async prefixUnusedParams(filePath) {
    const fullPath = path.join(this.projectRoot, filePath);
    if (!fs.existsSync(fullPath)) return;

    try {
      let content = fs.readFileSync(fullPath, 'utf8');
      let modified = false;

      // Common unused parameter patterns
      const patterns = [
        { from: 'prevProps', to: '_prevProps' },
        { from: 'nextProps', to: '_nextProps' },
        { from: 'index', to: '_index' },
        { from: 'registration', to: '_registration' },
        { from: 'callback', to: '_callback' },
        { from: 'error', to: '_error' },
        { from: 'event', to: '_event' },
      ];

      for (const pattern of patterns) {
        const beforeContent = content;
        // Only replace in parameter positions (after comma or opening paren, before comma or closing paren)
        content = content.replace(
          new RegExp(`([,(]\\s*)${pattern.from}(\\s*[,)])`, 'g'),
          `$1${pattern.to}$2`
        );
        
        if (content !== beforeContent) {
          modified = true;
          this.fixedCount++;
          console.log(`  ✅ Prefixed unused param '${pattern.from}' in ${filePath}`);
        }
      }

      if (modified) {
        fs.writeFileSync(fullPath, content);
      }
    } catch (error) {
      console.error(`  ❌ Error fixing ${filePath}: ${error.message}`);
    }
  }

  /**
   * Fix unused assignments by commenting them out
   */
  async fixUnusedAssignments() {
    const patterns = [
      { file: 'src/components/features/execution/ModelComparison.tsx', vars: ['selectedResponse', 'setSelectedResponse'] },
      { file: 'src/components/features/feedback/FeedbackCollector.tsx', vars: ['rating'] },
      { file: 'src/components/features/prompts/PromptQualityAssistant.tsx', vars: ['showDetails', 'setShowDetails'] },
      { file: 'src/components/features/testing/ABTestingDashboard.tsx', vars: ['selectedExperiment'] },
      { file: 'src/pages/Workspaces.tsx', vars: ['user', 'error', 'createWorkspace', 'clearError'] },
    ];

    for (const pattern of patterns) {
      await this.commentUnusedAssignments(pattern.file, pattern.vars);
    }
  }

  /**
   * Comment out unused assignments
   */
  async commentUnusedAssignments(filePath, unusedVars) {
    const fullPath = path.join(this.projectRoot, filePath);
    if (!fs.existsSync(fullPath)) return;

    try {
      let content = fs.readFileSync(fullPath, 'utf8');
      const lines = content.split('\n');
      let modified = false;

      for (let i = 0; i < lines.length; i++) {
        const line = lines[i];
        
        for (const unusedVar of unusedVars) {
          if (line.includes(`${unusedVar} =`) && !line.trim().startsWith('//')) {
            lines[i] = `  // ${line.trim()} // TODO: Remove unused variable`;
            modified = true;
            this.fixedCount++;
            console.log(`  ✅ Commented unused assignment '${unusedVar}' in ${filePath}`);
          }
        }
      }

      if (modified) {
        fs.writeFileSync(fullPath, lines.join('\n'));
      }
    } catch (error) {
      console.error(`  ❌ Error fixing ${filePath}: ${error.message}`);
    }
  }
}

// Run the fixer
const fixer = new TargetedUnusedFixer();
fixer.fixSpecificPatterns().catch(console.error);
