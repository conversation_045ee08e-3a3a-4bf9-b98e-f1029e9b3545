/**
 * LCP (Largest Contentful Paint) Optimization Utilities
 * Provides runtime LCP measurement, optimization, and monitoring
 */

interface LCPData {
  value: number;
  element: Element | null;
  url?: string;
  size: number;
  loadTime?: number;
  renderTime?: number;
  id?: string;
  tagName?: string;
  className?: string;
}

interface LCPOptimizationConfig {
  enableMonitoring: boolean;
  reportToAnalytics: boolean;
  optimizeImages: boolean;
  preloadCriticalResources: boolean;
  inlineCriticalCSS: boolean;
  threshold: number; // LCP threshold in milliseconds
}

class LCPOptimizer {
  private config: LCPOptimizationConfig;
  private lcpData: LCPData | null = null;
  private observer: PerformanceObserver | null = null;
  private optimizations: string[] = [];

  constructor(config: Partial<LCPOptimizationConfig> = {}) {
    this.config = {
      enableMonitoring: true,
      reportToAnalytics: false,
      optimizeImages: true,
      preloadCriticalResources: true,
      inlineCriticalCSS: true,
      threshold: 2500, // 2.5 seconds
      ...config,
    };

    if (this.config.enableMonitoring) {
      this.initializeLCPMonitoring();
    }
  }

  /**
   * Initialize LCP monitoring
   */
  private initializeLCPMonitoring(): void {
    if (!('PerformanceObserver' in window)) {
      console.warn('PerformanceObserver not supported');
      return;
    }

    this.observer = new PerformanceObserver((list) => {
      const entries = list.getEntries() as PerformanceEntry[];
      const lastEntry = entries[entries.length - 1] as any;

      this.lcpData = {
        value: lastEntry.startTime,
        element: lastEntry.element,
        url: lastEntry.url,
        size: lastEntry.size,
        loadTime: lastEntry.loadTime,
        renderTime: lastEntry.renderTime,
        id: lastEntry.element?.id,
        tagName: lastEntry.element?.tagName,
        className: lastEntry.element?.className,
      };

      // Analyze and optimize the LCP element
      this.analyzeLCPElement(lastEntry.element, lastEntry);
    });

    this.observer.observe({ type: 'largest-contentful-paint', buffered: true });
  }

  /**
   * Analyze the LCP element and apply optimizations
   */
  private analyzeLCPElement(element: Element, _entry: any): void {
    if (!element) return;

    const tagName = element.tagName.toLowerCase();
    const optimizations: string[] = [];

    // Image optimizations
    if (tagName === 'img') {
      optimizations.push(...this.optimizeImage(element as HTMLImageElement));
    }

    // Text optimizations
    if (['h1', 'h2', 'h3', 'p', 'div'].includes(tagName)) {
      optimizations.push(...this.optimizeText(element));
    }

    // General optimizations
    optimizations.push(...this.optimizeElement(element));

    this.optimizations = optimizations;

    // Report results
    this.reportLCPResults();
  }

  /**
   * Optimize image elements
   */
  private optimizeImage(img: HTMLImageElement): string[] {
    const optimizations: string[] = [];

    // Add loading="eager" for LCP images
    if (!img.loading || img.loading !== 'eager') {
      img.loading = 'eager';
      optimizations.push('Added loading="eager" to LCP image');
    }

    // Add fetchpriority="high" for LCP images
    if (!(img as any).fetchPriority || (img as any).fetchPriority !== 'high') {
      (img as any).fetchPriority = 'high';
      optimizations.push('Added fetchpriority="high" to LCP image');
    }

    // Add decoding="sync" for LCP images
    if (!img.decoding || img.decoding !== 'sync') {
      img.decoding = 'sync';
      optimizations.push('Added decoding="sync" to LCP image');
    }

    // Preload the image
    if (img.src && !document.querySelector(`link[rel="preload"][href="${img.src}"]`)) {
      this.preloadResource(img.src, 'image');
      optimizations.push('Preloaded LCP image');
    }

    return optimizations;
  }

  /**
   * Optimize text elements
   */
  private optimizeText(element: Element): string[] {
    const optimizations: string[] = [];
    const computedStyle = window.getComputedStyle(element);

    // Optimize font loading
    const fontFamily = computedStyle.fontFamily;
    if (fontFamily && !this.isFontPreloaded(fontFamily)) {
      this.preloadFont(fontFamily);
      optimizations.push(`Preloaded font: ${fontFamily}`);
    }

    // Add font-display: swap if not present
    if (!computedStyle.fontDisplay || computedStyle.fontDisplay === 'auto') {
      (element as HTMLElement).style.fontDisplay = 'swap';
      optimizations.push('Added font-display: swap');
    }

    return optimizations;
  }

  /**
   * General element optimizations
   */
  private optimizeElement(element: Element): string[] {
    const optimizations: string[] = [];

    // Add contain property for better rendering performance
    const style = (element as HTMLElement).style;
    if (!style.contain) {
      style.contain = 'layout style paint';
      optimizations.push('Added CSS containment');
    }

    // Add will-change for elements that might change
    if (!style.willChange) {
      style.willChange = 'auto';
      optimizations.push('Optimized will-change property');
    }

    return optimizations;
  }

  /**
   * Preload a resource
   */
  private preloadResource(href: string, as: string, type?: string): void {
    const link = document.createElement('link');
    link.rel = 'preload';
    link.href = href;
    link.as = as;
    if (type) link.type = type;
    if (as === 'font') link.crossOrigin = 'anonymous';
    
    document.head.appendChild(link);
  }

  /**
   * Check if font is already preloaded
   */
  private isFontPreloaded(fontFamily: string): boolean {
    const links = document.querySelectorAll('link[rel="preload"][as="font"]');
    return Array.from(links).some(link => 
      (link as HTMLLinkElement).href.includes(fontFamily.replace(/['"]/g, ''))
    );
  }

  /**
   * Preload font
   */
  private preloadFont(fontFamily: string): void {
    // Extract font name from font-family string
    const fontName = fontFamily.split(',')[0].replace(/['"]/g, '').trim();
    
    // Common Google Fonts URLs
    const googleFontsUrl = `https://fonts.googleapis.com/css2?family=${fontName.replace(/\s+/g, '+')}:wght@400;500;600;700&display=swap`;
    
    this.preloadResource(googleFontsUrl, 'style');
  }

  /**
   * Report LCP results
   */
  private reportLCPResults(): void {
    if (!this.lcpData) return;

    const isGood = this.lcpData.value <= this.config.threshold;
    const status = isGood ? '✅' : '⚠️';
    
    console.group(`${status} LCP Analysis Results`);
    console.log(`LCP Time: ${this.lcpData.value.toFixed(2)}ms (threshold: ${this.config.threshold}ms)`);
    console.log(`Element: ${this.lcpData.tagName}${this.lcpData.id ? `#${this.lcpData.id}` : ''}${this.lcpData.className ? `.${this.lcpData.className.split(' ')[0]}` : ''}`);
    console.log(`Size: ${this.lcpData.size} bytes`);
    
    if (this.optimizations.length > 0) {
      console.group('🔧 Applied Optimizations:');
      this.optimizations.forEach(opt => console.log(`• ${opt}`));
      console.groupEnd();
    }
    
    console.groupEnd();

    // Report to analytics if enabled
    if (this.config.reportToAnalytics) {
      this.reportToAnalytics();
    }
  }

  /**
   * Report to analytics
   */
  private async reportToAnalytics(): Promise<void> {
    try {
      // Import analytics dynamically to avoid blocking
      const { analytics } = await import('../config/firebase');
      if (analytics) {
        const { logEvent } = await import('firebase/analytics');
        logEvent(analytics, 'lcp_measurement', {
          lcp_value: this.lcpData?.value,
          lcp_element: this.lcpData?.tagName,
          lcp_size: this.lcpData?.size,
          optimizations_applied: this.optimizations.length,
          is_good: this.lcpData ? this.lcpData.value <= this.config.threshold : false,
        });
      }
    } catch (error) {
      console.warn('Failed to report LCP to analytics:', error);
    }
  }

  /**
   * Get current LCP data
   */
  public getLCPData(): LCPData | null {
    return this.lcpData;
  }

  /**
   * Get applied optimizations
   */
  public getOptimizations(): string[] {
    return this.optimizations;
  }

  /**
   * Manually trigger LCP optimization for a specific element
   */
  public optimizeElement(element: Element): string[] {
    return this.optimizeElement(element);
  }

  /**
   * Destroy the optimizer
   */
  public destroy(): void {
    if (this.observer) {
      this.observer.disconnect();
      this.observer = null;
    }
  }
}

// Export singleton instance
export const lcpOptimizer = new LCPOptimizer({
  enableMonitoring: import.meta.env.PROD || import.meta.env.VITE_ENABLE_LCP_MONITORING === 'true',
  reportToAnalytics: import.meta.env.PROD,
  threshold: 2500,
});

/**
 * Hook for using LCP optimization in React components
 */
export function useLCPOptimization() {
  return {
    getLCPData: () => lcpOptimizer.getLCPData(),
    getOptimizations: () => lcpOptimizer.getOptimizations(),
    optimizeElement: (element: Element) => lcpOptimizer.optimizeElement(element),
  };
}

/**
 * Initialize LCP optimization
 */
export function initializeLCPOptimization(): void {
  // The optimizer is already initialized as a singleton
  console.log('🎯 LCP Optimization initialized');
}
