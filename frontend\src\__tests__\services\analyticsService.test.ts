import { describe, it, expect, vi, beforeEach } from 'vitest';
import { analyticsService } from '@/services/analyticsService';

// Mock Firebase
const mockGetDocs = vi.fn();
const mockQuery = vi.fn();
const mockWhere = vi.fn();
const mockCollection = vi.fn();

vi.mock('firebase/firestore', () => ({
  collection: mockCollection,
  query: mockQuery,
  where: mockWhere,
  getDocs: mockGetDocs,
  Timestamp: {
    fromDate: vi.fn((date) => ({ seconds: Math.floor(date.getTime() / 1000) })),
    now: vi.fn(() => ({ seconds: Math.floor(Date.now() / 1000) }))
  }
}));

vi.mock('../config/firebase', () => ({
  db: {}
}));

describe('Analytics Service', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    
    // Setup default mock implementations
    mockCollection.mockReturnValue({});
    mockQuery.mockReturnValue({});
    mockWhere.mockReturnValue({});
  });

  describe('getUserAnalytics', () => {
    it('should handle empty collections gracefully', async () => {
      // Mock empty collection responses
      mockGetDocs.mockResolvedValue({
        docs: [],
        size: 0
      });

      const result = await analyticsService.getUserAnalytics('test-user', '7d');

      expect(result).toBeDefined();
      expect(result.metrics).toBeDefined();
      expect(result.metrics.totalPrompts).toBe(0);
      expect(result.metrics.totalExecutions).toBe(0);
      expect(result.metrics.totalDocuments).toBe(0);
      expect(result.metrics.successRate).toBe(100); // Default success rate when no data
    });

    it('should handle Firebase errors gracefully', async () => {
      // Mock Firebase error
      mockGetDocs.mockRejectedValue(new Error('Firebase connection failed'));

      const result = await analyticsService.getUserAnalytics('test-user', '7d');

      expect(result).toBeDefined();
      expect(result.metrics).toBeDefined();
      expect(result.modelUsage).toEqual([]);
      expect(result.activityData).toEqual([]);
      expect(result.topPrompts).toEqual([]);
      expect(result.costBreakdown).toEqual([]);
    });

    it('should return proper structure with mock data', async () => {
      // Mock some sample data
      const mockExecutionData = {
        userId: 'test-user',
        status: 'success',
        executionTime: 1500,
        cost: 0.05,
        model: 'gpt-4',
        promptId: 'prompt-1',
        createdAt: { seconds: Math.floor(Date.now() / 1000) }
      };

      mockGetDocs.mockResolvedValue({
        docs: [
          { data: () => mockExecutionData }
        ],
        size: 1
      });

      const result = await analyticsService.getUserAnalytics('test-user', '7d');

      expect(result).toBeDefined();
      expect(result.metrics.totalExecutions).toBe(1);
      expect(result.metrics.avgExecutionTime).toBe(1500);
      expect(result.metrics.totalCost).toBe(0.05);
      expect(result.metrics.successRate).toBe(100);
    });

    it('should handle different time ranges', async () => {
      mockGetDocs.mockResolvedValue({
        docs: [],
        size: 0
      });

      // Test different time ranges
      const timeRanges = ['7d', '30d', '90d'] as const;
      
      for (const timeRange of timeRanges) {
        const result = await analyticsService.getUserAnalytics('test-user', timeRange);
        expect(result).toBeDefined();
        expect(result.metrics).toBeDefined();
      }
    });
  });

  describe('getWorkspaceAnalytics', () => {
    it('should handle workspace analytics requests', async () => {
      mockGetDocs.mockResolvedValue({
        docs: [],
        size: 0
      });

      const result = await analyticsService.getWorkspaceAnalytics('workspace-1', '7d');

      expect(result).toBeDefined();
      expect(result.metrics).toBeDefined();
    });

    it('should handle workspace analytics errors gracefully', async () => {
      mockGetDocs.mockRejectedValue(new Error('Workspace not found'));

      const result = await analyticsService.getWorkspaceAnalytics('workspace-1', '7d');

      expect(result).toBeDefined();
      expect(result.metrics).toBeDefined();
      expect(result.metrics.totalExecutions).toBe(0);
    });
  });

  describe('error handling', () => {
    it('should not throw errors when Firebase is unavailable', async () => {
      mockGetDocs.mockRejectedValue(new Error('Network error'));

      // Should not throw
      await expect(analyticsService.getUserAnalytics('test-user', '7d')).resolves.toBeDefined();
      await expect(analyticsService.getWorkspaceAnalytics('workspace-1', '7d')).resolves.toBeDefined();
    });

    it('should return consistent data structure even on errors', async () => {
      mockGetDocs.mockRejectedValue(new Error('Database error'));

      const result = await analyticsService.getUserAnalytics('test-user', '7d');

      // Verify all required properties exist
      expect(result).toHaveProperty('metrics');
      expect(result).toHaveProperty('modelUsage');
      expect(result).toHaveProperty('activityData');
      expect(result).toHaveProperty('performanceMetrics');
      expect(result).toHaveProperty('topPrompts');
      expect(result).toHaveProperty('costBreakdown');

      // Verify metrics structure
      expect(result.metrics).toHaveProperty('totalPrompts');
      expect(result.metrics).toHaveProperty('totalExecutions');
      expect(result.metrics).toHaveProperty('totalDocuments');
      expect(result.metrics).toHaveProperty('avgExecutionTime');
      expect(result.metrics).toHaveProperty('successRate');
      expect(result.metrics).toHaveProperty('totalCost');
      expect(result.metrics).toHaveProperty('avgCostPerExecution');
    });
  });
});
