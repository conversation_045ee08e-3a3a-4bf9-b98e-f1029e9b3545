/**
 * 🎯 JSX RUNTIME FIX VERIFICATION SCRIPT
 * 
 * This script specifically tests the JSX runtime fix we just deployed.
 * 
 * INSTRUCTIONS:
 * 1. Open https://rag-prompt-library.web.app/ in your browser
 * 2. Hard refresh: Ctrl+Shift+R (Windows) or Cmd+Shift+R (Mac)
 * 3. Open Developer Tools (F12) → Console tab
 * 4. Copy and paste this ENTIRE script
 * 5. Press Enter to run
 */

console.clear();
console.log('%c🎯 JSX RUNTIME FIX VERIFICATION', 'color: #16a34a; font-size: 20px; font-weight: bold; background: #dcfce7; padding: 10px;');

const jsxTest = {
    results: [],
    passed: 0,
    failed: 0,
    
    test: function(description, testFn) {
        try {
            const result = testFn();
            if (result) {
                console.log(`%c✅ PASS: ${description}`, 'color: #16a34a; font-weight: bold;');
                this.passed++;
                this.results.push({ test: description, status: 'PASS', details: result });
            } else {
                console.log(`%c❌ FAIL: ${description}`, 'color: #dc2626; font-weight: bold;');
                this.failed++;
                this.results.push({ test: description, status: 'FAIL', details: result });
            }
        } catch (error) {
            console.log(`%c❌ ERROR: ${description} - ${error.message}`, 'color: #dc2626; font-weight: bold;');
            this.failed++;
            this.results.push({ test: description, status: 'ERROR', details: error.message });
        }
    },
    
    section: function(title) {
        console.log(`\n%c🔧 ${title}`, 'color: #1f2937; font-size: 16px; font-weight: bold; background: #f3f4f6; padding: 8px;');
    }
};

// STEP 1: Check for New File Hashes (Classic JSX Runtime Build)
jsxTest.section('STEP 1: NEW BUILD VERIFICATION');

const expectedNewHashes = ['DGoBtgzP', 'vPj4KYjs', 'CJyQGyaW', 'C-q7rv7I'];
const previousHashes = ['num6gqZz', 'DsaXcdDe', 'B_Tmb8de', 'QYuoXLh0'];

jsxTest.test('New Classic JSX Build Files Loaded', () => {
    const scripts = Array.from(document.querySelectorAll('script[src]'));
    let newFilesFound = 0;
    let oldFilesFound = 0;
    
    scripts.forEach(script => {
        const src = script.src;
        expectedNewHashes.forEach(hash => {
            if (src.includes(hash)) {
                newFilesFound++;
                console.log(`   ✅ Found new file: ${src.split('/').pop()}`);
            }
        });
        previousHashes.forEach(hash => {
            if (src.includes(hash)) {
                oldFilesFound++;
                console.log(`   ❌ Found old file: ${src.split('/').pop()}`);
            }
        });
    });
    
    if (newFilesFound > 0 && oldFilesFound === 0) {
        return `Found ${newFilesFound} new classic JSX files, no old files`;
    } else if (oldFilesFound > 0) {
        return false; // Old files still cached
    } else {
        return 'Could not determine file versions';
    }
});

// STEP 2: JSX Runtime Error Detection
jsxTest.section('STEP 2: JSX RUNTIME ERROR DETECTION');

let jsxErrorDetected = false;
let jsxErrorMessage = '';

// Check console history for JSX errors
const consoleHistory = [];
const originalError = console.error;
const originalLog = console.log;

console.error = function(...args) {
    const message = args.join(' ');
    consoleHistory.push({ type: 'error', message });
    
    if (message.includes('jsxDEV') || message.includes('jsx') && message.includes('not a function')) {
        jsxErrorDetected = true;
        jsxErrorMessage = message;
    }
    
    originalError.apply(console, args);
};

jsxTest.test('No JSX Runtime Errors in Console', () => {
    // Check for existing errors in the page
    const pageErrors = window.performance && window.performance.getEntriesByType ? 
                      window.performance.getEntriesByType('navigation') : [];
    
    // Check if any error tracking has captured JSX errors
    if (window.trackError || window.errorTracker) {
        // App has error tracking - check if JSX errors were tracked
        return 'Error tracking system detected - check manually for JSX errors';
    }
    
    // Check document for error indicators
    const hasErrorContent = document.body.textContent.includes('jsxDEV') || 
                           document.body.textContent.includes('TypeError');
    
    if (hasErrorContent) {
        return false;
    }
    
    return !jsxErrorDetected ? 'No JSX errors detected' : false;
});

// STEP 3: React Application Status
jsxTest.section('STEP 3: REACT APPLICATION STATUS');

jsxTest.test('React Components Rendered Successfully', () => {
    // Check for React-rendered content
    const hasReactContent = document.querySelector('[data-reactroot]') || 
                           document.querySelector('main') ||
                           document.querySelectorAll('button').length > 0 ||
                           document.querySelectorAll('nav').length > 0;
    
    if (hasReactContent) {
        const buttonCount = document.querySelectorAll('button').length;
        const linkCount = document.querySelectorAll('a').length;
        return `React content detected: ${buttonCount} buttons, ${linkCount} links`;
    }
    
    return false;
});

jsxTest.test('Navigation System Working', () => {
    // Check if React Router is working
    const currentPath = window.location.pathname;
    const hasNavigation = document.querySelector('nav') || 
                         document.querySelector('[role="navigation"]') ||
                         document.querySelectorAll('a[href]').length > 0;
    
    if (hasNavigation) {
        return `Navigation detected on path: ${currentPath}`;
    }
    
    return false;
});

// STEP 4: Execute Button Detection
jsxTest.section('STEP 4: EXECUTE BUTTON FUNCTIONALITY');

jsxTest.test('Execute Buttons Present and Functional', () => {
    const executeButtons = Array.from(document.querySelectorAll('button')).filter(btn => 
        btn.textContent.toLowerCase().includes('execute') ||
        btn.textContent.toLowerCase().includes('run') ||
        btn.getAttribute('aria-label')?.toLowerCase().includes('execute')
    );
    
    if (executeButtons.length > 0) {
        // Test if buttons are clickable (not disabled)
        const enabledButtons = executeButtons.filter(btn => !btn.disabled);
        return `Found ${executeButtons.length} execute buttons (${enabledButtons.length} enabled)`;
    }
    
    // If no execute buttons, check if we're on the right page
    const isPromptsPage = window.location.pathname.includes('/prompts') || 
                         document.title.toLowerCase().includes('prompt');
    
    if (!isPromptsPage) {
        return 'Not on prompts page - navigate to prompts to test execute buttons';
    }
    
    return false;
});

// STEP 5: Create Interactive Test Function
jsxTest.section('STEP 5: INTERACTIVE TESTING');

window.testExecuteButtonNow = function() {
    console.log('%c🧪 TESTING EXECUTE BUTTON FUNCTIONALITY', 'color: #ca8a04; font-size: 16px; font-weight: bold;');
    
    const executeButtons = Array.from(document.querySelectorAll('button')).filter(btn => 
        btn.textContent.toLowerCase().includes('execute')
    );
    
    if (executeButtons.length === 0) {
        console.log('%c❌ No Execute buttons found!', 'color: #dc2626; font-weight: bold;');
        console.log('%c💡 Navigate to: /prompts or /dashboard first', 'color: #3b82f6;');
        return;
    }
    
    console.log(`%c✅ Found ${executeButtons.length} Execute buttons`, 'color: #16a34a; font-weight: bold;');
    
    executeButtons.forEach((button, index) => {
        console.log(`%cButton ${index + 1}: "${button.textContent.trim()}"`, 'color: #3b82f6;');
        
        // Add click monitoring
        button.addEventListener('click', function(event) {
            const originalUrl = window.location.href;
            console.log(`%c🖱️ EXECUTE BUTTON CLICKED!`, 'color: #ca8a04; font-weight: bold;');
            console.log(`   Button: "${this.textContent.trim()}"`);
            console.log(`   URL before: ${originalUrl}`);
            
            setTimeout(() => {
                const newUrl = window.location.href;
                console.log(`   URL after: ${newUrl}`);
                
                if (newUrl !== originalUrl) {
                    if (newUrl.includes('/execute')) {
                        console.log(`%c✅ SUCCESS: Navigated to execution page!`, 'color: #16a34a; font-weight: bold;');
                        console.log(`%c🎯 NEXT: Test AI functionality on this page`, 'color: #3b82f6; font-weight: bold;');
                    } else if (newUrl.includes('/dashboard')) {
                        console.log(`%c❌ ISSUE: Still redirecting to dashboard`, 'color: #dc2626; font-weight: bold;');
                    } else {
                        console.log(`%c⚠️ UNEXPECTED: Navigated to: ${newUrl}`, 'color: #ca8a04; font-weight: bold;');
                    }
                } else {
                    console.log(`%c⚠️ NO NAVIGATION: URL did not change`, 'color: #ca8a04; font-weight: bold;');
                }
            }, 200);
        }, { once: false });
    });
    
    console.log('%c💡 Execute button monitoring enabled. Click an Execute button now!', 'color: #3b82f6; font-weight: bold;');
};

// STEP 6: Final Results
setTimeout(() => {
    jsxTest.section('STEP 6: VERIFICATION RESULTS');
    
    console.log(`\n%c📊 JSX RUNTIME FIX VERIFICATION SUMMARY`, 'color: #1f2937; font-size: 18px; font-weight: bold; background: #f3f4f6; padding: 10px;');
    console.log(`%c✅ Tests Passed: ${jsxTest.passed}`, 'color: #16a34a; font-weight: bold;');
    console.log(`%c❌ Tests Failed: ${jsxTest.failed}`, 'color: #dc2626; font-weight: bold;');
    
    const successRate = Math.round((jsxTest.passed / (jsxTest.passed + jsxTest.failed)) * 100);
    console.log(`%c📈 Success Rate: ${successRate}%`, successRate >= 80 ? 'color: #16a34a; font-weight: bold;' : 'color: #ca8a04; font-weight: bold;');
    
    if (jsxTest.failed > 0) {
        console.log(`\n%c🚨 FAILED TESTS:`, 'color: #dc2626; font-weight: bold;');
        jsxTest.results.filter(r => r.status === 'FAIL' || r.status === 'ERROR').forEach(result => {
            console.log(`%c   - ${result.test}: ${result.details}`, 'color: #dc2626;');
        });
    }
    
    console.log(`\n%c🎯 NEXT STEPS:`, 'color: #3b82f6; font-size: 16px; font-weight: bold;');
    
    if (successRate >= 80) {
        console.log(`%c✅ JSX RUNTIME FIX SUCCESSFUL!`, 'color: #16a34a; font-weight: bold;');
        console.log(`%c1. Navigate to Prompts page if not already there`, 'color: #3b82f6;');
        console.log(`%c2. Run: testExecuteButtonNow()`, 'color: #3b82f6;');
        console.log(`%c3. Click an Execute button`, 'color: #3b82f6;');
        console.log(`%c4. Verify navigation to execution page`, 'color: #3b82f6;');
        console.log(`%c5. Test AI functionality`, 'color: #3b82f6;');
    } else {
        console.log(`%c❌ JSX RUNTIME STILL HAS ISSUES`, 'color: #dc2626; font-weight: bold;');
        console.log(`%c1. Try hard refresh again: Ctrl+Shift+R`, 'color: #ca8a04;');
        console.log(`%c2. Clear all browser data for this site`, 'color: #ca8a04;');
        console.log(`%c3. Try incognito/private mode`, 'color: #ca8a04;');
        console.log(`%c4. Report the failed tests above`, 'color: #ca8a04;');
    }
    
    console.log(`\n%c🔧 AVAILABLE FUNCTIONS:`, 'color: #1f2937; font-weight: bold;');
    console.log(`%c• testExecuteButtonNow() - Test Execute button functionality`, 'color: #3b82f6;');
    
}, 1000);

console.log('\n%c🎯 JSX RUNTIME FIX VERIFICATION COMPLETE! Check results above.', 'color: #16a34a; font-size: 16px; font-weight: bold;');
