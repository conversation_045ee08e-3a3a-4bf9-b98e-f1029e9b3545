/**
 * Lighthouse Performance Tests
 * 
 * Automated Lighthouse audits to ensure performance, accessibility, 
 * best practices, and SEO standards are maintained.
 * 
 * @module LighthousePerformanceTests
 * @version 1.0.0
 */

import { describe, it, expect, beforeAll, afterAll } from 'vitest';
import lighthouse from 'lighthouse';
import * as chromeLauncher from 'chrome-launcher';
import { readFileSync } from 'fs';
import { join } from 'path';

// Performance budget configuration
const performanceBudget = JSON.parse(
  readFileSync(join(__dirname, '../../../performance-budget.json'), 'utf-8')
);

interface LighthouseResult {
  lhr: {
    categories: {
      performance: { score: number };
      accessibility: { score: number };
      'best-practices': { score: number };
      seo: { score: number };
      pwa?: { score: number };
    };
    audits: {
      [key: string]: {
        score: number | null;
        numericValue?: number;
        displayValue?: string;
      };
    };
  };
}

interface PerformanceMetrics {
  performance: number;
  accessibility: number;
  bestPractices: number;
  seo: number;
  pwa?: number;
  metrics: {
    firstContentfulPaint: number;
    largestContentfulPaint: number;
    firstInputDelay: number;
    cumulativeLayoutShift: number;
    speedIndex: number;
    totalBlockingTime: number;
  };
}

describe('Lighthouse Performance Tests', () => {
  let chrome: chromeLauncher.LaunchedChrome;
  const testUrl = process.env.TEST_URL || 'http://localhost:4173'; // Vite preview server

  beforeAll(async () => {
    // Launch Chrome for testing
    chrome = await chromeLauncher.launch({
      chromeFlags: [
        '--headless',
        '--disable-gpu',
        '--no-sandbox',
        '--disable-dev-shm-usage',
        '--disable-extensions',
        '--disable-background-timer-throttling',
        '--disable-backgrounding-occluded-windows',
        '--disable-renderer-backgrounding',
      ],
    });
  }, 30000);

  afterAll(async () => {
    if (chrome) {
      await chrome.kill();
    }
  });

  const runLighthouseAudit = async (url: string): Promise<PerformanceMetrics> => {
    const options = {
      logLevel: 'info' as const,
      output: 'json' as const,
      onlyCategories: ['performance', 'accessibility', 'best-practices', 'seo', 'pwa'],
      port: chrome.port,
      settings: {
        // Simulate mobile device for consistent testing
        formFactor: 'mobile' as const,
        throttling: {
          rttMs: 150,
          throughputKbps: 1638.4,
          cpuSlowdownMultiplier: 4,
        },
        screenEmulation: {
          mobile: true,
          width: 375,
          height: 667,
          deviceScaleFactor: 2,
        },
        emulatedUserAgent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15',
      },
    };

    const result = await lighthouse(url, options) as LighthouseResult;
    
    return {
      performance: Math.round((result.lhr.categories.performance?.score || 0) * 100),
      accessibility: Math.round((result.lhr.categories.accessibility?.score || 0) * 100),
      bestPractices: Math.round((result.lhr.categories['best-practices']?.score || 0) * 100),
      seo: Math.round((result.lhr.categories.seo?.score || 0) * 100),
      pwa: result.lhr.categories.pwa ? Math.round(result.lhr.categories.pwa.score * 100) : undefined,
      metrics: {
        firstContentfulPaint: result.lhr.audits['first-contentful-paint']?.numericValue || 0,
        largestContentfulPaint: result.lhr.audits['largest-contentful-paint']?.numericValue || 0,
        firstInputDelay: result.lhr.audits['max-potential-fid']?.numericValue || 0,
        cumulativeLayoutShift: result.lhr.audits['cumulative-layout-shift']?.numericValue || 0,
        speedIndex: result.lhr.audits['speed-index']?.numericValue || 0,
        totalBlockingTime: result.lhr.audits['total-blocking-time']?.numericValue || 0,
      },
    };
  };

  describe('Performance Score Validation', () => {
    it('should meet performance score threshold', async () => {
      const metrics = await runLighthouseAudit(testUrl);
      
      const threshold = performanceBudget.lighthouse.performance;
      expect(metrics.performance).toBeGreaterThanOrEqual(threshold);
      
      console.log(`Performance Score: ${metrics.performance}/100 (threshold: ${threshold})`);
    }, 60000);

    it('should meet accessibility score threshold', async () => {
      const metrics = await runLighthouseAudit(testUrl);
      
      const threshold = performanceBudget.lighthouse.accessibility;
      expect(metrics.accessibility).toBeGreaterThanOrEqual(threshold);
      
      console.log(`Accessibility Score: ${metrics.accessibility}/100 (threshold: ${threshold})`);
    }, 60000);

    it('should meet best practices score threshold', async () => {
      const metrics = await runLighthouseAudit(testUrl);
      
      const threshold = performanceBudget.lighthouse['best-practices'];
      expect(metrics.bestPractices).toBeGreaterThanOrEqual(threshold);
      
      console.log(`Best Practices Score: ${metrics.bestPractices}/100 (threshold: ${threshold})`);
    }, 60000);

    it('should meet SEO score threshold', async () => {
      const metrics = await runLighthouseAudit(testUrl);
      
      const threshold = performanceBudget.lighthouse.seo;
      expect(metrics.seo).toBeGreaterThanOrEqual(threshold);
      
      console.log(`SEO Score: ${metrics.seo}/100 (threshold: ${threshold})`);
    }, 60000);
  });

  describe('Core Web Vitals Validation', () => {
    it('should meet First Contentful Paint threshold', async () => {
      const metrics = await runLighthouseAudit(testUrl);
      
      const threshold = performanceBudget.budgets[1].timings.find(
        t => t.metric === 'first-contentful-paint'
      )?.budget || 1800;
      
      expect(metrics.metrics.firstContentfulPaint).toBeLessThanOrEqual(threshold);
      
      console.log(`First Contentful Paint: ${metrics.metrics.firstContentfulPaint}ms (threshold: ${threshold}ms)`);
    }, 60000);

    it('should meet Largest Contentful Paint threshold', async () => {
      const metrics = await runLighthouseAudit(testUrl);
      
      const threshold = performanceBudget.budgets[1].timings.find(
        t => t.metric === 'largest-contentful-paint'
      )?.budget || 2500;
      
      expect(metrics.metrics.largestContentfulPaint).toBeLessThanOrEqual(threshold);
      
      console.log(`Largest Contentful Paint: ${metrics.metrics.largestContentfulPaint}ms (threshold: ${threshold}ms)`);
    }, 60000);

    it('should meet Cumulative Layout Shift threshold', async () => {
      const metrics = await runLighthouseAudit(testUrl);
      
      const threshold = performanceBudget.budgets[1].timings.find(
        t => t.metric === 'cumulative-layout-shift'
      )?.budget || 0.1;
      
      expect(metrics.metrics.cumulativeLayoutShift).toBeLessThanOrEqual(threshold);
      
      console.log(`Cumulative Layout Shift: ${metrics.metrics.cumulativeLayoutShift} (threshold: ${threshold})`);
    }, 60000);

    it('should meet Total Blocking Time threshold', async () => {
      const metrics = await runLighthouseAudit(testUrl);
      
      const threshold = performanceBudget.budgets[1].timings.find(
        t => t.metric === 'total-blocking-time'
      )?.budget || 200;
      
      expect(metrics.metrics.totalBlockingTime).toBeLessThanOrEqual(threshold);
      
      console.log(`Total Blocking Time: ${metrics.metrics.totalBlockingTime}ms (threshold: ${threshold}ms)`);
    }, 60000);

    it('should meet Speed Index threshold', async () => {
      const metrics = await runLighthouseAudit(testUrl);
      
      const threshold = performanceBudget.budgets[1].timings.find(
        t => t.metric === 'speed-index'
      )?.budget || 3000;
      
      expect(metrics.metrics.speedIndex).toBeLessThanOrEqual(threshold);
      
      console.log(`Speed Index: ${metrics.metrics.speedIndex}ms (threshold: ${threshold}ms)`);
    }, 60000);
  });

  describe('Progressive Web App Validation', () => {
    it('should meet PWA score threshold if applicable', async () => {
      const metrics = await runLighthouseAudit(testUrl);
      
      if (metrics.pwa !== undefined) {
        const threshold = performanceBudget.lighthouse.pwa;
        expect(metrics.pwa).toBeGreaterThanOrEqual(threshold);
        
        console.log(`PWA Score: ${metrics.pwa}/100 (threshold: ${threshold})`);
      } else {
        console.log('PWA category not applicable for this application');
      }
    }, 60000);
  });

  describe('Performance Regression Detection', () => {
    it('should detect performance regressions', async () => {
      const metrics = await runLighthouseAudit(testUrl);
      
      // Store baseline metrics (in real implementation, this would be stored persistently)
      const baseline = {
        performance: 90,
        firstContentfulPaint: 1500,
        largestContentfulPaint: 2000,
        speedIndex: 2500,
      };

      // Check for regressions (allow 10% tolerance)
      const performanceRegression = (baseline.performance - metrics.performance) / baseline.performance;
      expect(performanceRegression).toBeLessThan(0.1); // Less than 10% regression

      const fcpRegression = (metrics.metrics.firstContentfulPaint - baseline.firstContentfulPaint) / baseline.firstContentfulPaint;
      expect(fcpRegression).toBeLessThan(0.2); // Less than 20% regression

      const lcpRegression = (metrics.metrics.largestContentfulPaint - baseline.largestContentfulPaint) / baseline.largestContentfulPaint;
      expect(lcpRegression).toBeLessThan(0.2); // Less than 20% regression

      console.log('Performance Regression Analysis:');
      console.log(`Performance Score Change: ${performanceRegression > 0 ? '+' : ''}${(performanceRegression * 100).toFixed(1)}%`);
      console.log(`FCP Change: ${fcpRegression > 0 ? '+' : ''}${(fcpRegression * 100).toFixed(1)}%`);
      console.log(`LCP Change: ${lcpRegression > 0 ? '+' : ''}${(lcpRegression * 100).toFixed(1)}%`);
    }, 60000);
  });

  describe('Mobile Performance Validation', () => {
    it('should perform well on mobile devices', async () => {
      // This test uses mobile emulation settings defined in runLighthouseAudit
      const metrics = await runLighthouseAudit(testUrl);
      
      // Mobile-specific thresholds (typically stricter)
      expect(metrics.performance).toBeGreaterThanOrEqual(85); // Slightly lower threshold for mobile
      expect(metrics.metrics.firstContentfulPaint).toBeLessThanOrEqual(2000); // 2s for mobile
      expect(metrics.metrics.largestContentfulPaint).toBeLessThanOrEqual(3000); // 3s for mobile
      
      console.log('Mobile Performance Metrics:');
      console.log(`Performance: ${metrics.performance}/100`);
      console.log(`FCP: ${metrics.metrics.firstContentfulPaint}ms`);
      console.log(`LCP: ${metrics.metrics.largestContentfulPaint}ms`);
    }, 60000);
  });

  describe('Bundle Size Validation', () => {
    it('should validate JavaScript bundle size through Lighthouse', async () => {
      const result = await lighthouse(testUrl, {
        logLevel: 'info',
        output: 'json',
        onlyAudits: ['unused-javascript', 'unminified-javascript', 'legacy-javascript'],
        port: chrome.port,
      }) as LighthouseResult;

      // Check for unused JavaScript
      const unusedJsScore = result.lhr.audits['unused-javascript']?.score;
      if (unusedJsScore !== null) {
        expect(unusedJsScore).toBeGreaterThanOrEqual(0.8); // 80% or better
      }

      // Check for unminified JavaScript
      const unminifiedJsScore = result.lhr.audits['unminified-javascript']?.score;
      if (unminifiedJsScore !== null) {
        expect(unminifiedJsScore).toBe(1); // Should be 100% (fully minified)
      }

      // Check for legacy JavaScript
      const legacyJsScore = result.lhr.audits['legacy-javascript']?.score;
      if (legacyJsScore !== null) {
        expect(legacyJsScore).toBeGreaterThanOrEqual(0.9); // 90% or better
      }

      console.log('Bundle Analysis:');
      console.log(`Unused JavaScript Score: ${unusedJsScore ? (unusedJsScore * 100).toFixed(0) : 'N/A'}/100`);
      console.log(`Unminified JavaScript Score: ${unminifiedJsScore ? (unminifiedJsScore * 100).toFixed(0) : 'N/A'}/100`);
      console.log(`Legacy JavaScript Score: ${legacyJsScore ? (legacyJsScore * 100).toFixed(0) : 'N/A'}/100`);
    }, 60000);
  });
});
