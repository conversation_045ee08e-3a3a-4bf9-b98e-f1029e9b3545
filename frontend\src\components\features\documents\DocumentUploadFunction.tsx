import React, { useState, useRef } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Upload, File, X, CheckCircle, AlertCircle } from 'lucide-react';
import { Button } from '@/components/common/Button';
import { useSuccessToast, useErrorToast } from '@/components/common/Toast';
import { httpsCallable } from 'firebase/functions';
import { functions } from '@/config/firebase';
import { LoadingSpinner } from '@/components/common/LoadingSpinner';

interface UploadFile {
  file: File;
  id: string;
  status: 'pending' | 'uploading' | 'processing' | 'completed' | 'error';
  progress: number;
  error?: string;
  documentId?: string;
}

interface DocumentUploadFunctionProps {
  onUploadComplete?: (documentId: string) => void;
  maxFiles?: number;
  className?: string;
}

export const DocumentUploadFunction: React.FC<DocumentUploadFunctionProps> = ({
  onUploadComplete,
  maxFiles = 5,
  className = ''
}) => {
  const { currentUser } = useAuth();
  const [uploadFiles, setUploadFiles] = useState<UploadFile[]>([]);
  const [isDragOver, setIsDragOver] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const successToast = useSuccessToast();
  const errorToast = useErrorToast();

  const uploadDocumentFunction = httpsCallable(functions, 'upload_document_via_function');

  const validateFile = (file: File): string | null => {
    // Check file type
    const allowedTypes = [
      'application/pdf',
      'text/plain',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'text/markdown'
    ];

    if (!allowedTypes.includes(file.type)) {
      return 'File type not supported. Please upload PDF, TXT, DOC, DOCX, or MD files.';
    }

    // Check file size (10MB limit)
    if (file.size > 10 * 1024 * 1024) {
      return 'File size must be less than 10MB.';
    }

    return null;
  };

  const fileToBase64 = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => {
        const result = reader.result as string;
        // Remove the data URL prefix (e.g., "data:application/pdf;base64,")
        const base64Data = result.split(',')[1];
        resolve(base64Data);
      };
      reader.onerror = error => reject(error);
    });
  };

  const handleFileSelect = (files: FileList | null) => {
    if (!files || !currentUser) return;

    const newFiles: UploadFile[] = [];
    const currentFileCount = uploadFiles.length;

    for (let i = 0; i < files.length && newFiles.length + currentFileCount < maxFiles; i++) {
      const file = files[i];
      const validationError = validateFile(file);

      if (validationError) {
        errorToast('Invalid file', validationError);
        continue;
      }

      newFiles.push({
        file,
        id: `${Date.now()}-${i}`,
        status: 'pending',
        progress: 0
      });
    }

    if (newFiles.length + currentFileCount >= maxFiles) {
      errorToast('Too many files', `Maximum ${maxFiles} files allowed.`);
    }

    setUploadFiles(prev => [...prev, ...newFiles]);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    handleFileSelect(e.dataTransfer.files);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const removeFile = (id: string) => {
    setUploadFiles(prev => prev.filter(f => f.id !== id));
  };

  const uploadFile = async (uploadFile: UploadFile) => {
    if (!currentUser) return;

    try {
      // Update status to uploading
      setUploadFiles(prev => prev.map(f =>
        f.id === uploadFile.id ? { ...f, status: 'uploading' as const, progress: 10 } : f
      ));

      // Convert file to base64
      const fileData = await fileToBase64(uploadFile.file);

      // Update progress
      setUploadFiles(prev => prev.map(f =>
        f.id === uploadFile.id ? { ...f, progress: 30 } : f
      ));

      // Call Firebase Function to upload
      const result = await uploadDocumentFunction({
        file_data: fileData,
        file_name: uploadFile.file.name,
        file_type: uploadFile.file.type
      });

      // Update progress
      setUploadFiles(prev => prev.map(f =>
        f.id === uploadFile.id ? { ...f, progress: 70 } : f
      ));

      const data = result.data as any;

      if (data.success) {
        // Mark as completed
        setUploadFiles(prev => prev.map(f =>
          f.id === uploadFile.id ? {
            ...f,
            status: 'completed' as const,
            progress: 100,
            documentId: data.documentId
          } : f
        ));

        // Show success toast
        successToast(
          'Document uploaded successfully!',
          `${uploadFile.file.name} is now being processed for RAG.`
        );

        if (onUploadComplete) {
          onUploadComplete(data.documentId);
        }
      } else {
        throw new Error(data.error || 'Upload failed');
      }

    } catch (error) {
      console.error('Upload error:', error);
      setUploadFiles(prev => prev.map(f =>
        f.id === uploadFile.id ? {
          ...f,
          status: 'error' as const,
          error: error instanceof Error ? error.message : 'Upload failed'
        } : f
      ));

      errorToast(
        'Upload failed',
        error instanceof Error ? error.message : 'An unexpected error occurred'
      );
    }
  };

  const uploadAllFiles = async () => {
    const pendingFiles = uploadFiles.filter(f => f.status === 'pending');
    
    for (const file of pendingFiles) {
      await uploadFile(file);
      // Small delay between uploads to avoid overwhelming the function
      await new Promise(resolve => setTimeout(resolve, 500));
    }
  };

  const getStatusIcon = (status: UploadFile['status']) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'error':
        return <AlertCircle className="w-5 h-5 text-red-500" />;
      case 'uploading':
      case 'processing':
        return <LoadingSpinner size="sm" />;
      default:
        return <File className="w-5 h-5 text-gray-400" />;
    }
  };

  const getStatusText = (file: UploadFile) => {
    switch (file.status) {
      case 'pending':
        return 'Ready to upload';
      case 'uploading':
        return `Uploading... ${file.progress}%`;
      case 'processing':
        return 'Processing...';
      case 'completed':
        return 'Upload complete';
      case 'error':
        return file.error || 'Upload failed';
      default:
        return '';
    }
  };

  const pendingCount = uploadFiles.filter(f => f.status === 'pending').length;
  const isUploading = uploadFiles.some(f => f.status === 'uploading' || f.status === 'processing');

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Upload Area */}
      <div
        className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
          isDragOver
            ? 'border-blue-500 bg-blue-50'
            : 'border-gray-300 hover:border-gray-400'
        }`}
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
      >
        <Upload className="w-12 h-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">
          Upload Documents for RAG Processing
        </h3>
        <p className="text-sm text-gray-600 mb-4">
          Drag and drop files here, or click to select files
        </p>
        <p className="text-xs text-gray-500 mb-4">
          Supported formats: PDF, TXT, DOC, DOCX, MD (max 10MB each)
        </p>
        <p className="text-xs text-blue-600 mb-4">
          ✨ Using Firebase Function upload (bypasses CORS issues)
        </p>
        <Button
          variant="outline"
          onClick={() => fileInputRef.current?.click()}
          aria-label="Upload documents - Select files to upload"
        >
          Select Files
        </Button>
        <input
          ref={fileInputRef}
          type="file"
          multiple
          accept=".pdf,.txt,.doc,.docx,.md"
          onChange={(e) => handleFileSelect(e.target.files)}
          className="hidden"
          aria-label="File input for document upload"
        />
      </div>

      {/* File List */}
      {uploadFiles.length > 0 && (
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <h4 className="text-sm font-medium text-gray-900">
              Files ({uploadFiles.length}/{maxFiles})
            </h4>
            {pendingCount > 0 && (
              <Button
                size="sm"
                onClick={uploadAllFiles}
                disabled={isUploading}
                aria-label={`Upload ${pendingCount} pending files`}
              >
                {isUploading ? 'Uploading...' : `Upload ${pendingCount} files`}
              </Button>
            )}
          </div>

          {uploadFiles.map((uploadFile) => (
            <div
              key={uploadFile.id}
              className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
            >
              <div className="flex items-center space-x-3 flex-1">
                {getStatusIcon(uploadFile.status)}
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900 truncate">
                    {uploadFile.file.name}
                  </p>
                  <p className="text-xs text-gray-500">
                    {(uploadFile.file.size / 1024 / 1024).toFixed(2)} MB • {getStatusText(uploadFile)}
                  </p>
                  {uploadFile.status === 'uploading' && (
                    <div className="w-full bg-gray-200 rounded-full h-1 mt-1">
                      <div
                        className="bg-blue-600 h-1 rounded-full transition-all duration-300"
                        style={{ width: `${uploadFile.progress}%` }}
                      />
                    </div>
                  )}
                </div>
              </div>
              
              {uploadFile.status === 'pending' && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => removeFile(uploadFile.id)}
                  aria-label={`Remove ${uploadFile.file.name}`}
                >
                  <X className="w-4 h-4" />
                </Button>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};
