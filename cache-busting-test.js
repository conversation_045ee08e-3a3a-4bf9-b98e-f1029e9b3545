/**
 * 🚀 CACHE-BUSTING VERIFICATION SCRIPT
 * 
 * This script forces a fresh load of all JavaScript files and verifies the JSX runtime fix.
 * 
 * INSTRUCTIONS:
 * 1. Open https://rag-prompt-library.web.app/ in your browser
 * 2. Open Developer Tools (F12) → Console tab
 * 3. Copy and paste this ENTIRE script
 * 4. Press Enter to run
 * 5. The script will force reload the page with cache busting
 */

console.clear();
console.log('%c🚀 CACHE-BUSTING VERIFICATION SCRIPT', 'color: #16a34a; font-size: 20px; font-weight: bold; background: #dcfce7; padding: 10px;');

const cacheBust = {
    log: function(message, type = 'info') {
        const styles = {
            info: 'color: #3b82f6; font-weight: bold;',
            success: 'color: #16a34a; font-weight: bold;',
            error: 'color: #dc2626; font-weight: bold;',
            warning: 'color: #ca8a04; font-weight: bold;'
        };
        console.log(`%c${message}`, styles[type]);
    },
    
    section: function(title) {
        console.log(`\n%c🔧 ${title}`, 'color: #1f2937; font-size: 16px; font-weight: bold; background: #f3f4f6; padding: 8px;');
    }
};

// STEP 1: Check Current JavaScript Files
cacheBust.section('STEP 1: CURRENT JAVASCRIPT FILES ANALYSIS');

const currentScripts = Array.from(document.querySelectorAll('script[src]'));
cacheBust.log(`Found ${currentScripts.length} script tags`);

currentScripts.forEach((script, index) => {
    const src = script.src;
    if (src.includes('assets/js/')) {
        const filename = src.split('/').pop();
        cacheBust.log(`Script ${index + 1}: ${filename}`);
        
        // Check if this is one of the new files with updated hashes
        if (filename.includes('num6gqZz') || filename.includes('DsaXcdDe') || 
            filename.includes('B_Tmb8de') || filename.includes('QYuoXLh0')) {
            cacheBust.log(`  ✅ NEW FILE DETECTED: ${filename}`, 'success');
        } else if (filename.includes('CYcQz2l0') || filename.includes('BPGWJ6pn') || 
                  filename.includes('BrGRJ4D7') || filename.includes('BwN3RYZH')) {
            cacheBust.log(`  ❌ OLD FILE DETECTED: ${filename}`, 'error');
        }
    }
});

// STEP 2: Check for JSX Runtime Error
cacheBust.section('STEP 2: JSX RUNTIME ERROR CHECK');

let jsxErrorDetected = false;

// Override console.error to catch JSX errors
const originalError = console.error;
console.error = function(...args) {
    const errorMessage = args.join(' ');
    if (errorMessage.includes('jsxDEV') || errorMessage.includes('o.jsxDEV is not a function')) {
        jsxErrorDetected = true;
        cacheBust.log('❌ JSX RUNTIME ERROR STILL DETECTED!', 'error');
        cacheBust.log('This means cached files are still being served.', 'error');
    }
    originalError.apply(console, args);
};

// Check if error already occurred
if (window.console.error.toString().includes('jsxDEV')) {
    jsxErrorDetected = true;
    cacheBust.log('❌ JSX RUNTIME ERROR DETECTED IN CONSOLE HISTORY!', 'error');
}

// STEP 3: Force Cache Busting
cacheBust.section('STEP 3: CACHE BUSTING OPERATIONS');

window.forceCacheBust = function() {
    cacheBust.log('🚀 FORCING CACHE BUST...', 'warning');
    
    // Method 1: Hard reload with cache bypass
    cacheBust.log('Method 1: Hard reload with cache bypass', 'info');
    window.location.reload(true);
};

window.clearAllCaches = async function() {
    cacheBust.log('🧹 CLEARING ALL CACHES...', 'warning');
    
    try {
        // Clear service worker caches
        if ('serviceWorker' in navigator) {
            const registrations = await navigator.serviceWorker.getRegistrations();
            for (const registration of registrations) {
                await registration.unregister();
                cacheBust.log('✅ Service worker unregistered', 'success');
            }
        }
        
        // Clear cache storage
        if ('caches' in window) {
            const cacheNames = await caches.keys();
            for (const cacheName of cacheNames) {
                await caches.delete(cacheName);
                cacheBust.log(`✅ Cache deleted: ${cacheName}`, 'success');
            }
        }
        
        // Clear local storage
        localStorage.clear();
        sessionStorage.clear();
        cacheBust.log('✅ Local and session storage cleared', 'success');
        
        cacheBust.log('🔄 Reloading page with fresh cache...', 'info');
        window.location.href = window.location.href + '?cachebust=' + Date.now();
        
    } catch (error) {
        cacheBust.log(`❌ Error clearing caches: ${error.message}`, 'error');
    }
};

// STEP 4: Automatic Detection and Action
cacheBust.section('STEP 4: AUTOMATIC CACHE DETECTION');

setTimeout(() => {
    if (jsxErrorDetected) {
        cacheBust.log('🚨 JSX ERROR DETECTED - CACHE BUSTING REQUIRED!', 'error');
        cacheBust.log('', 'info');
        cacheBust.log('IMMEDIATE ACTIONS:', 'warning');
        cacheBust.log('1. Run: clearAllCaches()', 'warning');
        cacheBust.log('2. Or manually: Ctrl+Shift+R (Windows) / Cmd+Shift+R (Mac)', 'warning');
        cacheBust.log('3. Or run: forceCacheBust()', 'warning');
        
        // Auto-clear caches after 3 seconds
        setTimeout(() => {
            cacheBust.log('🚀 AUTO-CLEARING CACHES IN 3 SECONDS...', 'warning');
            setTimeout(() => {
                window.clearAllCaches();
            }, 3000);
        }, 1000);
        
    } else {
        cacheBust.log('✅ NO JSX ERRORS DETECTED!', 'success');
        cacheBust.log('The cache busting was successful!', 'success');
        
        // Check if React is working
        setTimeout(() => {
            const hasReactContent = document.querySelector('[data-reactroot]') || 
                                   document.querySelectorAll('button').length > 0;
            
            if (hasReactContent) {
                cacheBust.log('✅ REACT APP IS WORKING!', 'success');
                cacheBust.log('', 'info');
                cacheBust.log('🎯 NEXT STEPS:', 'success');
                cacheBust.log('1. Navigate to Prompts page', 'info');
                cacheBust.log('2. Test Execute button functionality', 'info');
                cacheBust.log('3. Verify navigation to execution page', 'info');
            } else {
                cacheBust.log('⚠️ React content not detected yet...', 'warning');
            }
        }, 2000);
    }
}, 1000);

// STEP 5: Create Test Functions
cacheBust.section('STEP 5: AVAILABLE FUNCTIONS');

cacheBust.log('🔧 AVAILABLE CACHE-BUSTING FUNCTIONS:', 'info');
cacheBust.log('• forceCacheBust() - Force hard reload', 'info');
cacheBust.log('• clearAllCaches() - Clear all caches and reload', 'info');
cacheBust.log('', 'info');
cacheBust.log('🎯 IF JSX ERRORS PERSIST:', 'warning');
cacheBust.log('1. Close ALL browser tabs for this site', 'warning');
cacheBust.log('2. Clear browser data (Settings → Privacy → Clear browsing data)', 'warning');
cacheBust.log('3. Restart browser completely', 'warning');
cacheBust.log('4. Open site in incognito/private mode', 'warning');

// STEP 6: File Hash Verification
cacheBust.section('STEP 6: FILE HASH VERIFICATION');

const expectedNewHashes = ['num6gqZz', 'DsaXcdDe', 'B_Tmb8de', 'QYuoXLh0'];
const oldHashes = ['CYcQz2l0', 'BPGWJ6pn', 'BrGRJ4D7', 'BwN3RYZH'];

let newFilesDetected = 0;
let oldFilesDetected = 0;

currentScripts.forEach(script => {
    const src = script.src;
    expectedNewHashes.forEach(hash => {
        if (src.includes(hash)) newFilesDetected++;
    });
    oldHashes.forEach(hash => {
        if (src.includes(hash)) oldFilesDetected++;
    });
});

if (newFilesDetected > 0 && oldFilesDetected === 0) {
    cacheBust.log(`✅ NEW FILES DETECTED: ${newFilesDetected} files`, 'success');
} else if (oldFilesDetected > 0) {
    cacheBust.log(`❌ OLD FILES STILL CACHED: ${oldFilesDetected} files`, 'error');
    cacheBust.log('Cache busting required!', 'error');
} else {
    cacheBust.log('⚠️ Could not determine file versions', 'warning');
}

console.log('\n%c🚀 CACHE-BUSTING VERIFICATION COMPLETE!', 'color: #16a34a; font-size: 16px; font-weight: bold;');
console.log('%c💡 Watch the console output above for automatic cache detection and actions.', 'color: #3b82f6;');
