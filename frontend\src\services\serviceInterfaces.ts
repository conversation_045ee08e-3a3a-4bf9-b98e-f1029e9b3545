/**
 * Unified Service Interfaces
 * Defines standard interfaces that all services should implement for consistency
 */

import { ServiceResponse, ServiceOperation } from './baseService';

// Common service operation types
export interface CreateOperation<TInput, TOutput> extends ServiceOperation<TOutput> {
  input: TInput;
  validate(): boolean;
}

export interface ReadOperation<TOutput> extends ServiceOperation<TOutput> {
  id: string;
}

export interface UpdateOperation<TInput, TOutput> extends ServiceOperation<TOutput> {
  id: string;
  input: Partial<TInput>;
  validate(): boolean;
}

export interface DeleteOperation extends ServiceOperation<boolean> {
  id: string;
}

export interface ListOperation<TOutput> extends ServiceOperation<TOutput[]> {
  filters?: Record<string, any>;
  pagination?: {
    page: number;
    limit: number;
  };
  sorting?: {
    field: string;
    direction: 'asc' | 'desc';
  };
}

export interface SearchOperation<TOutput> extends ServiceOperation<TOutput[]> {
  query: string;
  filters?: Record<string, any>;
  options?: {
    fuzzy?: boolean;
    limit?: number;
    offset?: number;
  };
}

// Standard CRUD service interface
export interface CRUDService<TEntity, TCreateInput, TUpdateInput> {
  create(input: TCreateInput): Promise<ServiceResponse<TEntity>>;
  read(id: string): Promise<ServiceResponse<TEntity | null>>;
  update(id: string, input: Partial<TUpdateInput>): Promise<ServiceResponse<TEntity>>;
  delete(id: string): Promise<ServiceResponse<boolean>>;
  list(options?: {
    filters?: Record<string, any>;
    pagination?: { page: number; limit: number };
    sorting?: { field: string; direction: 'asc' | 'desc' };
  }): Promise<ServiceResponse<TEntity[]>>;
}

// Batch operations interface
export interface BatchService<TEntity, TCreateInput, TUpdateInput> {
  createBatch(inputs: TCreateInput[]): Promise<ServiceResponse<TEntity[]>>;
  updateBatch(updates: Array<{ id: string; input: Partial<TUpdateInput> }>): Promise<ServiceResponse<TEntity[]>>;
  deleteBatch(ids: string[]): Promise<ServiceResponse<boolean>>;
}

// Search service interface
export interface SearchService<TEntity> {
  search(query: string, options?: {
    filters?: Record<string, any>;
    fuzzy?: boolean;
    limit?: number;
    offset?: number;
  }): Promise<ServiceResponse<TEntity[]>>;
  
  searchByField(field: string, value: any, options?: {
    exact?: boolean;
    limit?: number;
  }): Promise<ServiceResponse<TEntity[]>>;
}

// File/Document service interface
export interface DocumentService<TDocument> {
  upload(file: File, metadata?: Record<string, any>): Promise<ServiceResponse<TDocument>>;
  download(id: string): Promise<ServiceResponse<Blob>>;
  getMetadata(id: string): Promise<ServiceResponse<TDocument>>;
  updateMetadata(id: string, metadata: Partial<TDocument>): Promise<ServiceResponse<TDocument>>;
  delete(id: string): Promise<ServiceResponse<boolean>>;
  list(userId?: string): Promise<ServiceResponse<TDocument[]>>;
}

// Authentication service interface
export interface AuthenticationService {
  signIn(credentials: { email: string; password: string }): Promise<ServiceResponse<{ user: any; token: string }>>;
  signUp(userData: { email: string; password: string; displayName?: string }): Promise<ServiceResponse<{ user: any; token: string }>>;
  signOut(): Promise<ServiceResponse<boolean>>;
  getCurrentUser(): Promise<ServiceResponse<any | null>>;
  refreshToken(): Promise<ServiceResponse<string>>;
  resetPassword(email: string): Promise<ServiceResponse<boolean>>;
  updateProfile(updates: Record<string, any>): Promise<ServiceResponse<any>>;
}

// Analytics service interface
export interface AnalyticsService {
  trackEvent(event: string, properties?: Record<string, any>): Promise<ServiceResponse<boolean>>;
  trackPageView(page: string, properties?: Record<string, any>): Promise<ServiceResponse<boolean>>;
  trackUserAction(action: string, userId: string, properties?: Record<string, any>): Promise<ServiceResponse<boolean>>;
  getMetrics(timeRange: { start: Date; end: Date }, metrics: string[]): Promise<ServiceResponse<Record<string, any>>>;
}

// Notification service interface
export interface NotificationService {
  send(notification: {
    to: string | string[];
    subject: string;
    message: string;
    type?: 'email' | 'push' | 'sms';
    priority?: 'low' | 'normal' | 'high';
  }): Promise<ServiceResponse<boolean>>;
  
  sendBatch(notifications: Array<{
    to: string | string[];
    subject: string;
    message: string;
    type?: 'email' | 'push' | 'sms';
    priority?: 'low' | 'normal' | 'high';
  }>): Promise<ServiceResponse<boolean>>;
  
  getDeliveryStatus(notificationId: string): Promise<ServiceResponse<{
    status: 'pending' | 'sent' | 'delivered' | 'failed';
    timestamp: Date;
    error?: string;
  }>>;
}

// Cache service interface (extended)
export interface ExtendedCacheService {
  get<T>(key: string): Promise<T | null>;
  set<T>(key: string, value: T, ttl?: number): Promise<void>;
  delete(key: string): Promise<void>;
  clear(): Promise<void>;
  has(key: string): Promise<boolean>;
  
  // Extended methods
  getMultiple<T>(keys: string[]): Promise<Array<T | null>>;
  setMultiple<T>(entries: Array<{ key: string; value: T; ttl?: number }>): Promise<void>;
  deleteMultiple(keys: string[]): Promise<void>;
  deleteByPattern(pattern: string): Promise<void>;
  getTTL(key: string): Promise<number | null>;
  extend(key: string, additionalTTL: number): Promise<boolean>;
  
  // Cache statistics
  getStats(): Promise<{
    hits: number;
    misses: number;
    hitRate: number;
    size: number;
    memoryUsage?: number;
  }>;
}

// Configuration service interface
export interface ConfigurationService {
  get<T>(key: string, defaultValue?: T): Promise<ServiceResponse<T>>;
  set<T>(key: string, value: T): Promise<ServiceResponse<boolean>>;
  delete(key: string): Promise<ServiceResponse<boolean>>;
  getAll(): Promise<ServiceResponse<Record<string, any>>>;
  
  // Environment-specific configurations
  getForEnvironment<T>(key: string, environment: string, defaultValue?: T): Promise<ServiceResponse<T>>;
  setForEnvironment<T>(key: string, environment: string, value: T): Promise<ServiceResponse<boolean>>;
  
  // Configuration validation
  validate(config: Record<string, any>, schema: any): Promise<ServiceResponse<boolean>>;
}

// Health check service interface
export interface HealthCheckService {
  checkHealth(): Promise<ServiceResponse<{
    status: 'healthy' | 'degraded' | 'unhealthy';
    timestamp: Date;
    services: Record<string, {
      status: 'healthy' | 'degraded' | 'unhealthy';
      responseTime?: number;
      error?: string;
    }>;
  }>>;
  
  checkServiceHealth(serviceName: string): Promise<ServiceResponse<{
    status: 'healthy' | 'degraded' | 'unhealthy';
    responseTime: number;
    error?: string;
  }>>;
}

// Audit service interface
export interface AuditService {
  logAction(action: {
    userId: string;
    action: string;
    resource: string;
    resourceId?: string;
    metadata?: Record<string, any>;
    timestamp?: Date;
  }): Promise<ServiceResponse<boolean>>;
  
  getAuditLog(filters: {
    userId?: string;
    action?: string;
    resource?: string;
    startDate?: Date;
    endDate?: Date;
    limit?: number;
    offset?: number;
  }): Promise<ServiceResponse<Array<{
    id: string;
    userId: string;
    action: string;
    resource: string;
    resourceId?: string;
    metadata?: Record<string, any>;
    timestamp: Date;
  }>>>;
}

// Service registry interface for dependency management
export interface ServiceRegistry {
  register<T>(name: string, service: T): void;
  get<T>(name: string): T | undefined;
  has(name: string): boolean;
  unregister(name: string): boolean;
  list(): string[];
  clear(): void;
}

// Service health monitoring interface
export interface ServiceMonitor {
  startMonitoring(serviceName: string, checkInterval: number): void;
  stopMonitoring(serviceName: string): void;
  getServiceStatus(serviceName: string): {
    isHealthy: boolean;
    lastCheck: Date;
    consecutiveFailures: number;
    averageResponseTime: number;
  } | null;
  
  onServiceDown(serviceName: string, callback: (error: Error) => void): void;
  onServiceUp(serviceName: string, callback: () => void): void;
}

// Export all interfaces for easy importing
export type {
  CRUDService,
  BatchService,
  SearchService,
  DocumentService,
  AuthenticationService,
  AnalyticsService,
  NotificationService,
  ExtendedCacheService,
  ConfigurationService,
  HealthCheckService,
  AuditService,
  ServiceRegistry,
  ServiceMonitor
};
