import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import { Analytics } from '@/pages/Analytics';

// Mock the auth context
const mockCurrentUser = {
  uid: 'test-user-id',
  email: '<EMAIL>',
  displayName: 'Test User'
};

vi.mock('../contexts/AuthContext', () => ({
  useAuth: () => ({
    currentUser: mockCurrentUser,
    loading: false
  })
}));

// Mock the analytics service
const mockAnalyticsData = {
  metrics: {
    totalPrompts: 5,
    totalExecutions: 25,
    totalDocuments: 3,
    avgExecutionTime: 1.5,
    totalCost: 2.45,
    successRate: 96.0
  },
  recentActivity: [
    {
      id: '1',
      type: 'execution_run' as const,
      title: 'Executed prompt',
      timestamp: new Date('2024-01-15T10:30:00Z'),
      cost: 0.05,
      duration: 1.2
    },
    {
      id: '2',
      type: 'document_uploaded' as const,
      title: 'Uploaded test.pdf',
      timestamp: new Date('2024-01-15T09:15:00Z')
    }
  ],
  topPrompts: [
    {
      id: 'prompt-1',
      title: 'Test Prompt 1',
      executionCount: 10,
      lastUsed: new Date('2024-01-15T10:30:00Z'),
      avgCost: 0.05
    },
    {
      id: 'prompt-2',
      title: 'Test Prompt 2',
      executionCount: 8,
      lastUsed: new Date('2024-01-14T15:20:00Z'),
      avgCost: 0.03
    }
  ]
};

vi.mock('../services/analyticsService', () => ({
  analyticsService: {
    getUserAnalytics: vi.fn(() => Promise.resolve(mockAnalyticsData))
  }
}));

// Mock Heroicons
vi.mock('@heroicons/react/24/outline', () => ({
  ChartBarIcon: ({ className }: { className?: string }) => <div data-testid="chart-bar-icon" className={className} />,
  ClockIcon: ({ className }: { className?: string }) => <div data-testid="clock-icon" className={className} />,
  DocumentTextIcon: ({ className }: { className?: string }) => <div data-testid="document-text-icon" className={className} />,
  PlayIcon: ({ className }: { className?: string }) => <div data-testid="play-icon" className={className} />,
  CurrencyDollarIcon: ({ className }: { className?: string }) => <div data-testid="currency-dollar-icon" className={className} />,
  CheckCircleIcon: ({ className }: { className?: string }) => <div data-testid="check-circle-icon" className={className} />,
  ExclamationTriangleIcon: ({ className }: { className?: string }) => <div data-testid="exclamation-triangle-icon" className={className} />
}));

// Mock LoadingSpinner
vi.mock('../components/common/LoadingSpinner', () => ({
  LoadingSpinner: ({ size }: { size?: string }) => <div data-testid="loading-spinner">Loading... ({size})</div>
}));

const renderWithRouter = (component: React.ReactElement) => {
  return render(
    <BrowserRouter>
      {component}
    </BrowserRouter>
  );
};

describe('Analytics Component', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders loading state initially', () => {
    renderWithRouter(<Analytics />);
    expect(screen.getByTestId('loading-spinner')).toBeInTheDocument();
  });

  it('renders analytics data correctly', async () => {
    renderWithRouter(<Analytics />);

    // Wait for data to load
    await waitFor(() => {
      expect(screen.getByText('Analytics')).toBeInTheDocument();
    });

    // Check metrics are displayed
    expect(screen.getByText('Total Prompts')).toBeInTheDocument();
    expect(screen.getByText('5')).toBeInTheDocument();
    expect(screen.getByText('Total Executions')).toBeInTheDocument();
    expect(screen.getByText('25')).toBeInTheDocument();
    expect(screen.getByText('Total Documents')).toBeInTheDocument();
    expect(screen.getByText('3')).toBeInTheDocument();
    expect(screen.getByText('Total Cost')).toBeInTheDocument();
    expect(screen.getByText('$2.450')).toBeInTheDocument();
    expect(screen.getByText('Avg Execution Time')).toBeInTheDocument();
    expect(screen.getByText('1.50s')).toBeInTheDocument();
    expect(screen.getByText('Success Rate')).toBeInTheDocument();
    expect(screen.getByText('96.0%')).toBeInTheDocument();
  });

  it('renders recent activity section', async () => {
    renderWithRouter(<Analytics />);

    await waitFor(() => {
      expect(screen.getByText('Recent Activity')).toBeInTheDocument();
    });

    expect(screen.getByText('Executed prompt')).toBeInTheDocument();
    expect(screen.getByText('Uploaded test.pdf')).toBeInTheDocument();
  });

  it('renders top prompts section', async () => {
    renderWithRouter(<Analytics />);

    await waitFor(() => {
      expect(screen.getByText('Top Prompts')).toBeInTheDocument();
    });

    expect(screen.getByText('Test Prompt 1')).toBeInTheDocument();
    expect(screen.getByText('Test Prompt 2')).toBeInTheDocument();
    expect(screen.getByText('10 executions • Avg: $0.050')).toBeInTheDocument();
    expect(screen.getByText('8 executions • Avg: $0.030')).toBeInTheDocument();
  });

  it('handles error state correctly', async () => {
    // Mock analytics service to throw an error
    const { analyticsService } = await import('../services/analyticsService');
    vi.mocked(analyticsService.getUserAnalytics).mockRejectedValueOnce(new Error('Test error'));

    renderWithRouter(<Analytics />);

    await waitFor(() => {
      expect(screen.getByText('Error Loading Analytics')).toBeInTheDocument();
    });

    expect(screen.getByText('Failed to load analytics data. Please try again.')).toBeInTheDocument();
    expect(screen.getByText('Try Again')).toBeInTheDocument();
  });
});
