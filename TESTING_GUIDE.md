# AI Integration Testing Guide

## Overview

This guide provides comprehensive testing procedures to verify that the AI integration is working correctly after removing mock responses.

## Prerequisites

1. Firebase Functions deployed to australia-southeast1
2. Environment variables configured
3. Frontend built and running
4. Valid API keys for OpenRouter and Google AI

## Testing Methods

### 1. Backend Testing (Python Script)

Run the comprehensive AI integration test:

```bash
# Make the script executable (Linux/Mac)
chmod +x test-ai-integration.py

# Run the test script
python test-ai-integration.py
```

This script tests:
- ✅ OpenRouter API connection
- ✅ Google AI API connection  
- ✅ Prompt generation functionality
- ✅ RAG (Retrieval Augmented Generation) functionality
- ✅ Environment variable configuration

### 2. Frontend Testing

#### A. Test OpenRouter Connection

1. Open your React application
2. Navigate to any prompt execution page
3. Click the **"Test OpenRouter Connection"** button
4. **Expected Result**: Success message with connection details

#### B. Test Prompt Execution

1. Create or select a prompt
2. Fill in any required variables
3. Click **"Execute Prompt"**
4. **Expected Result**: 
   - Real AI-generated response (not mock)
   - Execution metadata (tokens, cost, time)
   - Response saved to execution history

#### C. Test RAG Functionality

1. Enable RAG in prompt settings
2. Upload documents (if not already done)
3. Execute a prompt that would benefit from document context
4. **Expected Result**: 
   - Response incorporates document context
   - RAG metadata shows context documents used

### 3. Firebase Functions Testing

#### A. Direct Function Testing

Test functions directly via Firebase Console:

1. Go to Firebase Console > Functions
2. Select `execute_prompt` function
3. Use the testing interface with sample data:

```json
{
  "data": {
    "promptId": "test-prompt",
    "inputs": {
      "name": "Test User"
    },
    "useRag": false,
    "models": ["nvidia/llama-3.1-nemotron-ultra-253b-v1:free"]
  }
}
```

#### B. HTTP Endpoint Testing

Test the HTTP endpoint for CORS bypass:

```bash
curl -X POST \
  https://australia-southeast1-[PROJECT-ID].cloudfunctions.net/execute_prompt_http \
  -H "Content-Type: application/json" \
  -d '{
    "data": {
      "promptId": "test-prompt",
      "inputs": {"name": "Test User"}
    }
  }'
```

### 4. Monitoring and Logs

#### A. Function Logs

Monitor function execution:

```bash
# View all function logs
firebase functions:log

# View specific function logs
firebase functions:log --only execute_prompt

# Follow logs in real-time
firebase functions:log --follow
```

#### B. Frontend Console

Check browser console for:
- ✅ Successful Firebase Functions calls
- ✅ No mock response messages
- ✅ Proper AI response data
- ❌ No CORS errors
- ❌ No authentication errors

## Expected Behaviors

### ✅ Success Indicators

1. **Real AI Responses**: Responses are contextually relevant and not mock text
2. **Execution Metadata**: Shows real token usage, costs, and execution times
3. **Provider Information**: Displays actual model names and providers
4. **No Mock Messages**: No "mock response" or "Australia migration" messages
5. **Function Logs**: Show successful AI API calls in Firebase logs

### ❌ Failure Indicators

1. **Mock Responses**: Still seeing mock or placeholder text
2. **API Errors**: 401, 403, or 500 errors in console
3. **Timeout Errors**: Functions timing out during AI calls
4. **CORS Errors**: Cross-origin request failures
5. **Missing Metadata**: No token usage or cost information

## Troubleshooting

### Common Issues

#### 1. API Key Issues
```bash
# Check environment variables
firebase functions:config:get

# Re-set API keys if needed
firebase functions:config:set openrouter.api_key="your-key"
```

#### 2. Function Timeout
- Check function logs for slow AI API responses
- Consider increasing function timeout
- Verify network connectivity to AI providers

#### 3. CORS Issues
- Use the HTTP endpoint instead of callable functions
- Check CORS configuration in functions
- Verify frontend Firebase configuration

#### 4. Authentication Issues
- Ensure user is properly authenticated
- Check Firebase Auth configuration
- Verify function security rules

### Performance Testing

#### A. Response Time Testing

Monitor response times for:
- Simple prompts: < 5 seconds
- RAG prompts: < 10 seconds
- Complex prompts: < 15 seconds

#### B. Cost Monitoring

Track costs in:
- Function execution logs
- AI provider dashboards
- Firebase usage metrics

## Test Cases

### Basic Prompt Test
```
Prompt: "Hello, my name is {name}. Please greet me."
Variables: {"name": "Alice"}
Expected: Personalized greeting mentioning "Alice"
```

### RAG Test
```
Prompt: "Based on the uploaded documents, explain {topic}."
Variables: {"topic": "machine learning"}
Expected: Response incorporating document content about ML
```

### Multi-Model Test
```
Models: ["nvidia/llama-3.1-nemotron-ultra-253b-v1:free"]
Expected: Response from specified model with metadata
```

## Success Criteria

The AI integration is considered successful when:

1. ✅ All backend tests pass
2. ✅ Frontend shows real AI responses
3. ✅ No mock response indicators
4. ✅ Proper execution metadata
5. ✅ Function logs show successful AI calls
6. ✅ Response times are reasonable
7. ✅ Costs are tracked accurately
8. ✅ Error handling works properly
