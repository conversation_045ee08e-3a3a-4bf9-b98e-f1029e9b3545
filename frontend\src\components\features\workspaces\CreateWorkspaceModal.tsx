import React, { useState } from 'react';
import type { XMarkIcon, BuildingOfficeIcon, UserGroupIcon, CheckIcon } from '@heroicons/react/24/outline';
import { functions } from '@/config/firebase';
import { httpsCallable } from 'firebase/functions';

interface CreateWorkspaceModalProps {
  isOpen: boolean;
  onClose: () => void;
  onWorkspaceCreated: (workspace: any) => void;
}

interface WorkspacePlan {
  id: string;
  name: string;
  description: string;
  maxMembers: number;
  price: string;
  features: string[];
  recommended?: boolean;
}

const CreateWorkspaceModal: React.FC<CreateWorkspaceModalProps> = ({
  isOpen,
  onClose,
  onWorkspaceCreated
}) => {
  const [step, setStep] = useState(1); // 1: Basic Info, 2: Plan Selection
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    plan: 'free'
  });
  const [creating, setCreating] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  const plans: WorkspacePlan[] = [
    {
      id: 'free',
      name: 'Free',
      description: 'Perfect for small teams getting started',
      maxMembers: 5,
      price: '$0',
      features: [
        'Up to 5 team members',
        'Basic prompt sharing',
        'Community support',
        '1GB storage'
      ]
    },
    {
      id: 'pro',
      name: 'Pro',
      description: 'For growing teams with advanced needs',
      maxMembers: 25,
      price: '$29/month',
      features: [
        'Up to 25 team members',
        'Advanced collaboration tools',
        'Priority support',
        '10GB storage',
        'Version history',
        'Advanced analytics'
      ],
      recommended: true
    },
    {
      id: 'enterprise',
      name: 'Enterprise',
      description: 'For large organizations with custom requirements',
      maxMembers: 100,
      price: 'Contact us',
      features: [
        'Unlimited team members',
        'Custom integrations',
        'Dedicated support',
        'Unlimited storage',
        'Advanced security',
        'Custom branding',
        'SLA guarantee'
      ]
    }
  ];

  const validateForm = () => {
    const newErrors: Record<string, string> = {};
    
    if (!formData.name.trim()) {
      newErrors.name = 'Workspace name is required';
    } else if (formData.name.length < 3) {
      newErrors.name = 'Workspace name must be at least 3 characters';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleNext = () => {
    if (step === 1) {
      if (validateForm()) {
        setStep(2);
      }
    }
  };

  const handleBack = () => {
    if (step === 2) {
      setStep(1);
    }
  };

  // TODO: Add try-catch error handling to this async handler
  const handleCreate = async () => {
    if (!validateForm()) return;
    
    try {
      setCreating(true);
      const createWorkspace = httpsCallable(functions, 'create_workspace');
      
      const result = await createWorkspace({
        name: formData.name,
        description: formData.description,
        plan: formData.plan
      });
      
      const data = result.data as any;
      
      if (data.success) {
        onWorkspaceCreated(data.workspace);
        onClose();
        // Reset form
        setFormData({ name: '', description: '', plan: 'free' });
        setStep(1);
      } else {
        throw new Error(data.error || 'Failed to create workspace');
      }
    } catch (error) {
      console.error('Error creating workspace:', error);
      setErrors({ general: 'Failed to create workspace. Please try again.' });
    } finally {
      setCreating(false);
    }
  };

  const handleClose = () => {
    if (!creating) {
      onClose();
      setStep(1);
      setErrors({});
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        {/* Background overlay */}
        <div 
          className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"
          onClick={handleClose}
        ></div>

        {/* Modal */}
        <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
          {/* Header */}
          <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-3">
                <div className="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-blue-100">
                  <BuildingOfficeIcon className="h-6 w-6 text-blue-600" />
                </div>
                <div>
                  <h3 className="text-lg leading-6 font-medium text-gray-900">
                    Create New Workspace
                  </h3>
                  <p className="text-sm text-gray-500">
                    Step {step} of 2
                  </p>
                </div>
              </div>
              <button
                onClick={handleClose}
                disabled={creating}
                className="text-gray-400 hover:text-gray-600 disabled:opacity-50"
              >
                <XMarkIcon className="h-6 w-6" />
              </button>
            </div>

            {/* Progress Bar */}
            <div className="mb-6">
              <div className="flex items-center">
                <div className={`flex-1 h-2 rounded-full ${step >= 1 ? 'bg-blue-600' : 'bg-gray-200'}`}></div>
                <div className="mx-2"></div>
                <div className={`flex-1 h-2 rounded-full ${step >= 2 ? 'bg-blue-600' : 'bg-gray-200'}`}></div>
              </div>
              <div className="flex justify-between text-xs text-gray-500 mt-1">
                <span>Basic Info</span>
                <span>Plan Selection</span>
              </div>
            </div>

            {/* Step 1: Basic Information */}
            {step === 1 && (
              <div className="space-y-4">
                <div>
                  <label htmlFor="workspace-name" className="block text-sm font-medium text-gray-700">
                    Workspace Name *
                  </label>
                  <input
                    type="text"
                    id="workspace-name"
                    value={formData.name}
                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                    className={`mt-1 block w-full border rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 ${
                      errors.name ? 'border-red-300' : 'border-gray-300'
                    }`}
                    placeholder="e.g., Marketing Team, Product Development"
                  />
                  {errors.name && (
                    <p className="mt-1 text-sm text-red-600">{errors.name}</p>
                  )}
                </div>

                <div>
                  <label htmlFor="workspace-description" className="block text-sm font-medium text-gray-700">
                    Description (Optional)
                  </label>
                  <textarea
                    id="workspace-description"
                    rows={3}
                    value={formData.description}
                    onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                    className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Brief description of your workspace purpose..."
                  />
                </div>
              </div>
            )}

            {/* Step 2: Plan Selection */}
            {step === 2 && (
              <div className="space-y-4">
                <div>
                  <h4 className="text-sm font-medium text-gray-900 mb-3">Choose a Plan</h4>
                  <div className="space-y-3">
                    {plans.map((plan) => (
                      <div
                        key={plan.id}
                        onClick={() => setFormData({ ...formData, plan: plan.id })}
                        className={`relative cursor-pointer rounded-lg border p-4 transition-all ${
                          formData.plan === plan.id
                            ? 'border-blue-500 bg-blue-50 ring-2 ring-blue-500'
                            : 'border-gray-200 hover:border-gray-300'
                        }`}
                      >
                        {plan.recommended && (
                          <div className="absolute -top-2 -right-2 bg-blue-600 text-white text-xs font-bold px-2 py-1 rounded-full">
                            Recommended
                          </div>
                        )}
                        
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center space-x-2">
                              <h5 className="font-medium text-gray-900">{plan.name}</h5>
                              <span className="text-lg font-bold text-gray-900">{plan.price}</span>
                            </div>
                            <p className="text-sm text-gray-600 mt-1">{plan.description}</p>
                            
                            <div className="mt-3">
                              <div className="flex items-center space-x-1 text-sm text-gray-500 mb-2">
                                <UserGroupIcon className="h-4 w-4" />
                                <span>Up to {plan.maxMembers} members</span>
                              </div>
                              
                              <ul className="text-xs text-gray-600 space-y-1">
                                {plan.features.slice(0, 3).map((feature, index) => (
                                  <li key={index} className="flex items-center space-x-1">
                                    <CheckIcon className="h-3 w-3 text-green-500" />
                                    <span>{feature}</span>
                                  </li>
                                ))}
                                {plan.features.length > 3 && (
                                  <li className="text-gray-400">
                                    +{plan.features.length - 3} more features
                                  </li>
                                )}
                              </ul>
                            </div>
                          </div>
                          
                          {formData.plan === plan.id && (
                            <CheckIcon className="h-5 w-5 text-blue-600 mt-1" />
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {/* General Error */}
            {errors.general && (
              <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-md">
                <p className="text-sm text-red-600">{errors.general}</p>
              </div>
            )}
          </div>

          {/* Footer */}
          <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
            {step === 1 ? (
              <button
                type="button"
                onClick={handleNext}
                className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm"
              >
                Next
              </button>
            ) : (
              <button
                type="button"
                onClick={handleCreate}
                disabled={creating}
                className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {creating ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Creating...
                  </>
                ) : (
                  'Create Workspace'
                )}
              </button>
            )}
            
            {step === 2 && (
              <button
                type="button"
                onClick={handleBack}
                disabled={creating}
                className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50"
              >
                Back
              </button>
            )}
            
            <button
              type="button"
              onClick={handleClose}
              disabled={creating}
              className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:w-auto sm:text-sm disabled:opacity-50"
            >
              Cancel
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export { CreateWorkspaceModal };
export default CreateWorkspaceModal;
