import React, { Suspense, lazy, useEffect } from 'react';
import type { Routes, Route, Navigate } from 'react-router-dom';
import { BrowserRouter as Router } from 'react-router-dom';
import type { QueryClientProvider } from '@tanstack/react-query';
// Lazy load dev tools to reduce production bundle size
const ReactQueryDevtools = lazy(() => import('@tanstack/react-query-devtools').then(m => ({ default: m.ReactQueryDevtools })).catch(error => { console.error('Promise rejection:', error); return { default: () => null }; }));
import type { AuthProvider } from './contexts/AuthContext';
import { useAuth } from './contexts/AuthContext';
import type { WorkspaceProvider } from './contexts/WorkspaceContext';
import type { AuthPage } from './components/features/auth/AuthPage';
import type { Layout } from './components/layout/Layout';
import type { LoadingSpinner } from './components/common/LoadingSpinner';
import type { ErrorBoundaryProvider } from './components/common/ErrorBoundaries';
import type { ToastProvider } from './components/common/Toast';
import type { HelpProvider } from './components/features/help/HelpSystem';
// Lazy load monitoring components for better initial performance
const PerformanceMonitor = lazy(() => import('./components/monitoring/PerformanceMonitor').then(m => ({ default: m.PerformanceMonitor })).catch(error => { console.error('Promise rejection:', error); return { default: () => null }; }));
const PerformanceWarning = lazy(() => import('./components/monitoring/PerformanceMonitor').then(m => ({ default: m.PerformanceWarning })).catch(error => { console.error('Promise rejection:', error); return { default: () => null }; }));
const APIPerformanceMonitor = lazy(() => import('./components/monitoring/APIPerformanceMonitor').then(m => ({ default: m.APIPerformanceMonitor })).catch(error => { console.error('Promise rejection:', error); return { default: () => null }; }));
const APIPerformanceAlerts = lazy(() => import('./components/monitoring/APIPerformanceMonitor').then(m => ({ default: m.APIPerformanceAlerts })).catch(error => { console.error('Promise rejection:', error); return { default: () => null }; }));
const WebVitalsDashboard = lazy(() => import('./components/monitoring/WebVitalsDashboard').then(m => ({ default: m.WebVitalsDashboard })).catch(error => { console.error('Promise rejection:', error); return { default: () => null }; }));
const PerformanceDashboard = lazy(() => import('./components/monitoring/PerformanceDashboard'));
const ProductionWebVitalsMonitor = lazy(() => import('./components/monitoring/ProductionWebVitalsMonitor').then(m => ({ default: m.ProductionWebVitalsMonitor })).catch(error => { console.error('Promise rejection:', error); return { default: () => null }; }));
const ProductionErrorMonitor = lazy(() => import('./components/monitoring/ProductionErrorMonitor').then(m => ({ default: m.ProductionErrorMonitor })).catch(error => { console.error('Promise rejection:', error); return { default: () => null }; }));
import { queryClient, backgroundSync } from './lib/queryClient';
import { initializeErrorHandling } from './config/errorConfig';
import { initializeServiceFactory, initializeServices } from './services';
import { initializeRoutePrefetching } from './utils/routePrefetching';

// Lazy load heavy pages for better performance
const Dashboard = lazy(() => import('./pages/Dashboard').then(m => ({ default: m.Dashboard })).catch(error => { console.error('Promise rejection:', error); return { default: () => null }; }));
const Prompts = lazy(() => import('./pages/Prompts').then(m => ({ default: m.Prompts })).catch(error => { console.error('Promise rejection:', error); return { default: () => null }; }));
const Documents = lazy(() => import('./pages/Documents').then(m => ({ default: m.Documents })).catch(error => { console.error('Promise rejection:', error); return { default: () => null }; }));
const Executions = lazy(() => import('./pages/Executions').then(m => ({ default: m.Executions })).catch(error => { console.error('Promise rejection:', error); return { default: () => null }; }));
const ExecutePrompt = lazy(() => import('./pages/ExecutePrompt').then(m => ({ default: m.ExecutePrompt })).catch(error => { console.error('Promise rejection:', error); return { default: () => null }; }));
const Analytics = lazy(() => import('./pages/Analytics').then(m => ({ default: m.Analytics })).catch(error => { console.error('Promise rejection:', error); return { default: () => null }; }));
const Workspaces = lazy(() => import('./pages/Workspaces').then(m => ({ default: m.Workspaces })).catch(error => { console.error('Promise rejection:', error); return { default: () => null }; }));
const Settings = lazy(() => import('./pages/Settings').then(m => ({ default: m.Settings })).catch(error => { console.error('Promise rejection:', error); return { default: () => null }; }));
const BetaSignup = lazy(() => import('./pages/BetaSignup').then(m => ({ default: m.BetaSignup })).catch(error => { console.error('Promise rejection:', error); return { default: () => null }; }));
const BetaProgram = lazy(() => import('./pages/BetaProgram'));
const Marketplace = lazy(() => import('./pages/Marketplace').then(m => ({ default: m.Marketplace })).catch(error => { console.error('Promise rejection:', error); return { default: () => null }; }));
const HelpCenter = lazy(() => import('./components/features/help/HelpCenter').then(m => ({ default: m.HelpCenter })).catch(error => { console.error('Promise rejection:', error); return { default: () => null }; }));

// Protected Route component
const ProtectedRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { currentUser, loading } = useAuth();

  // Initialize route prefetching for authenticated users
  useEffect(() => {
    if (currentUser && !loading) {
      initializeRoutePrefetching(true);
    }
  }, [currentUser, loading]);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return currentUser ? <>{children}</> : <Navigate to="/auth" />;
};

// Public Route component (redirect to dashboard if authenticated)
const PublicRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { currentUser, loading } = useAuth();

  // Initialize route prefetching for unauthenticated users
  useEffect(() => {
    if (!currentUser && !loading) {
      initializeRoutePrefetching(false);
    }
  }, [currentUser, loading]);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return currentUser ? <Navigate to="/" /> : <>{children}</>;
};

function App() {
  // Enable background sync for offline support
  React.useEffect(() => {
    backgroundSync.enable();
    return () => backgroundSync.disable();
  }, []);

  // Initialize error handling system
  useEffect(() => {
    initializeErrorHandling();
  }, []);

  // Initialize service architecture
  useEffect(() => {
    initializeServiceFactory();
    initializeServices();
  }, []);

  return (
    <ErrorBoundaryProvider>
      <QueryClientProvider client={queryClient}>
        <ToastProvider>
          <Router future={{ v7_startTransition: true, v7_relativeSplatPath: true }}>
            <AuthProvider>
              <WorkspaceProvider>
                <HelpProvider>
                <div className="App">
              <Routes>
                {/* Public routes */}
                <Route
                  path="/auth"
                  element={
                    <PublicRoute>
                      <AuthPage />
                    </PublicRoute>
                  }
                />

                {/* Beta signup route */}
                <Route path="/beta-signup" element={
                  <Suspense fallback={<LoadingSpinner />}>
                    <BetaSignup />
                  </Suspense>
                } />

                {/* Beta program route */}
                <Route path="/beta" element={
                  <Suspense fallback={<LoadingSpinner />}>
                    <BetaProgram />
                  </Suspense>
                } />

                {/* Protected routes */}
                <Route
                  path="/"
                  element={
                    <ProtectedRoute>
                      <Layout />
                    </ProtectedRoute>
                  }
                >
                  <Route index element={
                    <Suspense fallback={<LoadingSpinner />}>
                      <Dashboard />
                    </Suspense>
                  } />
                  <Route path="prompts" element={
                    <Suspense fallback={<LoadingSpinner />}>
                      <Prompts />
                    </Suspense>
                  } />
                  <Route path="prompts/:promptId/execute" element={
                    <Suspense fallback={<LoadingSpinner />}>
                      <ExecutePrompt />
                    </Suspense>
                  } />
                  <Route path="documents" element={
                    <Suspense fallback={<LoadingSpinner />}>
                      <Documents />
                    </Suspense>
                  } />
                  <Route path="executions" element={
                    <Suspense fallback={<LoadingSpinner />}>
                      <Executions />
                    </Suspense>
                  } />
                  <Route path="analytics" element={
                    <Suspense fallback={<LoadingSpinner />}>
                      <Analytics />
                    </Suspense>
                  } />
                  <Route path="workspaces" element={
                    <Suspense fallback={<LoadingSpinner />}>
                      <Workspaces />
                    </Suspense>
                  } />
                  <Route path="marketplace" element={
                    <Suspense fallback={<LoadingSpinner />}>
                      <Marketplace />
                    </Suspense>
                  } />
                  <Route path="help" element={
                    <Suspense fallback={<LoadingSpinner />}>
                      <HelpCenter />
                    </Suspense>
                  } />
                  <Route path="settings" element={
                    <Suspense fallback={<LoadingSpinner />}>
                      <Settings />
                    </Suspense>
                  } />
                </Route>

                {/* Catch all route */}
                <Route path="*" element={<Navigate to="/" />} />
              </Routes>
              </div>
              </HelpProvider>
            </WorkspaceProvider>
          </AuthProvider>
        </Router>

        {/* Production Web Vitals Monitoring - Always enabled */}
        <Suspense fallback={null}>
          <ProductionWebVitalsMonitor />
        </Suspense>

        {/* Production Error Monitoring - Always enabled */}
        <Suspense fallback={null}>
          <ProductionErrorMonitor />
        </Suspense>

        {/* Development Performance Monitoring - Only in development */}
        {import.meta.env.DEV && (
          <Suspense fallback={<div>Loading monitoring...</div>}>
            <PerformanceMonitor />
            <PerformanceWarning />
            <APIPerformanceMonitor />
            <APIPerformanceAlerts />
            <WebVitalsDashboard />
            <PerformanceDashboard />
          </Suspense>
        )}
      </ToastProvider>

      {/* React Query DevTools - only in development */}
      {import.meta.env.DEV && (
        <Suspense fallback={null}>
          <ReactQueryDevtools initialIsOpen={false} />
        </Suspense>
      )}
    </QueryClientProvider>
  </ErrorBoundaryProvider>
);
}

export default App;
