/**
 * Specialized Error Boundary Components
 * Provides specific error boundaries for different parts of the application
 */

import React, { ReactNode } from 'react';
import ErrorBoundary, { withErrorBoundary } from './ErrorBoundary';
import { StandardError } from '@/utils/errorHandler';
import { AlertTriangle, RefreshCw, Home, FileX, Wifi, Database } from 'lucide-react';
import { Button } from './Button';

// Route-level error boundary
export const RouteErrorBoundary: React.FC<{ children: ReactNode; routeName?: string }> = ({ 
  children, 
  routeName 
}) => {
  const fallback = (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
      <div className="max-w-md w-full bg-white dark:bg-gray-800 shadow-lg rounded-lg p-6">
        <div className="flex items-center justify-center w-12 h-12 mx-auto bg-red-100 dark:bg-red-900 rounded-full">
          <AlertTriangle className="w-6 h-6 text-red-600 dark:text-red-400" />
        </div>
        <div className="mt-4 text-center">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            Page Error
          </h3>
          <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
            Something went wrong while loading this page. Please try refreshing or go back to the home page.
          </p>
          <div className="mt-6 flex flex-col sm:flex-row gap-3">
            <Button
              variant="primary"
              onClick={() => window.location.reload()}
              leftIcon={<RefreshCw className="w-4 h-4" />}
              className="flex-1"
            >
              Refresh Page
            </Button>
            <Button
              variant="outline"
              onClick={() => window.location.href = '/'}
              leftIcon={<Home className="w-4 h-4" />}
              className="flex-1"
            >
              Go Home
            </Button>
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <ErrorBoundary 
      fallback={fallback}
      component={`Route:${routeName || 'Unknown'}`}
    >
      {children}
    </ErrorBoundary>
  );
};

// Form error boundary
export const FormErrorBoundary: React.FC<{ children: ReactNode; formName?: string }> = ({ 
  children, 
  formName 
}) => {
  const fallback = (
    <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-4">
      <div className="flex">
        <div className="flex-shrink-0">
          <AlertTriangle className="h-5 w-5 text-red-400" />
        </div>
        <div className="ml-3">
          <h3 className="text-sm font-medium text-red-800 dark:text-red-200">
            Form Error
          </h3>
          <div className="mt-2 text-sm text-red-700 dark:text-red-300">
            <p>
              There was an error with this form. Please refresh the page and try again.
            </p>
          </div>
          <div className="mt-4">
            <Button
              size="sm"
              variant="outline"
              onClick={() => window.location.reload()}
              leftIcon={<RefreshCw className="w-4 h-4" />}
            >
              Refresh
            </Button>
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <ErrorBoundary 
      fallback={fallback}
      component={`Form:${formName || 'Unknown'}`}
    >
      {children}
    </ErrorBoundary>
  );
};

// Data loading error boundary
export const DataErrorBoundary: React.FC<{ 
  children: ReactNode; 
  dataType?: string;
  onRetry?: () => void;
}> = ({ children, dataType, onRetry }) => {
  const fallback = (
    <div className="text-center py-12">
      <Database className="mx-auto h-12 w-12 text-gray-400" />
      <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">
        Data Loading Error
      </h3>
      <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
        Failed to load {dataType || 'data'}. Please try again.
      </p>
      <div className="mt-6">
        <Button
          variant="primary"
          onClick={onRetry || (() => window.location.reload())}
          leftIcon={<RefreshCw className="w-4 h-4" />}
        >
          Try Again
        </Button>
      </div>
    </div>
  );

  return (
    <ErrorBoundary 
      fallback={fallback}
      component={`Data:${dataType || 'Unknown'}`}
    >
      {children}
    </ErrorBoundary>
  );
};

// Network error boundary
export const NetworkErrorBoundary: React.FC<{ children: ReactNode }> = ({ children }) => {
  const fallback = (
    <div className="text-center py-12">
      <Wifi className="mx-auto h-12 w-12 text-gray-400" />
      <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">
        Connection Error
      </h3>
      <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
        Unable to connect to the server. Please check your internet connection and try again.
      </p>
      <div className="mt-6">
        <Button
          variant="primary"
          onClick={() => window.location.reload()}
          leftIcon={<RefreshCw className="w-4 h-4" />}
        >
          Retry Connection
        </Button>
      </div>
    </div>
  );

  return (
    <ErrorBoundary 
      fallback={fallback}
      component="Network"
    >
      {children}
    </ErrorBoundary>
  );
};

// File operation error boundary
export const FileErrorBoundary: React.FC<{ children: ReactNode }> = ({ children }) => {
  const fallback = (
    <div className="text-center py-12">
      <FileX className="mx-auto h-12 w-12 text-gray-400" />
      <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">
        File Operation Error
      </h3>
      <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
        There was an error processing the file. Please try again with a different file.
      </p>
      <div className="mt-6">
        <Button
          variant="primary"
          onClick={() => window.location.reload()}
          leftIcon={<RefreshCw className="w-4 h-4" />}
        >
          Try Again
        </Button>
      </div>
    </div>
  );

  return (
    <ErrorBoundary 
      fallback={fallback}
      component="FileOperation"
    >
      {children}
    </ErrorBoundary>
  );
};

// HOCs for common error boundaries
export const withRouteErrorBoundary = <P extends object>(
  Component: React.ComponentType<P>,
  routeName?: string
) => withErrorBoundary(Component, undefined, undefined, `Route:${routeName || Component.displayName || Component.name}`);

export const withFormErrorBoundary = <P extends object>(
  Component: React.ComponentType<P>,
  formName?: string
) => withErrorBoundary(Component, undefined, undefined, `Form:${formName || Component.displayName || Component.name}`);

export const withDataErrorBoundary = <P extends object>(
  Component: React.ComponentType<P>,
  dataType?: string
) => withErrorBoundary(Component, undefined, undefined, `Data:${dataType || Component.displayName || Component.name}`);

// Error boundary provider for nested error boundaries
export const ErrorBoundaryProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  return (
    <RouteErrorBoundary routeName="App">
      <NetworkErrorBoundary>
        {children}
      </NetworkErrorBoundary>
    </RouteErrorBoundary>
  );
};

// Async component error boundary
export const AsyncErrorBoundary: React.FC<{ 
  children: ReactNode; 
  loading?: boolean;
  error?: StandardError | null;
  onRetry?: () => void;
  componentName?: string;
}> = ({ children, loading, error, onRetry, componentName }) => {
  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <AlertTriangle className="mx-auto h-12 w-12 text-red-400" />
        <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">
          {error.userMessage || 'Something went wrong'}
        </h3>
        <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
          Error in {componentName || 'component'}
        </p>
        {error.retryable && onRetry && (
          <div className="mt-6">
            <Button
              variant="primary"
              onClick={onRetry}
              leftIcon={<RefreshCw className="w-4 h-4" />}
            >
              Try Again
            </Button>
          </div>
        )}
      </div>
    );
  }

  return (
    <ErrorBoundary component={componentName}>
      {children}
    </ErrorBoundary>
  );
};
