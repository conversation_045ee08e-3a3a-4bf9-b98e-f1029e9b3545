/**
 * Vite Plugin for Critical Resource Preloading
 * Automatically adds preload links for critical CSS, JS, and other assets
 * to improve LCP (Largest Contentful Paint) performance
 */

import path from 'path';
import fs from 'fs';

export function resourcePreloader(options = {}) {
  const {
    // Critical assets that should be preloaded
    criticalAssets = ['main', 'index', 'vendor-react-core', 'vendor-firebase'],
    // Asset types to preload
    preloadTypes = ['script', 'style'],
    // Maximum number of assets to preload (to avoid overwhelming the browser)
    maxPreloads = 8,
    // Enable/disable the plugin
    enabled = true,
  } = options;

  let config;
  let isProduction = false;
  let storedCriticalAssets = [];

  return {
    name: 'resource-preloader',
    configResolved(resolvedConfig) {
      config = resolvedConfig;
      isProduction = resolvedConfig.command === 'build';
    },

    generateBundle(options, bundle) {
      if (!enabled || !isProduction) return;

      // Find critical assets in the bundle
      const criticalBundleAssets = [];
      
      Object.keys(bundle).forEach(fileName => {
        const asset = bundle[fileName];
        
        // Check if this is a critical asset
        const isCritical = criticalAssets.some(criticalName => 
          fileName.includes(criticalName) || asset.name?.includes(criticalName)
        );
        
        if (isCritical) {
          const ext = path.extname(fileName);
          let assetType = 'script';
          
          if (ext === '.css') assetType = 'style';
          else if (ext === '.woff2') assetType = 'font';
          else if (/\.(png|jpg|jpeg|webp|avif)$/i.test(ext)) assetType = 'image';
          
          if (preloadTypes.includes(assetType)) {
            criticalBundleAssets.push({
              fileName,
              type: assetType,
              size: asset.code?.length || 0,
            });
          }
        }
      });

      // Sort by importance (CSS first, then main JS, then other JS)
      criticalBundleAssets.sort((a, b) => {
        const typeOrder = { style: 0, script: 1, font: 2, image: 3 };
        const aOrder = typeOrder[a.type] || 4;
        const bOrder = typeOrder[b.type] || 4;
        
        if (aOrder !== bOrder) return aOrder - bOrder;
        
        // Within same type, prioritize by name importance
        const aImportance = getAssetImportance(a.fileName);
        const bImportance = getAssetImportance(b.fileName);
        return bImportance - aImportance;
      });

      // Limit to maxPreloads
      const assetsToPreload = criticalBundleAssets.slice(0, maxPreloads);

      // Store the preload information for HTML transformation
      storedCriticalAssets = assetsToPreload;
    },

    transformIndexHtml(html, context) {
      if (!enabled || !isProduction || !storedCriticalAssets.length) return html;

        // Generate preload links
        const preloadLinks = storedCriticalAssets.map(asset => {
          const { fileName, type } = asset;
          const href = `/${fileName}`;
          
          let linkTag = `<link rel="preload" href="${href}" as="${type}"`;
          
          // Add crossorigin for fonts
          if (type === 'font') {
            linkTag += ' crossorigin';
          }
          
          // Add type for fonts
          if (type === 'font' && fileName.endsWith('.woff2')) {
            linkTag += ' type="font/woff2"';
          }
          
          linkTag += ' />';
          return linkTag;
        }).join('\n    ');

        // Insert preload links before the closing </head> tag
        if (preloadLinks) {
          const headCloseIndex = html.indexOf('</head>');
          if (headCloseIndex !== -1) {
            const beforeHead = html.substring(0, headCloseIndex);
            const afterHead = html.substring(headCloseIndex);
            
            return `${beforeHead}    <!-- Critical resource preloads -->\n    ${preloadLinks}\n${afterHead}`;
          }
        }

      return html;
    }
  };
}

/**
 * Get asset importance score for sorting
 */
function getAssetImportance(fileName) {
  if (fileName.includes('index') || fileName.includes('main')) return 100;
  if (fileName.includes('vendor-react-core')) return 90;
  if (fileName.includes('vendor-firebase')) return 80;
  if (fileName.includes('components-common')) return 70;
  if (fileName.includes('vendor')) return 60;
  return 50;
}

/**
 * Development mode resource hints
 * Adds resource hints for development to simulate production behavior
 */
export function devResourceHints() {
  return {
    name: 'dev-resource-hints',
    transformIndexHtml: {
      enforce: 'pre',
      transform(html, context) {
        if (context.server) {
          // Add development resource hints
          const devHints = `
    <!-- Development resource hints -->
    <link rel="preload" href="/src/main.tsx" as="script" />
    <link rel="preload" href="/src/index.css" as="style" />
    <link rel="preload" href="/src/App.tsx" as="script" />`;
          
          const headCloseIndex = html.indexOf('</head>');
          if (headCloseIndex !== -1) {
            const beforeHead = html.substring(0, headCloseIndex);
            const afterHead = html.substring(headCloseIndex);
            return `${beforeHead}${devHints}\n${afterHead}`;
          }
        }
        return html;
      }
    }
  };
}
