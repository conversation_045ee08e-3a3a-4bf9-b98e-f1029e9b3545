/**
 * Reusable Select Component
 * Standardized select/dropdown implementation with consistent styling and behavior
 */

import React, { useState, useRef, useEffect } from 'react';
import type { SelectProps, SelectOption } from '@/components/common/types';

export const Select: React.FC<SelectProps> = ({
  options,
  multiple = false,
  searchable = false,
  clearable = false,
  name,
  label,
  placeholder = 'Select an option...',
  required = false,
  disabled = false,
  _error,
  helperText,
  value,
  onChange,
  onBlur,
  onFocus,
  onSearch,
  noOptionsMessage = 'No options available',
// loadingMessage = 'Loading...', // Unused assignment
  className = '',
  'data-testid': testId,
  id
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [focusedIndex, setFocusedIndex] = useState(-1);
  const selectRef = useRef<HTMLDivElement>(null);
  const searchInputRef = useRef<HTMLInputElement>(null);

  const selectId = id || `select-${name}`;

  // Filter options based on search term
  const filteredOptions = searchable && searchTerm
    ? options.filter(option => 
        option.label.toLowerCase().includes(searchTerm.toLowerCase())
      )
    : options;

  // Group options if they have groups
  const groupedOptions = filteredOptions.reduce((groups, option) => {
    const group = option.group || 'default';
    if (!groups[group]) {
      groups[group] = [];
    }
    groups[group].push(option);
    return groups;
  }, {} as Record<string, SelectOption[]>);

  // Get selected option(s)
  const selectedOptions = multiple 
    ? options.filter(option => Array.isArray(value) && value.includes(option.value))
    : options.find(option => option.value === value);

  // Handle option selection
  const handleOptionSelect = (option: SelectOption) => {
    if (option.disabled) return;

    if (multiple) {
      const currentValues = Array.isArray(value) ? value : [];
      const newValues = currentValues.includes(option.value)
        ? currentValues.filter(v => v !== option.value)
        : [...currentValues, option.value];
      
      if (onChange) {
        onChange(newValues.join(','));
      }
    } else {
      if (onChange) {
        onChange(String(option.value));
      }
      setIsOpen(false);
    }
  };

  // Handle clear selection
  const handleClear = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onChange) {
      onChange('');
    }
  };

  // Handle keyboard navigation
  const handleKeyDown = (e: React.KeyboardEvent) => {
    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        if (!isOpen) {
          setIsOpen(true);
        } else {
          setFocusedIndex(prev => 
            prev < filteredOptions.length - 1 ? prev + 1 : 0
          );
        }
        break;
      case 'ArrowUp':
        e.preventDefault();
        if (isOpen) {
          setFocusedIndex(prev => 
            prev > 0 ? prev - 1 : filteredOptions.length - 1
          );
        }
        break;
      case 'Enter':
        e.preventDefault();
        if (isOpen && focusedIndex >= 0) {
          handleOptionSelect(filteredOptions[focusedIndex]);
        } else {
          setIsOpen(!isOpen);
        }
        break;
      case 'Escape':
        setIsOpen(false);
        setFocusedIndex(-1);
        break;
      case 'Tab':
        setIsOpen(false);
        break;
    }
  };

  // Handle search input
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const term = e.target.value;
    setSearchTerm(term);
    setFocusedIndex(-1);
    if (onSearch) {
      onSearch(term);
    }
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (selectRef.current && !selectRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Focus search input when dropdown opens
  useEffect(() => {
    if (isOpen && searchable && searchInputRef.current) {
      searchInputRef.current.focus();
    }
  }, [isOpen, searchable]);

  // Reset search when dropdown closes
  useEffect(() => {
    if (!isOpen) {
      setSearchTerm('');
      setFocusedIndex(-1);
    }
  }, [isOpen]);

  const baseClasses = 'relative w-full cursor-default rounded-md bg-white py-1.5 pl-3 pr-10 text-left text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-600 disabled:cursor-not-allowed disabled:bg-gray-50 disabled:text-gray-500 disabled:ring-gray-200 dark:bg-gray-900 dark:text-white dark:ring-gray-600 dark:focus:ring-blue-500 dark:disabled:bg-gray-800';
  
  const errorClasses = error 
    ? 'ring-red-300 focus:ring-red-500 dark:ring-red-600 dark:focus:ring-red-500' 
    : '';

  const getDisplayValue = () => {
    if (multiple && Array.isArray(selectedOptions)) {
      return selectedOptions.length > 0 
        ? `${selectedOptions.length} selected`
        : placeholder;
    }
    return selectedOptions ? (selectedOptions as SelectOption).label : placeholder;
  };

  return (
    <div className={`w-full ${className}`}>
      {/* Label */}
      {label && (
        <label 
          htmlFor={selectId}
          className={`block text-sm font-medium leading-6 text-gray-900 dark:text-white mb-2 ${
            required ? "after:content-['*'] after:ml-0.5 after:text-red-500" : ''
          }`}
        >
          {label}
        </label>
      )}

      {/* Select Container */}
      <div ref={selectRef} className="relative">
        <button
          type="button"
          id={selectId}
          onClick={() => !disabled && setIsOpen(!isOpen)}
          onKeyDown={handleKeyDown}
          onBlur={onBlur}
          onFocus={onFocus}
          disabled={disabled}
          className={`${baseClasses} ${errorClasses}`}
          aria-haspopup="listbox"
          aria-expanded={isOpen}
          aria-invalid={error ? 'true' : 'false'}
          data-testid={testId}
        >
          <span className="block truncate">
            {getDisplayValue()}
          </span>
          
          <span className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2">
            {clearable && value && !disabled && (
              <button
                type="button"
                onClick={handleClear}
                className="pointer-events-auto h-5 w-5 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 mr-1"
              >
                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            )}
            <svg className="h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M5.23 7.21a.75.75 0 011.06.02L10 11.168l3.71-3.938a.75.75 0 111.08 1.04l-4.25 4.5a.75.75 0 01-1.08 0l-4.25-4.5a.75.75 0 01.02-1.06z" clipRule="evenodd" />
            </svg>
          </span>
        </button>

        {/* Dropdown */}
        {isOpen && (
          <div className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none dark:bg-gray-800 dark:ring-gray-600">
            {/* Search Input */}
            {searchable && (
              <div className="px-3 py-2 border-b border-gray-200 dark:border-gray-600">
                <input
                  ref={searchInputRef}
                  type="text"
                  value={searchTerm}
                  onChange={handleSearchChange}
                  placeholder="Search options..."
                  className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                />
              </div>
            )}

            {/* Options */}
            {filteredOptions.length === 0 ? (
              <div className="px-3 py-2 text-sm text-gray-500 dark:text-gray-400">
                {noOptionsMessage}
              </div>
            ) : (
              Object.entries(groupedOptions).map(([groupName, groupOptions]) => (
                <div key={groupName}>
                  {groupName !== 'default' && (
                    <div className="px-3 py-1 text-xs font-semibold text-gray-500 uppercase tracking-wide dark:text-gray-400">
                      {groupName}
                    </div>
                  )}
                  {groupOptions.map((option, _index) => {
                    const globalIndex = filteredOptions.indexOf(option);
                    const isSelected = multiple 
                      ? Array.isArray(value) && value.includes(option.value)
                      : option.value === value;
                    const isFocused = globalIndex === focusedIndex;

                    return (
                      <div
                        key={option.value}
                        onClick={() => handleOptionSelect(option)}
                        className={`
                          relative cursor-default select-none py-2 pl-3 pr-9 text-sm
                          ${isFocused ? 'bg-blue-600 text-white' : 'text-gray-900 dark:text-white'}
                          ${option.disabled ? 'opacity-50 cursor-not-allowed' : 'hover:bg-blue-600 hover:text-white'}
                        `}
                      >
                        <span className={`block truncate ${isSelected ? 'font-semibold' : 'font-normal'}`}>
                          {option.label}
                        </span>
                        
                        {isSelected && (
                          <span className={`absolute inset-y-0 right-0 flex items-center pr-4 ${isFocused ? 'text-white' : 'text-blue-600'}`}>
                            <svg className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                              <path fillRule="evenodd" d="M16.704 4.153a.75.75 0 01.143 1.052l-8 10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 011.05-.143z" clipRule="evenodd" />
                            </svg>
                          </span>
                        )}
                      </div>
                    );
                  })}
                </div>
              ))
            )}
          </div>
        )}
      </div>

      {/* Error Message */}
      {error && (
        <p className="mt-2 text-sm text-red-600 dark:text-red-400">
          {error}
        </p>
      )}

      {/* Helper Text */}
      {helperText && !error && (
        <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
          {helperText}
        </p>
      )}
    </div>
  );
};
