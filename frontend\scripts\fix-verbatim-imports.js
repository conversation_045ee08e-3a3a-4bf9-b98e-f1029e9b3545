#!/usr/bin/env node

/**
 * Fix VerbatimModuleSyntax Import Issues
 * Converts regular imports to type-only imports where needed for TypeScript verbatimModuleSyntax
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class VerbatimImportFixer {
  constructor() {
    this.projectRoot = path.join(__dirname, '..');
    this.srcDir = path.join(this.projectRoot, 'src');
    this.fixedCount = 0;
  }

  /**
   * Main execution function
   */
  async run() {
    console.log('🔧 Fixing verbatimModuleSyntax import issues...');
    
    const files = this.findTypeScriptFiles(this.srcDir);
    console.log(`📁 Found ${files.length} TypeScript files to check`);
    
    for (const file of files) {
      await this.fixImportsInFile(file);
    }
    
    console.log(`✅ Fixed imports in ${this.fixedCount} files`);
  }

  /**
   * Fix imports in a single file
   */
  async fixImportsInFile(filePath) {
    try {
      let content = fs.readFileSync(filePath, 'utf8');
      const originalContent = content;
      
      // Fix type-only imports for common patterns
      content = this.fixTypeOnlyImports(content);
      
      if (content !== originalContent) {
        fs.writeFileSync(filePath, content);
        this.fixedCount++;
        console.log(`✅ Fixed: ${path.relative(this.projectRoot, filePath)}`);
      }
    } catch (error) {
      console.error(`❌ Error fixing ${filePath}:`, error.message);
    }
  }

  /**
   * Fix type-only imports
   */
  fixTypeOnlyImports(content) {
    // Common type/interface imports that need type-only syntax
    const typeOnlyPatterns = [
      // BaseComponentProps and other common types
      {
        pattern: /import\s*{\s*([^}]*BaseComponentProps[^}]*)\s*}\s*from\s*['"]([^'"]*types[^'"]*)['"];?/g,
        replacement: "import type { $1 } from '$2';"
      },
      // React types
      {
        pattern: /import\s*{\s*([^}]*(?:FC|ReactNode|ReactElement|ComponentProps|PropsWithChildren)[^}]*)\s*}\s*from\s*['"]react['"];?/g,
        replacement: "import type { $1 } from 'react';"
      },
      // Interface imports from types directories
      {
        pattern: /import\s*{\s*([^}]*(?:Interface|Type|Props)[^}]*)\s*}\s*from\s*['"]([^'"]*\/types\/[^'"]*)['"];?/g,
        replacement: "import type { $1 } from '$2';"
      },
      // Common interface patterns
      {
        pattern: /import\s*{\s*([^}]*(?:Config|Options|Settings|Params|Response|Request)[^}]*)\s*}\s*from\s*['"]([^'"]*)['"];?/g,
        replacement: (match, imports, from) => {
          // Only convert if it looks like types (no lowercase function names)
          if (!/\b[a-z][a-zA-Z]*[a-z]\b/.test(imports)) {
            return `import type { ${imports} } from '${from}';`;
          }
          return match;
        }
      }
    ];

    let result = content;
    
    for (const { pattern, replacement } of typeOnlyPatterns) {
      if (typeof replacement === 'function') {
        result = result.replace(pattern, replacement);
      } else {
        result = result.replace(pattern, replacement);
      }
    }

    // Fix mixed imports (separate types from values)
    result = this.fixMixedImports(result);
    
    return result;
  }

  /**
   * Fix mixed imports by separating types from values
   */
  fixMixedImports(content) {
    // Pattern to match import statements
    const importPattern = /import\s*{\s*([^}]+)\s*}\s*from\s*['"]([^'"]+)['"];?/g;
    
    return content.replace(importPattern, (match, imports, from) => {
      const importItems = imports.split(',').map(item => item.trim());
      const types = [];
      const values = [];
      
      for (const item of importItems) {
        // Check if it's likely a type (starts with capital, ends with common type suffixes)
        if (this.isLikelyType(item)) {
          types.push(item);
        } else {
          values.push(item);
        }
      }
      
      // If we have both types and values, separate them
      if (types.length > 0 && values.length > 0) {
        const typeImport = `import type { ${types.join(', ')} } from '${from}';`;
        const valueImport = `import { ${values.join(', ')} } from '${from}';`;
        return `${typeImport}\n${valueImport}`;
      }
      
      // If only types, make it type-only
      if (types.length > 0 && values.length === 0) {
        return `import type { ${types.join(', ')} } from '${from}';`;
      }
      
      // Otherwise, keep as is
      return match;
    });
  }

  /**
   * Check if an import is likely a type
   */
  isLikelyType(importName) {
    const cleaned = importName.trim();
    
    // Common type patterns
    const typePatterns = [
      /Props$/,           // Component props
      /Type$/,            // Type definitions
      /Interface$/,       // Interfaces
      /Config$/,          // Configuration types
      /Options$/,         // Option types
      /Response$/,        // API response types
      /Request$/,         // API request types
      /^[A-Z][A-Za-z]*$/, // PascalCase (likely types/interfaces)
    ];
    
    // Common value patterns (should NOT be treated as types)
    const valuePatterns = [
      /^use[A-Z]/,        // React hooks
      /^create[A-Z]/,     // Factory functions
      /^get[A-Z]/,        // Getter functions
      /^set[A-Z]/,        // Setter functions
      /^handle[A-Z]/,     // Event handlers
      /^on[A-Z]/,         // Event handlers
    ];
    
    // Check if it's definitely a value
    if (valuePatterns.some(pattern => pattern.test(cleaned))) {
      return false;
    }
    
    // Check if it matches type patterns
    return typePatterns.some(pattern => pattern.test(cleaned));
  }

  /**
   * Find all TypeScript files
   */
  findTypeScriptFiles(dir) {
    const files = [];
    
    if (!fs.existsSync(dir)) {
      return files;
    }
    
    const entries = fs.readdirSync(dir, { withFileTypes: true });
    
    for (const entry of entries) {
      const fullPath = path.join(dir, entry.name);
      
      if (entry.isDirectory()) {
        if (!['node_modules', 'dist', '.git', '__tests__'].includes(entry.name)) {
          files.push(...this.findTypeScriptFiles(fullPath));
        }
      } else if (this.isTypeScriptFile(entry.name)) {
        files.push(fullPath);
      }
    }
    
    return files;
  }

  /**
   * Check if file is a TypeScript file
   */
  isTypeScriptFile(filename) {
    return /\.(ts|tsx)$/.test(filename) && !filename.endsWith('.d.ts');
  }
}

// Run the fixer
const fixer = new VerbatimImportFixer();
fixer.run().catch(console.error);
