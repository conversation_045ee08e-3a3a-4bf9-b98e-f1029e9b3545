name: Performance Budget Check

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  performance-budget:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: frontend/package-lock.json
        
    - name: Install dependencies
      working-directory: ./frontend
      run: npm ci
      
    - name: Build application
      working-directory: ./frontend
      run: npm run build:prod
      
    - name: Install Lighthouse CI
      run: npm install -g @lhci/cli@0.12.x
      
    - name: Run Lighthouse CI
      working-directory: ./frontend
      run: |
        npm run preview &
        sleep 10
        lhci autorun --upload.target=temporary-public-storage
      env:
        LHCI_GITHUB_APP_TOKEN: ${{ secrets.LHCI_GITHUB_APP_TOKEN }}
        
    - name: Check bundle size
      working-directory: ./frontend
      run: npm run check:budget
      
    - name: Comment PR with results
      if: github.event_name == 'pull_request'
      uses: actions/github-script@v7
      with:
        script: |
          const fs = require('fs');
          const path = './frontend/lhci_reports/manifest.json';
          
          if (fs.existsSync(path)) {
            const manifest = JSON.parse(fs.readFileSync(path, 'utf8'));
            const summary = manifest[0]?.summary || {};
            
            const comment = `## 🚀 Performance Budget Check Results
            
            ### Lighthouse Scores
            - **Performance**: ${Math.round((summary.performance || 0) * 100)}/100
            - **Accessibility**: ${Math.round((summary.accessibility || 0) * 100)}/100  
            - **Best Practices**: ${Math.round((summary['best-practices'] || 0) * 100)}/100
            - **SEO**: ${Math.round((summary.seo || 0) * 100)}/100
            
            ### Core Web Vitals
            - **LCP**: ${summary['largest-contentful-paint']?.displayValue || 'N/A'}
            - **FID**: ${summary['first-input-delay']?.displayValue || 'N/A'}
            - **CLS**: ${summary['cumulative-layout-shift']?.displayValue || 'N/A'}
            
            ${summary.performance < 0.9 ? '⚠️ **Performance score below target (90)**' : '✅ Performance score meets target'}
            ${summary['largest-contentful-paint']?.numericValue > 2500 ? '⚠️ **LCP exceeds 2.5s target**' : '✅ LCP within target'}
            `;
            
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: comment
            });
          }
