/**
 * Service Registry
 * Centralized registry for managing service instances and dependencies
 */

import { ServiceRegistry as IServiceRegistry, ServiceMonitor } from './serviceInterfaces';
import { BaseService, ServiceConfig, ServiceDependencies } from './baseService';
import { serviceFactory} from './serviceFactory';

// Service registration metadata
interface ServiceRegistration {
  instance: any;
  config: ServiceConfig;
  dependencies: ServiceDependencies;
  createdAt: Date;
  lastAccessed: Date;
  accessCount: number;
}

// Service health status
interface ServiceHealth {
  isHealthy: boolean;
  lastCheck: Date;
  consecutiveFailures: number;
  averageResponseTime: number;
  lastError?: Error;
}

export class ServiceRegistry implements IServiceRegistry, ServiceMonitor {
  private static instance: ServiceRegistry;
  private services = new Map<string, ServiceRegistration>();
  private healthStatus = new Map<string, ServiceHealth>();
  private healthCheckIntervals = new Map<string, NodeJS.Timeout>();
  private eventListeners = new Map<string, Array<{ event: 'up' | 'down'; callback: () => void }>>();

  private constructor() {}

  public static getInstance(): ServiceRegistry {
    if (!ServiceRegistry.instance) {
      ServiceRegistry.instance = new ServiceRegistry();
    }
    return ServiceRegistry.instance;
  }

  // Service Registry Interface Implementation
  register<T>(name: string, service: T): void {
    const registration: ServiceRegistration = {
      instance: service,
      config: service instanceof BaseService ? (service as any).config : {},
      dependencies: service instanceof BaseService ? (service as any).dependencies : {},
      createdAt: new Date(),
      lastAccessed: new Date(),
      accessCount: 0
    };

    this.services.set(name, registration);
    this.initializeHealthStatus(name);
    
    console.info(`[ServiceRegistry] Registered service: ${name}`);
  }

  get<T>(name: string): T | undefined {
    const registration = this.services.get(name);
    if (!registration) {
      return undefined;
    }

    // Update access tracking
    registration.lastAccessed = new Date();
    registration.accessCount++;

    return registration.instance as T;
  }

  has(name: string): boolean {
    return this.services.has(name);
  }

  unregister(name: string): boolean {
    const existed = this.services.has(name);
    
    if (existed) {
      // Stop health monitoring
      this.stopMonitoring(name);
      
      // Remove service
      this.services.delete(name);
      this.healthStatus.delete(name);
      this.eventListeners.delete(name);
      
      console.info(`[ServiceRegistry] Unregistered service: ${name}`);
    }

    return existed;
  }

  list(): string[] {
    return Array.from(this.services.keys());
  }

  clear(): void {
    // Stop all health monitoring
    for (const name of this.services.keys()) {
      this.stopMonitoring(name);
    }

    this.services.clear();
    this.healthStatus.clear();
    this.eventListeners.clear();
    
    console.info('[ServiceRegistry] Cleared all services');
  }

  // Service Monitor Interface Implementation
  startMonitoring(serviceName: string, checkInterval: number = 30000): void {
    if (!this.services.has(serviceName)) {
      throw new Error(`Service ${serviceName} is not registered`);
    }

    // Stop existing monitoring if any
    this.stopMonitoring(serviceName);

    const intervalId = setInterval(async () => {
      await this.performHealthCheck(serviceName);
    }, checkInterval);

    this.healthCheckIntervals.set(serviceName, intervalId);
    
    console.info(`[ServiceRegistry] Started monitoring service: ${serviceName} (interval: ${checkInterval}ms)`);
  }

  stopMonitoring(serviceName: string): void {
    const intervalId = this.healthCheckIntervals.get(serviceName);
    if (intervalId) {
      clearInterval(intervalId);
      this.healthCheckIntervals.delete(serviceName);
      console.info(`[ServiceRegistry] Stopped monitoring service: ${serviceName}`);
    }
  }

  getServiceStatus(serviceName: string): ServiceHealth | null {
    return this.healthStatus.get(serviceName) || null;
  }

  onServiceDown(serviceName: string, callback: (error: Error) => void): void {
    this.addEventListener(serviceName, 'down', callback);
  }

  onServiceUp(serviceName: string, callback: () => void): void {
    this.addEventListener(serviceName, 'up', callback);
  }

  // Additional registry methods
  getServiceInfo(name: string): ServiceRegistration | undefined {
    return this.services.get(name);
  }

  getAllServiceInfo(): Map<string, ServiceRegistration> {
    return new Map(this.services);
  }

  getServiceMetrics(): Record<string, any> {
    const metrics: Record<string, any> = {};

    for (const [name, registration] of this.services) {
      const health = this.healthStatus.get(name);
      
      metrics[name] = {
        accessCount: registration.accessCount,
        lastAccessed: registration.lastAccessed,
        createdAt: registration.createdAt,
        isHealthy: health?.isHealthy ?? true,
        averageResponseTime: health?.averageResponseTime ?? 0,
        consecutiveFailures: health?.consecutiveFailures ?? 0
      };
    }

    return metrics;
  }

  // Auto-registration methods
  autoRegisterService<T extends BaseService>(
    ServiceClass: new (config?: ServiceConfig, dependencies?: ServiceDependencies) => T,
    name: string,
    config?: ServiceConfig,
    dependencies?: ServiceDependencies
  ): T {
    if (this.has(name)) {
      return this.get<T>(name)!;
    }

    const service = serviceFactory.createService(ServiceClass, name, config);
    this.register(name, service);
    
    return service;
  }

  // Lazy loading service getter
  getOrCreate<T extends BaseService>(
    ServiceClass: new (config?: ServiceConfig, dependencies?: ServiceDependencies) => T,
    name: string,
    config?: ServiceConfig
  ): T {
    return this.autoRegisterService(ServiceClass, name, config);
  }

  // Private helper methods
  private initializeHealthStatus(serviceName: string): void {
    this.healthStatus.set(serviceName, {
      isHealthy: true,
      lastCheck: new Date(),
      consecutiveFailures: 0,
      averageResponseTime: 0
    });
  }

  private async performHealthCheck(serviceName: string): Promise<void> {
    const service = this.get(serviceName);
    const health = this.healthStatus.get(serviceName);
    
    if (!service || !health) {
      return;
    }

    const startTime = Date.now();
    let isHealthy = true;
    let error: Error | undefined;

    try {
      // Perform health check if service has a health check method
      if (typeof (service as any).healthCheck === 'function') {
        await (service as any).healthCheck();
      }
    } catch (err) {
      isHealthy = false;
      error = err as Error;
    }

    const responseTime = Date.now() - startTime;
    
    // Update health status
    const wasHealthy = health.isHealthy;
    health.isHealthy = isHealthy;
    health.lastCheck = new Date();
    health.averageResponseTime = (health.averageResponseTime + responseTime) / 2;
    
    if (isHealthy) {
      health.consecutiveFailures = 0;
    } else {
      health.consecutiveFailures++;
      health.lastError = error;
    }

    // Trigger events if status changed
    if (wasHealthy && !isHealthy) {
      this.triggerEvent(serviceName, 'down', error);
    } else if (!wasHealthy && isHealthy) {
      this.triggerEvent(serviceName, 'up');
    }
  }

  private addEventListener(serviceName: string, event: 'up' | 'down', callback: () => void): void {
    if (!this.eventListeners.has(serviceName)) {
      this.eventListeners.set(serviceName, []);
    }
    
    this.eventListeners.get(serviceName)!.push({ event, callback });
  }

  private triggerEvent(serviceName: string, event: 'up' | 'down', error?: Error): void {
    const listeners = this.eventListeners.get(serviceName) || [];
    
    for (const listener of listeners) {
      if (listener.event === event) {
        try {
          if (event === 'down' && error) {
            listener.callback(error);
          } else {
            listener.callback();
          }
        } catch (err) {
          console.error(`[ServiceRegistry] Error in event listener for ${serviceName}:${event}`, err);
        }
      }
    }
  }
}

// Global service registry instance
export const serviceRegistry = ServiceRegistry.getInstance();

// Convenience functions
export function registerService<T>(name: string, service: T): void {
  serviceRegistry.register(name, service);
}

export function getService<T>(name: string): T | undefined {
  return serviceRegistry.get<T>(name);
}

export function createAndRegisterService<T extends BaseService>(
  ServiceClass: new (config?: ServiceConfig, dependencies?: ServiceDependencies) => T,
  name: string,
  config?: ServiceConfig
): T {
  return serviceRegistry.autoRegisterService(ServiceClass, name, config);
}

// Service initialization helper
export function initializeServices(): void {
  console.info('[ServiceRegistry] Initializing service registry...');
  
  // Auto-register core services here if needed
  // Example:
  // createAndRegisterService(DocumentService, 'DocumentService');
  // createAndRegisterService(TemplateService, 'TemplateService');
  
  console.info('[ServiceRegistry] Service registry initialized');
}
