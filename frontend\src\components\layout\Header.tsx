import React from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Brain, Menu, User, LogOut, Settings, HelpCircle } from 'lucide-react';
import { useHelp } from '@/components/features/help/HelpSystem';

interface HeaderProps {
  onMenuToggle: () => void;
}

export const Header: React.FC<HeaderProps> = ({ onMenuToggle }) => {
  const { currentUser, userProfile, logout } = useAuth();
  const { toggleHelpMode, isHelpMode } = useHelp();

  // TODO: Add try-catch error handling to this async handler
  const handleLogout = async () => {
    try {
      await logout();
    } catch (error) {
      console.error('Failed to log out:', error);
    }
  };

  return (
    <header className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Left side */}
          <div className="flex items-center">
            <button
              onClick={onMenuToggle}
              className="p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500 lg:hidden"
              aria-label="Toggle navigation menu"
              aria-expanded="false"
              type="button"
            >
              <Menu className="h-6 w-6" aria-hidden="true" />
            </button>
            
            <div className="flex items-center ml-4 lg:ml-0">
              <Brain className="h-8 w-8 text-blue-600" />
              <h1 className="ml-2 text-xl font-bold text-gray-900 dark:text-white">
                PromptLibrary
              </h1>
            </div>
          </div>

          {/* Right side */}
          <div className="flex items-center space-x-4">
            {/* Help button */}
            <button
              onClick={toggleHelpMode}
              data-help="help-button"
              className={`p-2 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors ${
                isHelpMode
                  ? 'bg-blue-100 text-blue-600 dark:bg-blue-900 dark:text-blue-300'
                  : 'text-gray-400 hover:text-gray-500 hover:bg-gray-100 dark:hover:bg-gray-700'
              }`}
              title="Toggle Help Mode (Press ? key)"
              aria-label={isHelpMode ? "Exit help mode" : "Enter help mode"}
              aria-pressed={isHelpMode}
              type="button"
            >
              <HelpCircle className="h-6 w-6" aria-hidden="true" />
            </button>

            {/* User menu */}
            <div className="relative group">
              <button
                className="flex items-center space-x-2 p-2 rounded-md text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
                aria-label="User menu"
                aria-expanded="false"
                aria-haspopup="true"
                type="button"
              >
                {userProfile?.photoURL ? (
                  <img
                    src={userProfile.photoURL}
                    alt={`${userProfile?.displayName || currentUser?.email} profile picture`}
                    className="h-8 w-8 rounded-full"
                  />
                ) : (
                  <User className="h-8 w-8 p-1 bg-gray-200 dark:bg-gray-600 rounded-full" aria-hidden="true" />
                )}
                <span className="hidden md:block text-sm font-medium">
                  {userProfile?.displayName || currentUser?.email}
                </span>
              </button>

              {/* Dropdown menu */}
              <div
                className="absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-md shadow-lg ring-1 ring-black ring-opacity-5 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50"
                role="menu"
                aria-orientation="vertical"
                aria-labelledby="user-menu-button"
              >
                <div className="py-1" role="none">
                  <button
                    className="flex items-center w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                    role="menuitem"
                    type="button"
                  >
                    <Settings className="h-4 w-4 mr-2" aria-hidden="true" />
                    Settings
                  </button>
                  <button
                    onClick={handleLogout}
                    className="flex items-center w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                    role="menuitem"
                    type="button"
                  >
                    <LogOut className="h-4 w-4 mr-2" aria-hidden="true" />
                    Sign out
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </header>
  );
};
