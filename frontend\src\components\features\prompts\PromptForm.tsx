import React, { useState, useEffect } from 'react';
import type { Prompt, PromptVariable } from '@/types';

interface PromptFormProps {
  prompt?: Prompt;
  onSave: (promptData: Partial<Prompt>) => void;
  onCancel: () => void;
  loading?: boolean;
}

export const PromptForm: React.FC<PromptFormProps> = ({
  prompt,
  onSave,
  onCancel,
  loading = false
}) => {
  const [formData, setFormData] = useState({
    title: prompt?.title || '',
    description: prompt?.description || '',
    content: prompt?.content || '',
    category: prompt?.category || '',
    tags: prompt?.tags || [],
    variables: prompt?.variables || [],
    isPublic: prompt?.isPublic || false
  });

  const [newTag, setNewTag] = useState('');
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Extract variables from content
  const extractVariablesFromContent = (content: string): string[] => {
    const variableRegex = /\{\{([^}]+)\}\}/g;
    const matches = [];
    let match;
    while ((match = variableRegex.exec(content)) !== null) {
      matches.push(match[1].trim());
    }
    return [...new Set(matches)]; // Remove duplicates
  };

  // Auto-detect variables when content changes
  useEffect(() => {
    const detectedVariables = extractVariablesFromContent(formData.content);
    const existingVariableNames = formData.variables.map(v => v.name);

    // Add new variables that were detected but don't exist yet
    const newVariables = detectedVariables
      .filter(name => !existingVariableNames.includes(name))
      .map(name => ({
        name,
        description: '',
        type: 'text' as const,
        required: false,
        defaultValue: ''
      }));

    if (newVariables.length > 0) {
      setFormData(prev => ({
        ...prev,
        variables: [...prev.variables, ...newVariables]
      }));
    }

    // Remove variables that are no longer in the content
    const filteredVariables = formData.variables.filter(variable =>
      detectedVariables.includes(variable.name)
    );

    if (filteredVariables.length !== formData.variables.length) {
      setFormData(prev => ({
        ...prev,
        variables: filteredVariables
      }));
    }
  }, [formData.content]);

  useEffect(() => {
    if (prompt) {
      setFormData({
        title: prompt.title || '',
        description: prompt.description || '',
        content: prompt.content || '',
        category: prompt.category || '',
        tags: prompt.tags || [],
        variables: prompt.variables || [],
        isPublic: prompt.isPublic || false
      });
    }
  }, [prompt]);

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.title.trim()) {
      newErrors.title = 'Title is required';
    }

    if (!formData.content.trim()) {
      newErrors.content = 'Content is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    onSave(formData);
  };

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  const addTag = () => {
    if (newTag.trim() && !formData.tags.includes(newTag.trim())) {
      handleInputChange('tags', [...formData.tags, newTag.trim()]);
      setNewTag('');
    }
  };

  const removeTag = (tagToRemove: string) => {
    handleInputChange('tags', formData.tags.filter(tag => tag !== tagToRemove));
  };

  const addVariable = () => {
    const newVariable: PromptVariable = {
      name: '',
      description: '',
      type: 'text',
      required: false,
      defaultValue: ''
    };
    handleInputChange('variables', [...formData.variables, newVariable]);
  };

  const updateVariable = (index: number, field: keyof PromptVariable, value: any) => {
    const updatedVariables = [...formData.variables];
    updatedVariables[index] = {
      ...updatedVariables[index],
      [field]: value
    };
    handleInputChange('variables', updatedVariables);
  };

  const removeVariable = (index: number) => {
    handleInputChange('variables', formData.variables.filter((_, i) => i !== index));
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Title */}
      <div>
        <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-1">
          Title *
        </label>
        <input
          type="text"
          id="title"
          value={formData.title}
          onChange={(e) => handleInputChange('title', e.target.value)}
          className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 ${
            errors.title ? 'border-red-500' : 'border-gray-300'
          }`}
          placeholder="Enter prompt title"
          data-testid="title-input"
        />
        {errors.title && (
          <p className="mt-1 text-sm text-red-600" data-testid="title-error">
            {errors.title}
          </p>
        )}
      </div>

      {/* Description */}
      <div>
        <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
          Description
        </label>
        <textarea
          id="description"
          value={formData.description}
          onChange={(e) => handleInputChange('description', e.target.value)}
          rows={3}
          className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
          placeholder="Enter prompt description"
          data-testid="description-input"
        />
      </div>

      {/* Content */}
      <div>
        <label htmlFor="content" className="block text-sm font-medium text-gray-700 mb-1">
          Content *
        </label>
        <textarea
          id="content"
          value={formData.content}
          onChange={(e) => handleInputChange('content', e.target.value)}
          rows={8}
          className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 ${
            errors.content ? 'border-red-500' : 'border-gray-300'
          }`}
          placeholder="Enter your prompt content here..."
          data-testid="content-input"
        />
        {errors.content && (
          <p className="mt-1 text-sm text-red-600" data-testid="content-error">
            {errors.content}
          </p>
        )}
      </div>

      {/* Category */}
      <div>
        <label htmlFor="category" className="block text-sm font-medium text-gray-700 mb-1">
          Category
        </label>
        <select
          id="category"
          value={formData.category}
          onChange={(e) => handleInputChange('category', e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
          data-testid="category-select"
        >
          <option value="">Select a category</option>
          <option value="general">General</option>
          <option value="creative">Creative</option>
          <option value="technical">Technical</option>
          <option value="business">Business</option>
          <option value="educational">Educational</option>
        </select>
      </div>

      {/* Tags */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Tags
        </label>
        <div className="flex flex-wrap gap-2 mb-2">
          {formData.tags.map((tag, index) => (
            <span
              key={index}
              className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
            >
              {tag}
              <button
                type="button"
                onClick={() => removeTag(tag)}
                className="ml-1 text-blue-600 hover:text-blue-800"
                data-testid={`remove-tag-${index}`}
              >
                ×
              </button>
            </span>
          ))}
        </div>
        <div className="flex gap-2">
          <input
            type="text"
            value={newTag}
            onChange={(e) => setNewTag(e.target.value)}
            onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addTag())}
            className="flex-1 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="Add a tag"
            data-testid="tag-input"
          />
          <button
            type="button"
            onClick={addTag}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
            data-testid="add-tag-button"
          >
            Add
          </button>
        </div>
      </div>

      {/* Variables */}
      <div>
        <div className="flex justify-between items-center mb-2">
          <label className="block text-sm font-medium text-gray-700">
            Variables
          </label>
          <button
            type="button"
            onClick={addVariable}
            className="px-3 py-1 bg-green-600 text-white text-sm rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500"
            data-testid="add-variable-button"
          >
            Add Variable
          </button>
        </div>

        {formData.variables.map((variable, index) => (
          <div key={index} className="border border-gray-200 rounded-md p-4 mb-3">
            <div className="flex justify-between items-start mb-3">
              <h4 className="text-sm font-medium text-gray-700">Variable {index + 1}</h4>
              <button
                type="button"
                onClick={() => removeVariable(index)}
                className="text-red-600 hover:text-red-800"
                data-testid={`remove-variable-${index}`}
              >
                Remove
              </button>
            </div>

            <div className="grid grid-cols-2 gap-3">
              <div>
                <label className="block text-xs font-medium text-gray-600 mb-1">
                  Name
                </label>
                <input
                  type="text"
                  value={variable.name}
                  onChange={(e) => updateVariable(index, 'name', e.target.value)}
                  className="w-full px-2 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-1 focus:ring-blue-500"
                  placeholder="Variable name"
                  data-testid={`variable-name-${index}`}
                />
              </div>

              <div>
                <label className="block text-xs font-medium text-gray-600 mb-1">
                  Type
                </label>
                <select
                  value={variable.type}
                  onChange={(e) => updateVariable(index, 'type', e.target.value)}
                  className="w-full px-2 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-1 focus:ring-blue-500"
                  data-testid={`variable-type-${index}`}
                >
                  <option value="text">Text</option>
                  <option value="number">Number</option>
                  <option value="boolean">Boolean</option>
                  <option value="select">Select</option>
                </select>
              </div>
            </div>

            <div className="mt-3">
              <label className="block text-xs font-medium text-gray-600 mb-1">
                Description
              </label>
              <input
                type="text"
                value={variable.description}
                onChange={(e) => updateVariable(index, 'description', e.target.value)}
                className="w-full px-2 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-1 focus:ring-blue-500"
                placeholder="Variable description"
                data-testid={`variable-description-${index}`}
              />
            </div>

            <div className="mt-3 flex items-center">
              <input
                type="checkbox"
                id={`required-${index}`}
                checked={variable.required}
                onChange={(e) => updateVariable(index, 'required', e.target.checked)}
                className="mr-2"
                data-testid={`variable-required-${index}`}
              />
              <label htmlFor={`required-${index}`} className="text-xs text-gray-600">
                Required
              </label>
            </div>
          </div>
        ))}
      </div>

      {/* Public checkbox */}
      <div className="flex items-center">
        <input
          type="checkbox"
          id="isPublic"
          checked={formData.isPublic}
          onChange={(e) => handleInputChange('isPublic', e.target.checked)}
          className="mr-2"
          data-testid="public-checkbox"
        />
        <label htmlFor="isPublic" className="text-sm text-gray-700">
          Make this prompt public
        </label>
      </div>

      {/* Form Actions */}
      <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
        <button
          type="button"
          onClick={onCancel}
          className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
          data-testid="cancel-button"
        >
          Cancel
        </button>
        <button
          type="submit"
          disabled={loading}
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
          data-testid="save-button"
        >
          {loading ? 'Saving...' : 'Save Prompt'}
        </button>
      </div>
    </form>
  );
};

export default PromptForm;