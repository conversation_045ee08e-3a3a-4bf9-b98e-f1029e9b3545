/**
 * Centralized Error Handling System
 * Provides consistent error handling patterns across the application
 */

import type { ErrorInfo } from 'react';
import { productionErrorTracker } from './productionErrorTracking';

// Error types and interfaces
export interface AppError extends Error {
  code?: string;
  userMessage?: string;
  retryable?: boolean;
  context?: string;
  correlationId?: string;
  statusCode?: number;
  details?: Record<string, any>;
}

export interface ErrorContext {
  component?: string;
  action?: string;
  userId?: string;
  sessionId?: string;
  url?: string;
  userAgent?: string;
  timestamp?: Date;
  metadata?: Record<string, any>;
}

export interface ErrorHandlerOptions {
  showToast?: boolean;
  logToConsole?: boolean;
  reportToService?: boolean;
  fallbackMessage?: string;
  retryable?: boolean;
  context?: ErrorContext;
}

// Error categories
export enum ErrorCategory {
  NETWORK = 'NETWORK',
  VALIDATION = 'VALIDATION',
  AUTHENTICATION = 'AUTHENTICATION',
  AUTHORIZATION = 'AUTHORIZATION',
  NOT_FOUND = 'NOT_FOUND',
  SERVER = 'SERVER',
  CLIENT = 'CLIENT',
  UNKNOWN = 'UNKNOWN'
}

// Error severity levels
export enum ErrorSeverity {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  CRITICAL = 'CRITICAL'
}

// Standardized error class
export class StandardError extends Error implements AppError {
  public readonly code: string;
  public readonly userMessage: string;
  public readonly retryable: boolean;
  public readonly context: string;
  public readonly correlationId: string;
  public readonly statusCode?: number;
  public readonly details?: Record<string, any>;
  public readonly category: ErrorCategory;
  public readonly severity: ErrorSeverity;

  constructor(
    message: string,
    options: {
      code?: string;
      userMessage?: string;
      retryable?: boolean;
      context?: string;
      correlationId?: string;
      statusCode?: number;
      details?: Record<string, any>;
      category?: ErrorCategory;
      severity?: ErrorSeverity;
    } = {}
  ) {
    super(message);
    this.name = 'StandardError';
    this.code = options.code || 'UNKNOWN_ERROR';
    this.userMessage = options.userMessage || 'An unexpected error occurred. Please try again.';
    this.retryable = options.retryable ?? true;
    this.context = options.context || 'unknown';
    this.correlationId = options.correlationId || this.generateCorrelationId();
    this.statusCode = options.statusCode;
    this.details = options.details;
    this.category = options.category || ErrorCategory.UNKNOWN;
    this.severity = options.severity || ErrorSeverity.MEDIUM;
  }

  private generateCorrelationId(): string {
    return `err_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

// Error handler class
export class ErrorHandler {
  private static instance: ErrorHandler;
  private errorReportingService?: (error: AppError, context?: ErrorContext) => void;
  private toastService?: (type: 'error' | 'warning' | 'info', title: string, message?: string) => void;

  private constructor() {}

  public static getInstance(): ErrorHandler {
    if (!ErrorHandler.instance) {
      ErrorHandler.instance = new ErrorHandler();
    }
    return ErrorHandler.instance;
  }

  // Configure error reporting service
  public setErrorReportingService(service: (error: AppError, context?: ErrorContext) => void): void {
    this.errorReportingService = service;
  }

  // Configure toast service
  public setToastService(service: (type: 'error' | 'warning' | 'info', title: string, message?: string) => void): void {
    this.toastService = service;
  }

  // Main error handling method
  public handleError(error: unknown, options: ErrorHandlerOptions = {}): StandardError {
    const standardError = this.normalizeError(error, options.context);

    // Log to console if enabled (default: true in development)
    if (options.logToConsole !== false) {
      this.logError(standardError, options.context);
    }

    // Show toast notification if enabled
    if (options.showToast !== false && this.toastService) {
      this.showErrorToast(standardError, options.fallbackMessage);
    }

    // Report to error service if enabled (default: true in production)
    if (options.reportToService !== false && this.errorReportingService) {
      this.reportError(standardError, options.context);
    }

    // Track error in production error tracker
    productionErrorTracker.trackError(standardError, options.context);

    return standardError;
  }

  // Normalize different error types to StandardError
  private normalizeError(error: unknown, context?: ErrorContext): StandardError {
    if (error instanceof StandardError) {
      return error;
    }

    if (error instanceof Error) {
      return new StandardError(error.message, {
        code: this.extractErrorCode(error),
        userMessage: this.extractUserMessage(error),
        retryable: this.isRetryable(error),
        context: context?.component || context?.action || 'unknown',
        category: this.categorizeError(error),
        severity: this.determineSeverity(error),
        details: { originalError: error.name, stack: error.stack }
      });
    }

    // Handle string errors
    if (typeof error === 'string') {
      return new StandardError(error, {
        context: context?.component || context?.action || 'unknown'
      });
    }

    // Handle network/fetch errors
    if (error && typeof error === 'object' && 'status' in error) {
      const networkError = error as { status: number; statusText?: string; message?: string };
      return new StandardError(
        networkError.message || networkError.statusText || 'Network error',
        {
          code: `HTTP_${networkError.status}`,
          statusCode: networkError.status,
          category: ErrorCategory.NETWORK,
          severity: networkError.status >= 500 ? ErrorSeverity.HIGH : ErrorSeverity.MEDIUM,
          retryable: networkError.status >= 500 || networkError.status === 408 || networkError.status === 429,
          context: context?.component || context?.action || 'network'
        }
      );
    }

    // Fallback for unknown error types
    return new StandardError('An unknown error occurred', {
      context: context?.component || context?.action || 'unknown',
      details: { originalError: error }
    });
  }

  // Extract error code from various error types
  private extractErrorCode(error: Error): string {
    if ('code' in error && typeof error.code === 'string') {
      return error.code;
    }
    if ('status' in error && typeof error.status === 'number') {
      return `HTTP_${error.status}`;
    }
    return error.name || 'UNKNOWN_ERROR';
  }

  // Extract user-friendly message
  private extractUserMessage(error: Error): string {
    if ('userMessage' in error && typeof error.userMessage === 'string') {
      return error.userMessage;
    }
    
    // Map common error patterns to user-friendly messages
    const message = error.message.toLowerCase();
    if (message.includes('network') || message.includes('fetch')) {
      return 'Unable to connect to the server. Please check your internet connection and try again.';
    }
    if (message.includes('unauthorized') || message.includes('401')) {
      return 'Your session has expired. Please log in again.';
    }
    if (message.includes('forbidden') || message.includes('403')) {
      return 'You do not have permission to perform this action.';
    }
    if (message.includes('not found') || message.includes('404')) {
      return 'The requested resource was not found.';
    }
    if (message.includes('timeout')) {
      return 'The request timed out. Please try again.';
    }
    
    return 'An unexpected error occurred. Please try again.';
  }

  // Determine if error is retryable
  private isRetryable(error: Error): boolean {
    if ('retryable' in error && typeof error.retryable === 'boolean') {
      return error.retryable;
    }
    
    const message = error.message.toLowerCase();
    const nonRetryablePatterns = [
      'unauthorized', '401', 'forbidden', '403', 'not found', '404',
      'bad request', '400', 'validation', 'invalid'
    ];
    
    return !nonRetryablePatterns.some(pattern => message.includes(pattern));
  }

  // Categorize error
  private categorizeError(error: Error): ErrorCategory {
    const message = error.message.toLowerCase();
    
    if (message.includes('network') || message.includes('fetch') || message.includes('timeout')) {
      return ErrorCategory.NETWORK;
    }
    if (message.includes('unauthorized') || message.includes('401')) {
      return ErrorCategory.AUTHENTICATION;
    }
    if (message.includes('forbidden') || message.includes('403')) {
      return ErrorCategory.AUTHORIZATION;
    }
    if (message.includes('not found') || message.includes('404')) {
      return ErrorCategory.NOT_FOUND;
    }
    if (message.includes('validation') || message.includes('invalid') || message.includes('400')) {
      return ErrorCategory.VALIDATION;
    }
    if (message.includes('500') || message.includes('server')) {
      return ErrorCategory.SERVER;
    }
    
    return ErrorCategory.CLIENT;
  }

  // Determine error severity
  private determineSeverity(error: Error): ErrorSeverity {
    if ('status' in error && typeof error.status === 'number') {
      const status = error.status;
      if (status >= 500) return ErrorSeverity.HIGH;
      if (status >= 400) return ErrorSeverity.MEDIUM;
      return ErrorSeverity.LOW;
    }
    
    const message = error.message.toLowerCase();
    if (message.includes('critical') || message.includes('fatal')) {
      return ErrorSeverity.CRITICAL;
    }
    if (message.includes('server') || message.includes('500')) {
      return ErrorSeverity.HIGH;
    }
    
    return ErrorSeverity.MEDIUM;
  }

  // Log error to console
  private logError(error: StandardError, context?: ErrorContext): void {
    const logData = {
      error: {
        name: error.name,
        message: error.message,
        code: error.code,
        category: error.category,
        severity: error.severity,
        correlationId: error.correlationId,
        stack: error.stack
      },
      context,
      timestamp: new Date().toISOString()
    };

    if (error.severity === ErrorSeverity.CRITICAL || error.severity === ErrorSeverity.HIGH) {
      console.error('🚨 Critical Error:', logData);
    } else if (error.severity === ErrorSeverity.MEDIUM) {
      console.warn('⚠️ Error:', logData);
    } else {
      console.info('ℹ️ Minor Error:', logData);
    }
  }

  // Show error toast
  private showErrorToast(error: StandardError, fallbackMessage?: string): void {
    if (!this.toastService) return;

    const message = fallbackMessage || error.userMessage;
    const title = error.severity === ErrorSeverity.CRITICAL ? 'Critical Error' : 'Error';
    
    this.toastService('error', title, message);
  }

  // Report error to external service
  private reportError(error: StandardError, context?: ErrorContext): void {
    if (!this.errorReportingService) return;
    
    // Only report medium and higher severity errors in production
    if (process.env.NODE_ENV === 'production' && error.severity !== ErrorSeverity.LOW) {
      this.errorReportingService(error, context);
    }
  }
}

// Convenience functions
export const errorHandler = ErrorHandler.getInstance();

export const handleError = (error: unknown, options?: ErrorHandlerOptions): StandardError => {
  return errorHandler.handleError(error, options);
};

export const createError = (message: string, options?: Partial<StandardError>): StandardError => {
  return new StandardError(message, options);
};

// React Error Boundary integration
export const handleReactError = (error: Error, errorInfo: ErrorInfo, component?: string): StandardError => {
  return errorHandler.handleError(error, {
    context: {
      component,
      action: 'render',
      metadata: { componentStack: errorInfo.componentStack }
    },
    showToast: true,
    reportToService: true
  });
};

// Async operation error handling
export const withErrorHandling = <T extends any[], R>(
  fn: (...args: T) => Promise<R>,
  options?: ErrorHandlerOptions
) => {
  return async (...args: T): Promise<R> => {
    try {
      return await fn(...args);
    } catch (error) {
      throw errorHandler.handleError(error, options);
    }
  };
};

// Safe async operation wrapper
export const safeAsync = async <T>(
  operation: () => Promise<T>,
  options?: ErrorHandlerOptions & { defaultValue?: T }
): Promise<T | undefined> => {
  try {
    return await operation();
  } catch (error) {
    errorHandler.handleError(error, options);
    return options?.defaultValue;
  }
};

// Retry mechanism with exponential backoff
export const withRetry = async <T>(
  operation: () => Promise<T>,
  options: {
    maxAttempts?: number;
    baseDelay?: number;
    maxDelay?: number;
    backoffFactor?: number;
    retryCondition?: (error: StandardError) => boolean;
    onRetry?: (attempt: number, error: StandardError) => void;
  } = {}
): Promise<T> => {
  const {
    maxAttempts = 3,
    baseDelay = 1000,
    maxDelay = 10000,
    backoffFactor = 2,
    retryCondition = (error) => error.retryable,
    onRetry
  } = options;

  let lastError: StandardError;

  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = errorHandler.handleError(error, { showToast: false, logToConsole: attempt === maxAttempts });

      if (attempt === maxAttempts || !retryCondition(lastError)) {
        throw lastError;
      }

      if (onRetry) {
        onRetry(attempt, lastError);
      }

      const delay = Math.min(baseDelay * Math.pow(backoffFactor, attempt - 1), maxDelay);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }

  throw lastError!;
};

// Form validation error handling
export interface ValidationError {
  field: string;
  message: string;
  code?: string;
}

export const createValidationError = (errors: ValidationError[]): StandardError => {
  return new StandardError('Validation failed', {
    code: 'VALIDATION_ERROR',
    category: ErrorCategory.VALIDATION,
    severity: ErrorSeverity.LOW,
    userMessage: 'Please correct the highlighted fields and try again.',
    retryable: false,
    details: { validationErrors: errors }
  });
};

// Network error handling
export const handleNetworkError = (response: Response): StandardError => {
  return new StandardError(`HTTP ${response.status}: ${response.statusText}`, {
    code: `HTTP_${response.status}`,
    statusCode: response.status,
    category: ErrorCategory.NETWORK,
    severity: response.status >= 500 ? ErrorSeverity.HIGH : ErrorSeverity.MEDIUM,
    retryable: response.status >= 500 || response.status === 408 || response.status === 429,
    userMessage: getNetworkErrorMessage(response.status)
  });
};

const getNetworkErrorMessage = (status: number): string => {
  switch (status) {
    case 400:
      return 'The request was invalid. Please check your input and try again.';
    case 401:
      return 'Your session has expired. Please log in again.';
    case 403:
      return 'You do not have permission to perform this action.';
    case 404:
      return 'The requested resource was not found.';
    case 408:
      return 'The request timed out. Please try again.';
    case 429:
      return 'Too many requests. Please wait a moment and try again.';
    case 500:
      return 'A server error occurred. Please try again later.';
    case 502:
      return 'The server is temporarily unavailable. Please try again later.';
    case 503:
      return 'The service is temporarily unavailable. Please try again later.';
    default:
      return 'A network error occurred. Please check your connection and try again.';
  }
};
