#!/usr/bin/env python3
"""
Test AI Provider Integration
This script tests the AI providers to ensure they're working correctly
"""

import os
import sys
import asyncio
import json
from datetime import datetime
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Add the functions directory to the path
sys.path.append('functions')

async def test_ai_providers():
    """Test all AI providers"""
    print("🧪 Testing AI Provider Integration...")
    print("=" * 50)
    
    try:
        # Import the AI service
        from functions.src.ai_service import ai_service
        
        # Test OpenRouter
        print("\n🔗 Testing OpenRouter...")
        openrouter_result = await ai_service.test_provider_connection('openrouter')
        
        if openrouter_result['success']:
            print("✅ OpenRouter: CONNECTED")
            print(f"   Model: {openrouter_result.get('model', 'Unknown')}")
            print(f"   Response: {openrouter_result.get('response', 'No response')[:100]}...")
            print(f"   Tokens: {openrouter_result.get('tokens_used', 0)}")
            print(f"   Time: {openrouter_result.get('response_time', 0):.2f}s")
        else:
            print("❌ OpenRouter: FAILED")
            print(f"   Error: {openrouter_result.get('error', 'Unknown error')}")
        
        # Test Google AI
        print("\n🤖 Testing Google AI...")
        google_result = await ai_service.test_provider_connection('google')
        
        if google_result['success']:
            print("✅ Google AI: CONNECTED")
            print(f"   Model: {google_result.get('model', 'Unknown')}")
            print(f"   Response: {google_result.get('response', 'No response')[:100]}...")
            print(f"   Tokens: {google_result.get('tokens_used', 0)}")
            print(f"   Time: {google_result.get('response_time', 0):.2f}s")
        else:
            print("❌ Google AI: FAILED")
            print(f"   Error: {google_result.get('error', 'Unknown error')}")
        
        # Test prompt generation
        print("\n📝 Testing Prompt Generation...")
        prompt_result = await ai_service.generate_prompt_response(
            user_id="test_user",
            prompt_template="Hello! Please respond with a brief greeting.",
            variables={},
            provider="openrouter",
            user_tier="free"
        )
        
        if prompt_result['success']:
            print("✅ Prompt Generation: SUCCESS")
            print(f"   Response: {prompt_result.get('response', 'No response')[:100]}...")
            metadata = prompt_result.get('metadata', {})
            print(f"   Model: {metadata.get('model', 'Unknown')}")
            print(f"   Tokens: {metadata.get('tokens_used', 0)}")
            print(f"   Cost: ${metadata.get('cost', 0):.4f}")
            print(f"   Time: {metadata.get('total_time', 0):.2f}s")
        else:
            print("❌ Prompt Generation: FAILED")
            print(f"   Error: {prompt_result.get('error', 'Unknown error')}")
        
        # Test RAG functionality
        print("\n🔍 Testing RAG Functionality...")
        rag_result = await ai_service.generate_rag_response(
            user_id="test_user",
            query="What is artificial intelligence?",
            max_context_tokens=1000,
            provider="openrouter"
        )
        
        if rag_result['success']:
            print("✅ RAG Generation: SUCCESS")
            print(f"   Response: {rag_result.get('response', 'No response')[:100]}...")
            metadata = rag_result.get('metadata', {})
            print(f"   Context docs: {metadata.get('context_documents', 0)}")
            print(f"   Tokens: {metadata.get('tokens_used', 0)}")
            print(f"   Time: {metadata.get('total_time', 0):.2f}s")
        else:
            print("❌ RAG Generation: FAILED")
            print(f"   Error: {rag_result.get('error', 'Unknown error')}")
        
        # Summary
        print("\n" + "=" * 50)
        print("📊 TEST SUMMARY")
        print("=" * 50)
        
        tests = [
            ("OpenRouter Connection", openrouter_result['success']),
            ("Google AI Connection", google_result['success']),
            ("Prompt Generation", prompt_result['success']),
            ("RAG Functionality", rag_result['success'])
        ]
        
        passed = sum(1 for _, success in tests if success)
        total = len(tests)
        
        for test_name, success in tests:
            status = "✅ PASS" if success else "❌ FAIL"
            print(f"{test_name:.<30} {status}")
        
        print(f"\nResults: {passed}/{total} tests passed")
        
        if passed == total:
            print("🎉 All tests passed! AI integration is working correctly.")
            return True
        else:
            print("⚠️  Some tests failed. Check the errors above.")
            return False
            
    except Exception as e:
        print(f"❌ Critical error during testing: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_environment():
    """Check if environment variables are set"""
    print("🔧 Checking Environment Variables...")
    print("-" * 30)
    
    required_vars = [
        'OPENROUTER_API_KEY',
        'GOOGLE_API_KEY'
    ]
    
    missing = []
    for var in required_vars:
        value = os.getenv(var)
        if value:
            print(f"✅ {var}: Set (starts with {value[:10]}...)")
        else:
            print(f"❌ {var}: Not set")
            missing.append(var)
    
    if missing:
        print(f"\n⚠️  Missing environment variables: {missing}")
        print("Please set them in your .env file or environment")
        return False
    
    print("\n✅ All required environment variables are set")
    return True

async def main():
    """Main test function"""
    print("🚀 AI Provider Integration Test")
    print("=" * 50)
    print(f"Timestamp: {datetime.now().isoformat()}")
    print()
    
    # Check environment first
    if not check_environment():
        print("\n❌ Environment check failed. Please fix the issues above.")
        sys.exit(1)
    
    # Load environment variables
    try:
        from dotenv import load_dotenv
        load_dotenv('functions/.env')
        print("✅ Loaded environment from functions/.env")
    except ImportError:
        print("ℹ️  dotenv not available, using system environment")
    except Exception as e:
        print(f"⚠️  Could not load .env file: {e}")
    
    print()
    
    # Run AI tests
    success = await test_ai_providers()
    
    if success:
        print("\n🎉 Integration test completed successfully!")
        sys.exit(0)
    else:
        print("\n❌ Integration test failed!")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
