#!/usr/bin/env node

/**
 * <PERSON>rip<PERSON> to fix remaining parsing errors in the codebase
 * Focuses on syntax errors that prevent ESLint from parsing files correctly
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const srcDir = path.join(__dirname, '..', 'src');

// Common parsing error patterns and their fixes
const parsingErrorFixes = [
  // Fix malformed promise chains
  {
    pattern: /\.then\(([^)]+)\)\.catch\(([^)]+)\)\s*=>\s*{/g,
    replacement: '.then(($1) => {'
  },
  // Fix malformed destructuring with extra commas
  {
    pattern: /,\s*'data-test'\s*:\s*}\)/g,
    replacement: '}'
  },
  // Fix malformed arrow functions
  {
    pattern: /=>\s*{\s*\.catch\([^)]+\)\s*=>\s*{/g,
    replacement: '=> {'
  },
  // Fix malformed object destructuring
  {
    pattern: /{\s*([^}]+),\s*'[^']*'\s*:\s*}\s*\)/g,
    replacement: '{ $1 }'
  },
  // Fix incomplete statements
  {
    pattern: /;\s*\.catch\([^)]+\)\s*=>\s*[^;]*;/g,
    replacement: ';'
  },
  // Fix malformed imports with 'as}'
  {
    pattern: /import\s*{\s*([^}]+)\s+as\s*}\s*from/g,
    replacement: 'import { $1 } from'
  },
  // Fix malformed promise chains in then statements
  {
    pattern: /\.then\(\(\)\.catch\([^)]+\)\s*=>\s*[^)]*\)\s*=>\s*{/g,
    replacement: '.then(() => {'
  },
  // Fix incomplete commented function declarations
  {
    pattern: /\/\/\s*(export\s+const\s+\w+\s*=\s*[^{]*{)\s*$/gm,
    replacement: '$1'
  }
];

function fixParsingErrors(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    
    // Apply each fix pattern
    parsingErrorFixes.forEach(fix => {
      const newContent = content.replace(fix.pattern, fix.replacement);
      if (newContent !== content) {
        content = newContent;
        modified = true;
      }
    });
    
    // Additional specific fixes based on common patterns
    
    // Fix malformed promise chains in import statements
    if (content.includes('.then((') && content.includes(').catch(')) {
      const fixedContent = content.replace(
        /\.then\(\(([^)]+)\)\.catch\([^)]+\)\s*=>\s*[^)]*\)\s*=>\s*{/g,
        '.then(($1) => {'
      );
      if (fixedContent !== content) {
        content = fixedContent;
        modified = true;
      }
    }
    
    // Fix incomplete object destructuring
    if (content.includes("'data-test':}")) {
      const fixedContent = content.replace(/'data-test'\s*:\s*}/g, '');
      if (fixedContent !== content) {
        content = fixedContent;
        modified = true;
      }
    }
    
    // Fix malformed function parameters
    if (content.includes(",'data-test':}")) {
      const fixedContent = content.replace(/,\s*'data-test'\s*:\s*}/g, '');
      if (fixedContent !== content) {
        content = fixedContent;
        modified = true;
      }
    }
    
    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ Fixed parsing errors in: ${path.relative(srcDir, filePath)}`);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`❌ Error processing ${filePath}:`, error.message);
    return false;
  }
}

function processDirectory(dir) {
  const files = fs.readdirSync(dir);
  let totalFixed = 0;
  
  for (const file of files) {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory()) {
      totalFixed += processDirectory(filePath);
    } else if (file.endsWith('.ts') || file.endsWith('.tsx')) {
      if (fixParsingErrors(filePath)) {
        totalFixed++;
      }
    }
  }
  
  return totalFixed;
}

console.log('🔧 Starting parsing error fixes...\n');

const fixedCount = processDirectory(srcDir);

console.log(`\n✨ Parsing error fix complete!`);
console.log(`📊 Files fixed: ${fixedCount}`);

if (fixedCount > 0) {
  console.log('\n🔍 Running ESLint to verify fixes...');
  
  // Run ESLint to check if parsing errors are resolved
  const { execSync } = await import('child_process');
  try {
    const result = execSync('npx eslint src --max-warnings=1000', { 
      encoding: 'utf8',
      cwd: path.join(__dirname, '..')
    });
    console.log('✅ ESLint parsing successful!');
  } catch (error) {
    const errorCount = (error.stdout.match(/error/g) || []).length;
    console.log(`⚠️  ${errorCount} errors remaining (may include non-parsing errors)`);
  }
}
