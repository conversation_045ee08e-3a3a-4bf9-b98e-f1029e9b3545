/**
 * React Hooks for Error Handling
 * Provides convenient hooks for error handling in React components
 */

import { useCallback, useState, useEffect } from 'react';
import { useToast } from '@/components/common/Toast';
import type { StandardError, ErrorHandlerOptions, ValidationError } from '@/utils/errorHandler';
import { errorHandler, withRetry, safeAsync, createValidationError } from '@/utils/errorHandler';

// Hook for general error handling
export const useErrorHandler = () => {
  const { addToast } = useToast();

  // Configure toast service on mount
  useEffect(() => {
    errorHandler.setToastService((type, title, message) => {
      addToast({ type, title, message, duration: type === 'error' ? 0 : 5000 });
    });
  }, [addToast]);

  const handleError = useCallback((error: unknown, options?: ErrorHandlerOptions): StandardError => {
    return errorHandler.handleError(error, options);
  }, []);

  return { handleError };
};

// Hook for async operations with error handling
export const useAsyncError = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<StandardError | null>(null);
  const { handleError } = useErrorHandler();

  const execute = useCallback(async <T>(
    operation: () => Promise<T>,
    options?: ErrorHandlerOptions
  ): Promise<T | undefined> => {
    setLoading(true);
    setError(null);

    try {
      const result = await operation();
      return result;
    } catch (err) {
      const standardError = handleError(err, options);
      setError(standardError);
      return undefined;
    } finally {
      setLoading(false);
    }
  }, [handleError]);

  const reset = useCallback(() => {
    setError(null);
    setLoading(false);
  }, []);

  return { execute, loading, error, reset };
};

// Hook for operations with retry capability
export const useAsyncWithRetry = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<StandardError | null>(null);
  const [retryCount, setRetryCount] = useState(0);
  const { handleError } = useErrorHandler();

  const execute = useCallback(async <T>(
    operation: () => Promise<T>,
    retryOptions?: {
      maxAttempts?: number;
      baseDelay?: number;
      maxDelay?: number;
      backoffFactor?: number;
      showRetryToast?: boolean;
    }
  ): Promise<T | undefined> => {
    setLoading(true);
    setError(null);
    setRetryCount(0);

    try {
      const result = await withRetry(operation, {
        ...retryOptions,
        onRetry: (attempt, error) => {
          setRetryCount(attempt);
          if (retryOptions?.showRetryToast) {
            handleError(error, {
              showToast: true,
              fallbackMessage: `Attempt ${attempt} failed. Retrying...`
            });
          }
        }
      });
      return result;
    } catch (err) {
      const standardError = handleError(err);
      setError(standardError);
      return undefined;
    } finally {
      setLoading(false);
    }
  }, [handleError]);

  const reset = useCallback(() => {
    setError(null);
    setLoading(false);
    setRetryCount(0);
  }, []);

  return { execute, loading, error, retryCount, reset };
};

// Hook for safe async operations (doesn't throw)
export const useSafeAsync = () => {
  const [loading, setLoading] = useState(false);
  const { handleError } = useErrorHandler();

  const execute = useCallback(async <T>(
    operation: () => Promise<T>,
    options?: ErrorHandlerOptions & { defaultValue?: T }
  ): Promise<T | undefined> => {
    setLoading(true);

    try {
      const result = await safeAsync(operation, options);
      return result;
    } finally {
      setLoading(false);
    }
  }, [handleError]);

  return { execute, loading };
};

// Hook for form validation with error handling
export const useFormValidation = <T extends Record<string, any>>() => {
  const [errors, setErrors] = useState<Record<keyof T, string>>({} as Record<keyof T, string>);
  const [isValid, setIsValid] = useState(true);
  const { handleError } = useErrorHandler();

  const validateField = useCallback((
    field: keyof T,
    value: any,
    validators: Array<(value: any) => string | null>
  ): boolean => {
    for (const validator of validators) {
      const error = validator(value);
      if (error) {
        setErrors(prev => ({ ...prev, [field]: error }));
        return false;
      }
    }
    
    setErrors(prev => {
      const newErrors = { ...prev };
      delete newErrors[field];
      return newErrors;
    });
    return true;
  }, []);

  const validateForm = useCallback((
    data: T,
    validationRules: Record<keyof T, Array<(value: any) => string | null>>
  ): boolean => {
    const validationErrors: ValidationError[] = [];
    const newErrors: Record<keyof T, string> = {} as Record<keyof T, string>;

    for (const [field, validators] of Object.entries(validationRules) as Array<[keyof T, Array<(value: any) => string | null>]>) {
      for (const validator of validators) {
        const error = validator(data[field]);
        if (error) {
          newErrors[field] = error;
          validationErrors.push({
            field: String(field),
            message: error,
            code: 'VALIDATION_ERROR'
          });
          break; // Only show first error per field
        }
      }
    }

    setErrors(newErrors);
    const valid = Object.keys(newErrors).length === 0;
    setIsValid(valid);

    if (!valid) {
      const validationError = createValidationError(validationErrors);
      handleError(validationError, { showToast: false }); // Don't show toast for validation errors
    }

    return valid;
  }, [handleError]);

  const clearErrors = useCallback(() => {
    setErrors({} as Record<keyof T, string>);
    setIsValid(true);
  }, []);

  const clearFieldError = useCallback((field: keyof T) => {
    setErrors(prev => {
      const newErrors = { ...prev };
      delete newErrors[field];
      return newErrors;
    });
  }, []);

  return {
    errors,
    isValid,
    validateField,
    validateForm,
    clearErrors,
    clearFieldError
  };
};

// Hook for API calls with standardized error handling
export const useApiCall = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<StandardError | null>(null);
  const { handleError } = useErrorHandler();

  const call = useCallback(async <T>(
    apiCall: () => Promise<Response>,
    options?: {
      showSuccessToast?: boolean;
      successMessage?: string;
      showErrorToast?: boolean;
      transform?: (data: any) => T;
    }
  ): Promise<T | undefined> => {
    setLoading(true);
    setError(null);

    try {
      const response = await apiCall();
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      const result = options?.transform ? options.transform(data) : data;

      if (options?.showSuccessToast && options?.successMessage) {
        handleError(new Error('Success'), {
          showToast: true,
          fallbackMessage: options.successMessage
        });
      }

      return result;
    } catch (err) {
      const standardError = handleError(err, {
        showToast: options?.showErrorToast !== false,
        context: { action: 'api_call' }
      });
      setError(standardError);
      return undefined;
    } finally {
      setLoading(false);
    }
  }, [handleError]);

  const reset = useCallback(() => {
    setError(null);
    setLoading(false);
  }, []);

  return { call, loading, error, reset };
};

// Common validation functions
export const validators = {
  required: (message = 'This field is required') => (value: any): string | null => {
    if (value === null || value === undefined || value === '') {
      return message;
    }
    return null;
  },

  email: (message = 'Please enter a valid email address') => (value: string): string | null => {
    if (value && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
      return message;
    }
    return null;
  },

  minLength: (min: number, message?: string) => (value: string): string | null => {
    if (value && value.length < min) {
      return message || `Must be at least ${min} characters long`;
    }
    return null;
  },

  maxLength: (max: number, message?: string) => (value: string): string | null => {
    if (value && value.length > max) {
      return message || `Must be no more than ${max} characters long`;
    }
    return null;
  },

  pattern: (regex: RegExp, message = 'Invalid format') => (value: string): string | null => {
    if (value && !regex.test(value)) {
      return message;
    }
    return null;
  },

  custom: (validator: (value: any) => boolean, message: string) => (value: any): string | null => {
    if (!validator(value)) {
      return message;
    }
    return null;
  }
};
