const { onCall, onRequest } = require('firebase-functions/v2/https');
const { initializeApp } = require('firebase-admin/app');

// Initialize Firebase Admin
initializeApp();

// Main API endpoint - Firebase callable functions handle CORS automatically
exports.api = onCall({ region: 'australia-southeast1' }, (request) => {
  const data = request.data || {};
  const endpoint = data.endpoint || 'health';

  switch (endpoint) {
    case 'health':
      return {
        status: 'success',
        message: 'API working',
        region: 'australia-southeast1'
      };
    
    case 'execute_prompt':
      return {
        status: 'success',
        message: 'Mock execution',
        region: 'australia-southeast1',
        response: 'Test response from Australia'
      };
    
    case 'test_openrouter_connection':
      return {
        status: 'success',
        message: 'Mock connection',
        region: 'australia-southeast1'
      };

    case 'get_available_models':
      return {
        status: 'success',
        message: 'Available models retrieved',
        region: 'australia-southeast1',
        models: {
          'openrouter/gpt-4': {
            provider: 'openrouter',
            model_name: 'openai/gpt-4',
            display_name: 'GPT-4',
            description: 'Most capable GPT model',
            context_window: 8192,
            cost_per_1k_tokens: 0.03
          },
          'openrouter/gpt-3.5-turbo': {
            provider: 'openrouter',
            model_name: 'openai/gpt-3.5-turbo',
            display_name: 'GPT-3.5 Turbo',
            description: 'Fast and efficient model',
            context_window: 4096,
            cost_per_1k_tokens: 0.002
          },
          'openrouter/claude-3-sonnet': {
            provider: 'openrouter',
            model_name: 'anthropic/claude-3-sonnet',
            display_name: 'Claude 3 Sonnet',
            description: 'Balanced performance model',
            context_window: 200000,
            cost_per_1k_tokens: 0.015
          }
        },
        apiKeysConfigured: {
          'openrouter/gpt-4': true,
          'openrouter/gpt-3.5-turbo': true,
          'openrouter/claude-3-sonnet': true
        }
      };

    default:
      return {
        status: 'error',
        message: `Unknown endpoint: ${endpoint}`
      };
  }
});

// Separate function for get_available_models (for backward compatibility)
exports.get_available_models = onCall({ region: 'australia-southeast1' }, (request) => {
  return {
    success: true,
    message: 'Available models retrieved',
    region: 'australia-southeast1',
    models: {
      'openrouter/gpt-4': {
        provider: 'openrouter',
        model_name: 'openai/gpt-4',
        display_name: 'GPT-4',
        description: 'Most capable GPT model',
        context_window: 8192,
        cost_per_1k_tokens: 0.03
      },
      'openrouter/gpt-3.5-turbo': {
        provider: 'openrouter',
        model_name: 'openai/gpt-3.5-turbo',
        display_name: 'GPT-3.5 Turbo',
        description: 'Fast and efficient model',
        context_window: 4096,
        cost_per_1k_tokens: 0.002
      },
      'openrouter/claude-3-sonnet': {
        provider: 'openrouter',
        model_name: 'anthropic/claude-3-sonnet',
        display_name: 'Claude 3 Sonnet',
        description: 'Balanced performance model',
        context_window: 200000,
        cost_per_1k_tokens: 0.015
      }
    },
    apiKeysConfigured: {
      'openrouter/gpt-4': true,
      'openrouter/gpt-3.5-turbo': true,
      'openrouter/claude-3-sonnet': true
    }
  };
});

// Health check endpoint
exports.health = onRequest({ region: 'australia-southeast1' }, (req, res) => {
  res.json({
    status: 'healthy',
    region: 'australia-southeast1'
  });
});
