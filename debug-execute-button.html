<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Execute Button Debug Tool</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .debug-section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .debug-title {
            color: #2563eb;
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            border-bottom: 2px solid #e5e7eb;
            padding-bottom: 10px;
        }
        .test-button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .test-button:hover {
            background: #2563eb;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
        }
        .success {
            background: #dcfce7;
            border: 1px solid #16a34a;
            color: #15803d;
        }
        .error {
            background: #fef2f2;
            border: 1px solid #dc2626;
            color: #dc2626;
        }
        .warning {
            background: #fefce8;
            border: 1px solid #ca8a04;
            color: #a16207;
        }
        .info {
            background: #eff6ff;
            border: 1px solid #3b82f6;
            color: #1d4ed8;
        }
        .code {
            background: #f3f4f6;
            padding: 15px;
            border-radius: 6px;
            font-family: monospace;
            font-size: 12px;
            overflow-x: auto;
            margin: 10px 0;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-success { background: #16a34a; }
        .status-error { background: #dc2626; }
        .status-warning { background: #ca8a04; }
        .status-info { background: #3b82f6; }
    </style>
</head>
<body>
    <h1>🔍 Execute Button Comprehensive Debug Tool</h1>
    <p>This tool will help diagnose why the Execute button is not working correctly.</p>

    <div class="debug-section">
        <div class="debug-title">🌐 1. Website Accessibility Test</div>
        <button class="test-button" onclick="testWebsiteAccess()">Test Website Access</button>
        <div id="website-result"></div>
    </div>

    <div class="debug-section">
        <div class="debug-title">🔗 2. Route Configuration Test</div>
        <button class="test-button" onclick="testRoutes()">Test Route Patterns</button>
        <div id="route-result"></div>
    </div>

    <div class="debug-section">
        <div class="debug-title">🔐 3. Authentication Status Test</div>
        <button class="test-button" onclick="testAuthentication()">Check Auth Status</button>
        <div id="auth-result"></div>
    </div>

    <div class="debug-section">
        <div class="debug-title">🎯 4. Execute Button Simulation</div>
        <button class="test-button" onclick="simulateExecuteClick()">Simulate Execute Click</button>
        <div id="execute-result"></div>
    </div>

    <div class="debug-section">
        <div class="debug-title">🔧 5. Firebase Functions Test</div>
        <button class="test-button" onclick="testFirebaseFunctions()">Test Backend API</button>
        <div id="firebase-result"></div>
    </div>

    <div class="debug-section">
        <div class="debug-title">📋 6. Manual Testing Instructions</div>
        <div class="info result">
            <strong>Step-by-Step Manual Testing:</strong><br>
            1. Open browser developer tools (F12)<br>
            2. Go to Console tab<br>
            3. Navigate to: https://rag-prompt-library.web.app/<br>
            4. Login if not already authenticated<br>
            5. Go to Prompts page<br>
            6. Click Execute button on any prompt<br>
            7. Watch console for errors and network requests<br>
            8. Note the URL change and any redirects
        </div>
    </div>

    <div class="debug-section">
        <div class="debug-title">🚨 7. Common Issues Checklist</div>
        <div id="checklist-result">
            <div class="warning result">
                <strong>Potential Issues to Check:</strong><br>
                ✓ Route mismatch (Fixed: /prompts/{id}/execute)<br>
                ? Authentication state during navigation<br>
                ? Prompt loading in ExecutePrompt component<br>
                ? Firebase Functions deployment region<br>
                ? Browser cache or service worker issues<br>
                ? JavaScript errors preventing navigation<br>
                ? Network connectivity to Firebase<br>
                ? CORS issues with API calls
            </div>
        </div>
    </div>

    <script>
        function addResult(containerId, content, type = 'info') {
            const container = document.getElementById(containerId);
            const result = document.createElement('div');
            result.className = `result ${type}`;
            result.innerHTML = `<span class="status-indicator status-${type}"></span>${content}`;
            container.appendChild(result);
        }

        function clearResults(containerId) {
            document.getElementById(containerId).innerHTML = '';
        }

        async function testWebsiteAccess() {
            clearResults('website-result');
            addResult('website-result', 'Testing website accessibility...', 'info');
            
            try {
                const response = await fetch('https://rag-prompt-library.web.app/', { 
                    method: 'HEAD',
                    mode: 'no-cors'
                });
                addResult('website-result', '✅ Website is accessible', 'success');
            } catch (error) {
                addResult('website-result', `❌ Website access failed: ${error.message}`, 'error');
            }
        }

        function testRoutes() {
            clearResults('route-result');
            addResult('route-result', 'Testing route patterns...', 'info');
            
            const routes = [
                '/prompts',
                '/prompts/test-id/execute',
                '/execute-prompt/test-id'
            ];
            
            routes.forEach(route => {
                const isCorrect = route.includes('/prompts/') && route.includes('/execute');
                const status = isCorrect ? 'success' : 'error';
                const icon = isCorrect ? '✅' : '❌';
                addResult('route-result', `${icon} Route: ${route} - ${isCorrect ? 'CORRECT' : 'INCORRECT'}`, status);
            });
        }

        async function testAuthentication() {
            clearResults('auth-result');
            addResult('auth-result', 'Testing authentication status...', 'info');
            
            // This would need to be run in the actual app context
            addResult('auth-result', '⚠️ Authentication test requires running in app context', 'warning');
            addResult('auth-result', 'Manual check: Open browser console and run: window.firebase?.auth()?.currentUser', 'info');
        }

        function simulateExecuteClick() {
            clearResults('execute-result');
            addResult('execute-result', 'Simulating Execute button click...', 'info');
            
            const simulationCode = `
// Simulation of Execute button click flow:
const promptId = 'test-prompt-123';
const expectedRoute = \`/prompts/\${promptId}/execute\`;

console.log('Expected navigation:', expectedRoute);

// Check if route exists in React Router
// Check authentication state
// Check prompt loading logic
            `;
            
            addResult('execute-result', `<div class="code">${simulationCode}</div>`, 'info');
            addResult('execute-result', '⚠️ Run this simulation in the browser console on the actual website', 'warning');
        }

        async function testFirebaseFunctions() {
            clearResults('firebase-result');
            addResult('firebase-result', 'Testing Firebase Functions...', 'info');
            
            try {
                const healthResponse = await fetch('https://us-central1-rag-prompt-library.cloudfunctions.net/api/health');
                if (healthResponse.ok) {
                    addResult('firebase-result', '✅ Firebase Functions health check passed', 'success');
                } else {
                    addResult('firebase-result', `❌ Firebase Functions health check failed: ${healthResponse.status}`, 'error');
                }
            } catch (error) {
                addResult('firebase-result', `❌ Firebase Functions connection failed: ${error.message}`, 'error');
            }
        }

        // Auto-run basic tests on page load
        window.onload = function() {
            testWebsiteAccess();
            testRoutes();
        };
    </script>
</body>
</html>
