#!/usr/bin/env python3
"""
Test the deployed API endpoint to verify AI functionality
"""

import requests
import json
from datetime import datetime

# Configuration
PROJECT_ID = "rag-prompt-library"
API_BASE_URL = f"https://us-central1-{PROJECT_ID}.cloudfunctions.net/api"
WEBSITE_URL = "https://rag-prompt-library.web.app"

def test_api_endpoint(endpoint, data=None, method="GET"):
    """Test an API endpoint"""
    url = f"{API_BASE_URL}/{endpoint}"
    
    headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
    }
    
    try:
        if method == "POST":
            response = requests.post(url, json=data, headers=headers, timeout=30)
        else:
            response = requests.get(url, headers=headers, timeout=30)
        
        return {
            'success': True,
            'status_code': response.status_code,
            'response': response.json() if response.headers.get('content-type', '').startswith('application/json') else response.text,
            'response_time': response.elapsed.total_seconds()
        }
    except Exception as e:
        return {
            'success': False,
            'error': str(e)
        }

def test_callable_function(function_name, data=None):
    """Test a Firebase callable function"""
    url = f"https://us-central1-{PROJECT_ID}.cloudfunctions.net/{function_name}"
    
    headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
    }
    
    try:
        response = requests.post(url, json={'data': data}, headers=headers, timeout=30)
        
        return {
            'success': True,
            'status_code': response.status_code,
            'response': response.json() if response.headers.get('content-type', '').startswith('application/json') else response.text,
            'response_time': response.elapsed.total_seconds()
        }
    except Exception as e:
        return {
            'success': False,
            'error': str(e)
        }

def verify_api_deployment():
    """Verify the API deployment is working correctly"""
    print("🌐 API Deployment Verification")
    print("=" * 50)
    print(f"API Base URL: {API_BASE_URL}")
    print(f"Timestamp: {datetime.now().isoformat()}")
    print()
    
    results = {}
    
    # Test 1: Health Check
    print("🏥 Testing Health Check...")
    health_result = test_api_endpoint("health")
    if health_result['success']:
        print(f"✅ Health Check: {health_result['status_code']} - {health_result['response_time']:.2f}s")
        print(f"   Response: {health_result['response']}")
        results['health'] = True
    else:
        print(f"❌ Health Check: {health_result['error']}")
        results['health'] = False
    
    # Test 2: Test OpenRouter Connection (Callable Function)
    print("\n🔗 Testing OpenRouter Connection (Callable)...")
    openrouter_result = test_callable_function("test_openrouter_connection", {})
    if openrouter_result['success']:
        response = openrouter_result['response']
        print(f"✅ OpenRouter Callable: {openrouter_result['status_code']} - {openrouter_result['response_time']:.2f}s")
        
        if isinstance(response, dict) and 'result' in response:
            result_data = response['result']
            if 'mock' in str(result_data).lower() or 'migration' in str(result_data).lower():
                print("❌ Still returning mock responses")
                print(f"   Response: {result_data}")
                results['openrouter_callable'] = False
            else:
                print("✅ Real API response detected")
                print(f"   Model: {result_data.get('model', 'Unknown')}")
                print(f"   Tokens: {result_data.get('tokens', 'Unknown')}")
                results['openrouter_callable'] = True
        else:
            print(f"⚠️  Unexpected response format: {response}")
            results['openrouter_callable'] = False
    else:
        print(f"❌ OpenRouter Callable: {openrouter_result['error']}")
        results['openrouter_callable'] = False
    
    # Test 3: Execute Prompt (Callable Function)
    print("\n📝 Testing Prompt Execution (Callable)...")
    prompt_data = {
        "prompt": "Hello! Please respond with a brief greeting.",
        "provider": "openrouter",
        "use_rag": False
    }
    
    prompt_result = test_callable_function("execute_prompt", prompt_data)
    if prompt_result['success']:
        response = prompt_result['response']
        print(f"✅ Prompt Execution Callable: {prompt_result['status_code']} - {prompt_result['response_time']:.2f}s")
        
        if isinstance(response, dict) and 'result' in response:
            result_data = response['result']
            if 'mock' in str(result_data).lower() or 'migration' in str(result_data).lower():
                print("❌ Still returning mock responses")
                print(f"   Response: {result_data}")
                results['prompt_callable'] = False
            else:
                print("✅ Real AI response detected")
                print(f"   Response length: {len(str(result_data.get('response', '')))}")
                print(f"   Model: {result_data.get('metadata', {}).get('model', 'Unknown')}")
                print(f"   Tokens: {result_data.get('metadata', {}).get('tokens_used', 'Unknown')}")
                results['prompt_callable'] = True
        else:
            print(f"⚠️  Unexpected response format: {response}")
            results['prompt_callable'] = False
    else:
        print(f"❌ Prompt Execution Callable: {prompt_result['error']}")
        results['prompt_callable'] = False
    
    # Test 4: HTTP Endpoint for Execute Prompt
    print("\n🌐 Testing Prompt Execution (HTTP)...")
    http_result = test_api_endpoint("execute_prompt", prompt_data, method="POST")
    if http_result['success']:
        response = http_result['response']
        print(f"✅ Prompt Execution HTTP: {http_result['status_code']} - {http_result['response_time']:.2f}s")
        
        if isinstance(response, dict):
            if 'mock' in str(response).lower() or 'migration' in str(response).lower():
                print("❌ Still returning mock responses")
                print(f"   Response: {response}")
                results['prompt_http'] = False
            else:
                print("✅ Real AI response detected")
                print(f"   Response length: {len(str(response.get('response', '')))}")
                print(f"   Model: {response.get('metadata', {}).get('model', 'Unknown')}")
                print(f"   Tokens: {response.get('metadata', {}).get('tokens_used', 'Unknown')}")
                results['prompt_http'] = True
        else:
            print(f"⚠️  Unexpected response format: {response}")
            results['prompt_http'] = False
    else:
        print(f"❌ Prompt Execution HTTP: {http_result['error']}")
        results['prompt_http'] = False
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 API VERIFICATION SUMMARY")
    print("=" * 50)
    
    passed_tests = sum(1 for result in results.values() if result)
    total_tests = len(results)
    
    for test_name, passed in results.items():
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{test_name.replace('_', ' ').title():.<30} {status}")
    
    print(f"\nOverall Score: {passed_tests}/{total_tests}")
    
    if passed_tests == total_tests:
        print("🎉 All API tests passed! AI integration is working correctly.")
        return True
    elif passed_tests >= total_tests * 0.5:
        print("⚠️  Some API tests passed, but issues need attention.")
        return False
    else:
        print("❌ Multiple API failures detected. AI integration needs fixing.")
        return False

if __name__ == "__main__":
    print("🚀 Starting API Deployment Verification...")
    print()
    
    # Run API tests
    success = verify_api_deployment()
    
    if success:
        print("\n🎉 API deployment verification successful!")
    else:
        print("\n⚠️  API deployment verification found issues.")
    
    print(f"\n📅 Verification completed: {datetime.now().isoformat()}")
