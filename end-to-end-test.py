#!/usr/bin/env python3
"""
End-to-End Testing Script
Comprehensive testing of the complete prompt execution pipeline
"""

import os
import sys
import asyncio
import json
import time
from datetime import datetime
from typing import Dict, Any, List

# Add the functions directory to the path
sys.path.append('functions')

class EndToEndTester:
    def __init__(self):
        self.test_results = {}
        self.start_time = None
        
    async def run_all_tests(self):
        """Run all end-to-end tests"""
        self.start_time = datetime.now()
        
        print("🚀 End-to-End Testing - Complete Prompt Execution Pipeline")
        print("=" * 70)
        print(f"Started: {self.start_time.isoformat()}")
        print()
        
        # Test sequence
        tests = [
            ("Environment Setup", self.test_environment_setup),
            ("AI Service Initialization", self.test_ai_service_init),
            ("Provider Connections", self.test_provider_connections),
            ("Basic Prompt Generation", self.test_basic_prompt_generation),
            ("Template Processing", self.test_template_processing),
            ("RAG Functionality", self.test_rag_functionality),
            ("Error Handling", self.test_error_handling),
            ("Performance Metrics", self.test_performance_metrics),
            ("Firebase Integration", self.test_firebase_integration)
        ]
        
        for test_name, test_func in tests:
            print(f"\n🧪 Running: {test_name}")
            print("-" * 50)
            
            try:
                result = await test_func()
                self.test_results[test_name] = result
                
                if result['success']:
                    print(f"✅ {test_name}: PASSED")
                    if 'details' in result:
                        for detail in result['details']:
                            print(f"   • {detail}")
                else:
                    print(f"❌ {test_name}: FAILED")
                    print(f"   Error: {result.get('error', 'Unknown error')}")
                    
            except Exception as e:
                print(f"❌ {test_name}: EXCEPTION - {e}")
                self.test_results[test_name] = {
                    'success': False,
                    'error': str(e),
                    'exception': True
                }
        
        # Generate final report
        await self.generate_final_report()
    
    async def test_environment_setup(self) -> Dict[str, Any]:
        """Test environment setup and configuration"""
        details = []
        
        # Check required environment variables
        required_vars = ['OPENROUTER_API_KEY', 'GOOGLE_API_KEY']
        missing_vars = []
        
        for var in required_vars:
            if os.getenv(var):
                details.append(f"{var} is set")
            else:
                missing_vars.append(var)
        
        if missing_vars:
            return {
                'success': False,
                'error': f"Missing environment variables: {missing_vars}"
            }
        
        # Check Python dependencies
        try:
            import firebase_admin
            import openai
            import google.generativeai
            details.append("Core dependencies available")
        except ImportError as e:
            return {
                'success': False,
                'error': f"Missing Python dependencies: {e}"
            }
        
        return {
            'success': True,
            'details': details
        }
    
    async def test_ai_service_init(self) -> Dict[str, Any]:
        """Test AI service initialization"""
        try:
            from functions.src.ai_service import ai_service
            
            details = []
            
            # Check if AI service components are initialized
            if hasattr(ai_service, 'llm_manager'):
                details.append("LLM Manager initialized")
            
            if hasattr(ai_service, 'template_engine'):
                details.append("Template Engine initialized")
            
            if hasattr(ai_service, 'context_retriever'):
                details.append("Context Retriever initialized")
            
            if hasattr(ai_service, 'cost_tracker'):
                details.append("Cost Tracker initialized")
            
            return {
                'success': True,
                'details': details
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    async def test_provider_connections(self) -> Dict[str, Any]:
        """Test AI provider connections"""
        try:
            from functions.src.ai_service import ai_service
            
            providers = ['openrouter', 'google']
            details = []
            failed_providers = []
            
            for provider in providers:
                try:
                    result = await ai_service.test_provider_connection(provider)
                    if result['success']:
                        details.append(f"{provider}: Connected successfully")
                    else:
                        failed_providers.append(provider)
                        details.append(f"{provider}: Failed - {result.get('error', 'Unknown')}")
                except Exception as e:
                    failed_providers.append(provider)
                    details.append(f"{provider}: Exception - {str(e)}")
            
            # At least one provider should work
            if len(failed_providers) < len(providers):
                return {
                    'success': True,
                    'details': details,
                    'working_providers': len(providers) - len(failed_providers)
                }
            else:
                return {
                    'success': False,
                    'error': "All providers failed",
                    'details': details
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    async def test_basic_prompt_generation(self) -> Dict[str, Any]:
        """Test basic prompt generation"""
        try:
            from functions.src.ai_service import ai_service
            
            test_prompt = "Hello! Please respond with a brief greeting and confirm you're working correctly."
            
            result = await ai_service.generate_prompt_response(
                user_id="test_user",
                prompt_template=test_prompt,
                variables={},
                provider="openrouter",
                user_tier="free"
            )
            
            if result['success']:
                response = result['response']
                metadata = result.get('metadata', {})
                
                details = [
                    f"Response generated: {len(response)} characters",
                    f"Model: {metadata.get('model', 'Unknown')}",
                    f"Tokens used: {metadata.get('tokens_used', 0)}",
                    f"Response time: {metadata.get('total_time', 0):.2f}s",
                    f"Cost: ${metadata.get('cost', 0):.4f}"
                ]
                
                return {
                    'success': True,
                    'details': details,
                    'response_preview': response[:100] + "..." if len(response) > 100 else response
                }
            else:
                return {
                    'success': False,
                    'error': result.get('error', 'Unknown error')
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    async def test_template_processing(self) -> Dict[str, Any]:
        """Test template processing with variables"""
        try:
            from functions.src.ai_service import ai_service
            
            template = "Hello {name}! Please tell me about {topic} in {style} style."
            variables = {
                "name": "Alice",
                "topic": "artificial intelligence",
                "style": "simple"
            }
            
            result = await ai_service.generate_prompt_response(
                user_id="test_user",
                prompt_template=template,
                variables=variables,
                provider="openrouter",
                user_tier="free"
            )
            
            if result['success']:
                response = result['response']
                
                # Check if variables were properly substituted
                contains_name = "alice" in response.lower()
                contains_topic = "artificial intelligence" in response.lower() or "ai" in response.lower()
                
                details = [
                    f"Template processed successfully",
                    f"Response length: {len(response)} characters",
                    f"Contains name reference: {contains_name}",
                    f"Contains topic reference: {contains_topic}"
                ]
                
                return {
                    'success': True,
                    'details': details
                }
            else:
                return {
                    'success': False,
                    'error': result.get('error', 'Unknown error')
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    async def test_rag_functionality(self) -> Dict[str, Any]:
        """Test RAG functionality"""
        try:
            from functions.src.ai_service import ai_service
            
            query = "What is machine learning and how does it work?"
            
            result = await ai_service.generate_rag_response(
                user_id="test_user",
                query=query,
                max_context_tokens=1000,
                provider="openrouter"
            )
            
            if result['success']:
                response = result['response']
                metadata = result.get('metadata', {})
                
                details = [
                    f"RAG response generated: {len(response)} characters",
                    f"Context documents: {metadata.get('context_documents', 0)}",
                    f"Total time: {metadata.get('total_time', 0):.2f}s"
                ]
                
                return {
                    'success': True,
                    'details': details
                }
            else:
                return {
                    'success': False,
                    'error': result.get('error', 'Unknown error')
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    async def test_error_handling(self) -> Dict[str, Any]:
        """Test error handling and fallback mechanisms"""
        try:
            from functions.src.ai_service import ai_service
            
            # Test with invalid provider
            result = await ai_service.generate_prompt_response(
                user_id="test_user",
                prompt_template="Test prompt",
                variables={},
                provider="invalid_provider",
                user_tier="free"
            )
            
            # Should gracefully handle invalid provider
            details = [
                f"Invalid provider handled gracefully",
                f"Fallback mechanism triggered: {not result['success'] or 'fallback' in str(result)}"
            ]
            
            return {
                'success': True,
                'details': details
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    async def test_performance_metrics(self) -> Dict[str, Any]:
        """Test performance metrics and tracking"""
        try:
            from functions.src.ai_service import ai_service
            
            start_time = time.time()
            
            result = await ai_service.generate_prompt_response(
                user_id="test_user",
                prompt_template="Generate a short response about testing.",
                variables={},
                provider="openrouter",
                user_tier="free"
            )
            
            end_time = time.time()
            total_time = end_time - start_time
            
            if result['success']:
                metadata = result.get('metadata', {})
                
                details = [
                    f"Total execution time: {total_time:.2f}s",
                    f"AI response time: {metadata.get('total_time', 0):.2f}s",
                    f"Tokens tracked: {metadata.get('tokens_used', 0) > 0}",
                    f"Cost tracked: {metadata.get('cost', 0) >= 0}"
                ]
                
                return {
                    'success': True,
                    'details': details
                }
            else:
                return {
                    'success': False,
                    'error': result.get('error', 'Unknown error')
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    async def test_firebase_integration(self) -> Dict[str, Any]:
        """Test Firebase integration components"""
        try:
            # Check if Firebase components are available
            details = []
            
            try:
                import firebase_admin
                from firebase_admin import firestore
                details.append("Firebase Admin SDK available")
            except ImportError:
                return {
                    'success': False,
                    'error': "Firebase Admin SDK not available"
                }
            
            # Check if main.py functions are defined
            try:
                with open('functions/main.py', 'r') as f:
                    main_content = f.read()
                
                required_functions = [
                    'execute_prompt',
                    'test_openrouter_connection'
                ]
                
                for func in required_functions:
                    if func in main_content:
                        details.append(f"Function {func} defined")
                    else:
                        details.append(f"Function {func} missing")
                
            except Exception as e:
                details.append(f"Could not check main.py: {e}")
            
            return {
                'success': True,
                'details': details
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    async def generate_final_report(self):
        """Generate final test report"""
        end_time = datetime.now()
        duration = end_time - self.start_time
        
        print("\n" + "=" * 70)
        print("📊 END-TO-END TESTING FINAL REPORT")
        print("=" * 70)
        
        passed_tests = sum(1 for result in self.test_results.values() if result['success'])
        total_tests = len(self.test_results)
        pass_rate = (passed_tests / total_tests) * 100 if total_tests > 0 else 0
        
        print(f"Duration: {duration.total_seconds():.2f} seconds")
        print(f"Tests Run: {total_tests}")
        print(f"Passed: {passed_tests}")
        print(f"Failed: {total_tests - passed_tests}")
        print(f"Pass Rate: {pass_rate:.1f}%")
        
        print("\n📋 Test Results Summary:")
        for test_name, result in self.test_results.items():
            status = "✅ PASS" if result['success'] else "❌ FAIL"
            print(f"  {test_name:.<35} {status}")
        
        print("\n🎯 Overall Assessment:")
        if pass_rate >= 90:
            print("🎉 EXCELLENT: System is ready for production!")
        elif pass_rate >= 75:
            print("✅ GOOD: System is mostly functional with minor issues")
        elif pass_rate >= 50:
            print("⚠️  FAIR: System has significant issues that need attention")
        else:
            print("❌ POOR: System has critical issues and is not ready")
        
        print(f"\n📅 Test completed: {end_time.isoformat()}")
        
        return pass_rate >= 75

async def main():
    """Main testing function"""
    tester = EndToEndTester()
    success = await tester.run_all_tests()
    
    if success:
        print("\n🎉 End-to-end testing completed successfully!")
        sys.exit(0)
    else:
        print("\n❌ End-to-end testing revealed issues that need attention!")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
