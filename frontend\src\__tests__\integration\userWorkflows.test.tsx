/**
 * User Workflow Integration Tests
 * 
 * Tests critical user workflows and component interactions to ensure
 * the application works correctly from end-to-end user perspective.
 * 
 * @module UserWorkflowTests
 * @version 1.0.0
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { render, screen, fireEventcleanup } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import React from 'react';

// Mock Firebase
vi.mock('../../config/firebase', () => ({
  auth: {
    currentUser: { uid: 'test-user-id', email: '<EMAIL>' },
    onAuthStateChanged: vi.fn((callback) => {
      callback({ uid: 'test-user-id', email: '<EMAIL>' });
      return () => {};
    }),
  },
  db: {},
  storage: {},
}));

// Mock services
vi.mock('../../services/documentService', () => ({
  DocumentService: {
    getUserDocuments: vi.fn().mockResolvedValue([
      {
        id: 'doc-1',
        title: 'Test Document',
        content: 'Test content',
        status: 'processed',
        createdAt: new Date(),
      },
    ]),
    uploadDocument: vi.fn().mockResolvedValue({
      id: 'new-doc-id',
      title: 'New Document',
      status: 'processing',
    }),
    deleteDocument: vi.fn().mockResolvedValue(undefined),
  },
}));

vi.mock('../../services/firestore', () => ({
  promptService: {
    getUserPrompts: vi.fn().mockResolvedValue([
      {
        id: 'prompt-1',
        title: 'Test Prompt',
        content: 'Test prompt content',
        category: 'general',
        createdAt: new Date(),
      },
    ]),
    createPrompt: vi.fn().mockResolvedValue({
      id: 'new-prompt-id',
      title: 'New Prompt',
      content: 'New prompt content',
    }),
    updatePrompt: vi.fn().mockResolvedValue(undefined),
    deletePrompt: vi.fn().mockResolvedValue(undefined),
  },
}));

// Test wrapper component
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });

  return (
    <QueryClientProvider client={queryClient}>
      <BrowserRouter>
        {children}
      </BrowserRouter>
    </QueryClientProvider>
  );
};

// Mock components for testing
const MockDashboard = () => (
  <div data-testid="dashboard">
    <h1>Dashboard</h1>
    <button data-testid="create-prompt-btn">Create Prompt</button>
    <button data-testid="upload-document-btn">Upload Document</button>
    <div data-testid="recent-prompts">Recent Prompts</div>
    <div data-testid="recent-documents">Recent Documents</div>
  </div>
);

const MockPromptEditor = () => (
  <div data-testid="prompt-editor">
    <h2>Prompt Editor</h2>
    <input data-testid="prompt-title" placeholder="Enter prompt title" />
    <textarea data-testid="prompt-content" placeholder="Enter prompt content" />
    <button data-testid="save-prompt-btn">Save Prompt</button>
    <button data-testid="generate-ai-btn">Generate with AI</button>
  </div>
);

const MockDocumentUpload = () => (
  <div data-testid="document-upload">
    <h2>Document Upload</h2>
    <input data-testid="file-input" type="file" />
    <button data-testid="upload-btn">Upload</button>
    <div data-testid="upload-progress">Upload Progress: 0%</div>
  </div>
);

describe('User Workflow Integration Tests', () => {
  let queryClient: QueryClient;

  beforeEach(() => {
    queryClient = new QueryClient({
      defaultOptions: {
        queries: { retry: false },
        mutations: { retry: false },
      },
    });
    vi.clearAllMocks();
  });

  afterEach(() => {
    cleanup();
  });

  describe('Dashboard Navigation Workflow', () => {
    it('should navigate through main dashboard features', async () => {
      render(
        <TestWrapper>
          <MockDashboard />
        </TestWrapper>
      );

      // Verify dashboard loads
      expect(screen.getByTestId('dashboard')).toBeInTheDocument();
      expect(screen.getByText('Dashboard')).toBeInTheDocument();

      // Verify navigation elements are present
      expect(screen.getByTestId('create-prompt-btn')).toBeInTheDocument();
      expect(screen.getByTestId('upload-document-btn')).toBeInTheDocument();
      expect(screen.getByTestId('recent-prompts')).toBeInTheDocument();
      expect(screen.getByTestId('recent-documents')).toBeInTheDocument();
    });

    it('should handle dashboard interactions', async () => {
      render(
        <TestWrapper>
          <MockDashboard />
        </TestWrapper>
      );

      // Test create prompt button
      const createPromptBtn = screen.getByTestId('create-prompt-btn');
      fireEvent.click(createPromptBtn);
      
      // Test upload document button
      const uploadDocumentBtn = screen.getByTestId('upload-document-btn');
      fireEvent.click(uploadDocumentBtn);

      // Verify buttons are clickable
      expect(createPromptBtn).toBeInTheDocument();
      expect(uploadDocumentBtn).toBeInTheDocument();
    });
  });

  describe('Prompt Creation Workflow', () => {
    it('should complete full prompt creation workflow', async () => {
      render(
        <TestWrapper>
          <MockPromptEditor />
        </TestWrapper>
      );

      // Verify prompt editor loads
      expect(screen.getByTestId('prompt-editor')).toBeInTheDocument();
      expect(screen.getByText('Prompt Editor')).toBeInTheDocument();

      // Fill in prompt details
      const titleInput = screen.getByTestId('prompt-title');
      const contentTextarea = screen.getByTestId('prompt-content');
      
      fireEvent.change(titleInput, { target: { value: 'Test Prompt Title' } });
      fireEvent.change(contentTextarea, { target: { value: 'Test prompt content for integration testing' } });

      // Verify input values
      expect(titleInput).toHaveValue('Test Prompt Title');
      expect(contentTextarea).toHaveValue('Test prompt content for integration testing');

      // Test save functionality
      const saveBtn = screen.getByTestId('save-prompt-btn');
      fireEvent.click(saveBtn);

      // Test AI generation
      const generateBtn = screen.getByTestId('generate-ai-btn');
      fireEvent.click(generateBtn);

      // Verify buttons are functional
      expect(saveBtn).toBeInTheDocument();
      expect(generateBtn).toBeInTheDocument();
    });

    it('should handle prompt validation and error states', async () => {
      render(
        <TestWrapper>
          <MockPromptEditor />
        </TestWrapper>
      );

      // Try to save without content
      const saveBtn = screen.getByTestId('save-prompt-btn');
      fireEvent.click(saveBtn);

      // Verify form elements are present for validation
      expect(screen.getByTestId('prompt-title')).toBeInTheDocument();
      expect(screen.getByTestId('prompt-content')).toBeInTheDocument();
    });
  });

  describe('Document Upload Workflow', () => {
    it('should complete document upload workflow', async () => {
      render(
        <TestWrapper>
          <MockDocumentUpload />
        </TestWrapper>
      );

      // Verify upload interface loads
      expect(screen.getByTestId('document-upload')).toBeInTheDocument();
      expect(screen.getByText('Document Upload')).toBeInTheDocument();

      // Test file input
      const fileInput = screen.getByTestId('file-input');
      expect(fileInput).toBeInTheDocument();

      // Test upload button
      const uploadBtn = screen.getByTestId('upload-btn');
      fireEvent.click(uploadBtn);

      // Verify upload progress indicator
      expect(screen.getByTestId('upload-progress')).toBeInTheDocument();
    });

    it('should handle file selection and upload progress', async () => {
      render(
        <TestWrapper>
          <MockDocumentUpload />
        </TestWrapper>
      );

      // Simulate file selection
      const fileInput = screen.getByTestId('file-input');
      const file = new File(['test content'], 'test.txt', { type: 'text/plain' });
      
      Object.defineProperty(fileInput, 'files', {
        value: [file],
        writable: false,
      });

      fireEvent.change(fileInput);

      // Verify file input functionality
      expect(fileInput).toBeInTheDocument();
    });
  });

  describe('Cross-Component Integration', () => {
    it('should handle data flow between components', async () => {
      // Test data sharing between dashboard and editors
      const { rerender } = render(
        <TestWrapper>
          <MockDashboard />
        </TestWrapper>
      );

      // Switch to prompt editor
      rerender(
        <TestWrapper>
          <MockPromptEditor />
        </TestWrapper>
      );

      // Verify component switching works
      expect(screen.getByTestId('prompt-editor')).toBeInTheDocument();

      // Switch to document upload
      rerender(
        <TestWrapper>
          <MockDocumentUpload />
        </TestWrapper>
      );

      // Verify component switching works
      expect(screen.getByTestId('document-upload')).toBeInTheDocument();
    });

    it('should maintain state consistency across navigation', async () => {
      render(
        <TestWrapper>
          <MockDashboard />
        </TestWrapper>
      );

      // Verify initial state
      expect(screen.getByTestId('dashboard')).toBeInTheDocument();

      // Test state persistence through navigation
      const createBtn = screen.getByTestId('create-prompt-btn');
      fireEvent.click(createBtn);

      // Verify interaction tracking
      expect(createBtn).toBeInTheDocument();
    });
  });

  describe('Error Handling Integration', () => {
    it('should handle network errors gracefully', async () => {
      // Mock network error
      vi.mocked(require('../../services/firestore').promptService.getUserPrompts)
        .mockRejectedValueOnce(new Error('Network error'));

      render(
        <TestWrapper>
          <MockDashboard />
        </TestWrapper>
      );

      // Verify error handling doesn't break the UI
      expect(screen.getByTestId('dashboard')).toBeInTheDocument();
    });

    it('should handle authentication errors', async () => {
      // Mock auth error
      vi.mocked(require('../../config/firebase').auth.currentUser) = null;

      render(
        <TestWrapper>
          <MockDashboard />
        </TestWrapper>
      );

      // Verify graceful handling of auth errors
      expect(screen.getByTestId('dashboard')).toBeInTheDocument();
    });
  });

  describe('Performance Integration', () => {
    it('should handle large data sets efficiently', async () => {
      // Mock large dataset
      const largePromptList = Array.from({ length: 100 }, (_, i) => ({
        id: `prompt-${i}`,
        title: `Prompt ${i}`,
        content: `Content for prompt ${i}`,
        category: 'test',
        createdAt: new Date(),
      }));

      vi.mocked(require('../../services/firestore').promptService.getUserPrompts)
        .mockResolvedValueOnce(largePromptList);

      render(
        <TestWrapper>
          <MockDashboard />
        </TestWrapper>
      );

      // Verify performance with large datasets
      expect(screen.getByTestId('dashboard')).toBeInTheDocument();
    });

    it('should handle concurrent operations', async () => {
      render(
        <TestWrapper>
          <MockPromptEditor />
        </TestWrapper>
      );

      // Simulate concurrent operations
      const saveBtn = screen.getByTestId('save-prompt-btn');
      const generateBtn = screen.getByTestId('generate-ai-btn');

      // Click both buttons rapidly
      fireEvent.click(saveBtn);
      fireEvent.click(generateBtn);

      // Verify concurrent operation handling
      expect(saveBtn).toBeInTheDocument();
      expect(generateBtn).toBeInTheDocument();
    });
  });
});
