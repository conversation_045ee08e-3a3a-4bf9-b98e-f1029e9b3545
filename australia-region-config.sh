#!/bin/bash

# Australia Region Configuration Script
# Configures Firebase project for optimal Australia region hosting

echo "🇦🇺 Configuring Firebase for Australia Region Hosting..."
echo "=" * 60

# Check if Firebase CLI is installed
if ! command -v firebase &> /dev/null; then
    echo "❌ Firebase CLI is not installed. Please install it first:"
    echo "npm install -g firebase-tools"
    exit 1
fi

# Check if logged in to Firebase
if ! firebase projects:list &> /dev/null; then
    echo "❌ Not logged in to Firebase. Please login first:"
    echo "firebase login"
    exit 1
fi

echo "📋 Current Firebase project:"
firebase use

echo ""
echo "🌏 Setting up Australia region configuration..."

# 1. Configure Functions for Australia region
echo "1️⃣ Configuring Firebase Functions for australia-southeast1..."
firebase functions:config:set region.primary="australia-southeast1"
firebase functions:config:set region.fallback="us-central1"

# 2. Set timezone and locale
echo "2️⃣ Setting Australia timezone and locale..."
firebase functions:config:set app.timezone="Australia/Sydney"
firebase functions:config:set app.locale="en-AU"
firebase functions:config:set app.currency="AUD"

# 3. Configure performance settings for Australia
echo "3️⃣ Optimizing performance settings for Australia..."
firebase functions:config:set performance.timeout="540s"
firebase functions:config:set performance.memory="2GB"
firebase functions:config:set performance.max_instances="50"
firebase functions:config:set performance.min_instances="1"

# 4. Set CORS for Australian domains
echo "4️⃣ Configuring CORS for Australian hosting..."
firebase functions:config:set cors.origins="https://your-domain.com.au,https://your-domain.com,http://localhost:3000"
firebase functions:config:set cors.methods="GET,POST,OPTIONS,PUT,DELETE"
firebase functions:config:set cors.headers="Content-Type,Authorization,X-Requested-With"

# 5. Configure monitoring and logging
echo "5️⃣ Setting up monitoring for Australia region..."
firebase functions:config:set monitoring.region="australia-southeast1"
firebase functions:config:set logging.level="INFO"
firebase functions:config:set logging.timezone="Australia/Sydney"

# 6. Set data residency compliance
echo "6️⃣ Configuring data residency compliance..."
firebase functions:config:set compliance.data_residency="australia"
firebase functions:config:set compliance.privacy_policy="https://your-domain.com/privacy"
firebase functions:config:set compliance.terms_of_service="https://your-domain.com/terms"

# 7. Configure backup and disaster recovery
echo "7️⃣ Setting up backup configuration..."
firebase functions:config:set backup.region="australia-southeast1"
firebase functions:config:set backup.frequency="daily"
firebase functions:config:set backup.retention="30"

# 8. Set Australian business hours for rate limiting
echo "8️⃣ Configuring Australian business hours..."
firebase functions:config:set business_hours.timezone="Australia/Sydney"
firebase functions:config:set business_hours.start="09:00"
firebase functions:config:set business_hours.end="17:00"
firebase functions:config:set business_hours.days="monday,tuesday,wednesday,thursday,friday"

echo ""
echo "✅ Australia region configuration completed!"
echo ""
echo "📋 Configuration Summary:"
echo "• Primary region: australia-southeast1"
echo "• Timezone: Australia/Sydney"
echo "• Locale: en-AU"
echo "• Currency: AUD"
echo "• Data residency: Australia"
echo "• Performance optimized for Australian users"
echo ""
echo "🔄 Next steps:"
echo "1. Deploy functions: ./deploy-functions.sh"
echo "2. Test latency from Australia"
echo "3. Monitor performance in Firebase Console"
echo "4. Update DNS settings if using custom domain"
echo ""
echo "🌐 Your functions will be available at:"
echo "https://australia-southeast1-$(firebase use).cloudfunctions.net/"
