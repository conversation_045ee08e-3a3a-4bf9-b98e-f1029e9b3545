/**
 * Route prefetching utilities for improved performance
 * Prefetches critical routes based on user behavior and navigation patterns
 */

interface PrefetchOptions {
  priority?: 'high' | 'low';
  delay?: number;
  condition?: () => boolean;
}

class RoutePrefetcher {
  private prefetchedRoutes = new Set<string>();
  private prefetchPromises = new Map<string, Promise<any>>();

  /**
   * Prefetch a route component
   */
  async prefetchRoute(routeName: string, options: PrefetchOptions = {}) {
    const { priority = 'low', delay = 0, condition = () => true } = options;

    // Skip if already prefetched or condition not met
    if (this.prefetchedRoutes.has(routeName) || !condition()) {
      return;
    }

    // Add delay if specified
    if (delay > 0) {
      await new Promise(resolve => setTimeout(resolve, delay));
    }

    // Skip if condition changed during delay
    if (!condition()) {
      return;
    }

    try {
      let prefetchPromise = this.prefetchPromises.get(routeName);
      
      if (!prefetchPromise) {
        prefetchPromise = this.getRouteImport(routeName);
        this.prefetchPromises.set(routeName, prefetchPromise);
      }

      await prefetchPromise;
      this.prefetchedRoutes.add(routeName);
      
      console.log(`✅ Prefetched route: ${routeName} (priority: ${priority})`);
    } catch (error) {
      console.warn(`❌ Failed to prefetch route: ${routeName}`, error);
      this.prefetchPromises.delete(routeName);
    }
  }

  /**
   * Get the import function for a route
   */
  private getRouteImport(routeName: string): Promise<any> {
    const routeImports: Record<string, () => Promise<any>> = {
      'dashboard': () => import('../pages/Dashboard'),
      'prompts': () => import('../pages/Prompts'),
      'documents': () => import('../pages/Documents'),
      'executions': () => import('../pages/Executions'),
      'execute-prompt': () => import('../pages/ExecutePrompt'),
      'analytics': () => import('../pages/Analytics'),
      'workspaces': () => import('../pages/Workspaces'),
      'settings': () => import('../pages/Settings'),
      'beta-signup': () => import('../pages/BetaSignup'),
    };

    const importFn = routeImports[routeName];
    if (!importFn) {
      throw new Error(`Unknown route: ${routeName}`);
    }

    return importFn();
  }

  /**
   * Prefetch critical routes based on user authentication status
   */
  prefetchCriticalRoutes(isAuthenticated: boolean) {
    if (isAuthenticated) {
      // Prefetch most commonly used authenticated routes
      this.prefetchRoute('dashboard', { priority: 'high', delay: 100 });
      this.prefetchRoute('prompts', { priority: 'high', delay: 200 });
      this.prefetchRoute('documents', { priority: 'low', delay: 500 });
    } else {
      // Prefetch beta signup for unauthenticated users
      this.prefetchRoute('beta-signup', { priority: 'low', delay: 1000 });
    }
  }

  /**
   * Prefetch routes on hover (for navigation links)
   */
  prefetchOnHover(routeName: string) {
    this.prefetchRoute(routeName, { 
      priority: 'high',
      delay: 50, // Small delay to avoid prefetching on accidental hovers
    });
  }

  /**
   * Prefetch routes on idle (when browser is not busy)
   */
  prefetchOnIdle(routeNames: string[]) {
    if ('requestIdleCallback' in window) {
      window.requestIdleCallback(() => {
        routeNames.forEach((routeName, index) => {
          this.prefetchRoute(routeName, {
            priority: 'low',
            delay: index * 100, // Stagger prefetching
          });
        });
      });
    } else {
      // Fallback for browsers without requestIdleCallback
      setTimeout(() => {
        routeNames.forEach((routeName, index) => {
          this.prefetchRoute(routeName, {
            priority: 'low',
            delay: index * 100,
          });
        });
      }, 2000);
    }
  }

  /**
   * Check if a route has been prefetched
   */
  isPrefetched(routeName: string): boolean {
    return this.prefetchedRoutes.has(routeName);
  }

  /**
   * Clear prefetch cache
   */
  clearCache() {
    this.prefetchedRoutes.clear();
    this.prefetchPromises.clear();
  }
}

// Export singleton instance
export const routePrefetcher = new RoutePrefetcher();

/**
 * Hook for prefetching routes in React components
 */
export function usePrefetchRoute() {
  return {
    prefetchRoute: routePrefetcher.prefetchRoute.bind(routePrefetcher),
    prefetchOnHover: routePrefetcher.prefetchOnHover.bind(routePrefetcher),
    isPrefetched: routePrefetcher.isPrefetched.bind(routePrefetcher),
  };
}

/**
 * Initialize route prefetching based on user state
 */
export function initializeRoutePrefetching(isAuthenticated: boolean) {
  // Prefetch critical routes
  routePrefetcher.prefetchCriticalRoutes(isAuthenticated);

  // Prefetch additional routes on idle
  const idleRoutes = isAuthenticated 
    ? ['executions', 'analytics', 'settings']
    : [];
  
  if (idleRoutes.length > 0) {
    routePrefetcher.prefetchOnIdle(idleRoutes);
  }
}
