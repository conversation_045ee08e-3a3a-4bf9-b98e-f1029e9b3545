import type { WebVitalMetric } from './webVitals';

export interface AlertRule {
  metric: string;
  threshold: number;
  severity: 'warning' | 'critical';
  condition: 'greater_than' | 'less_than';
  enabled: boolean;
}

export interface Alert {
  id: string;
  rule: AlertRule;
  metric: WebVitalMetric;
  timestamp: number;
  acknowledged: boolean;
  message: string;
}

export interface AlertingConfig {
  enabled: boolean;
  rules: AlertRule[];
  webhookUrl?: string;
  emailEndpoint?: string;
  slackWebhook?: string;
  retryAttempts: number;
  retryDelay: number;
}

/**
 * Production-ready Web Vitals alerting system
 * Handles threshold monitoring, alert generation, and notification delivery
 */
export class WebVitalsAlerting {
  private config: AlertingConfig;
  private alerts: Alert[] = [];
  private listeners: Array<(alert: Alert) => void> = [];

  constructor(config: Partial<AlertingConfig> = {}) {
    this.config = {
      enabled: true,
      rules: [
        // Critical thresholds (Core Web Vitals)
        { metric: 'LCP', threshold: 2500, severity: 'critical', condition: 'greater_than', enabled: true },
        { metric: 'FID', threshold: 100, severity: 'critical', condition: 'greater_than', enabled: true },
        { metric: 'CLS', threshold: 0.1, severity: 'critical', condition: 'greater_than', enabled: true },
        
        // Warning thresholds
        { metric: 'LCP', threshold: 2000, severity: 'warning', condition: 'greater_than', enabled: true },
        { metric: 'FID', threshold: 75, severity: 'warning', condition: 'greater_than', enabled: true },
        { metric: 'CLS', threshold: 0.05, severity: 'warning', condition: 'greater_than', enabled: true },
        { metric: 'FCP', threshold: 1800, severity: 'warning', condition: 'greater_than', enabled: true },
        { metric: 'TTFB', threshold: 600, severity: 'warning', condition: 'greater_than', enabled: true },
      ],
      retryAttempts: 3,
      retryDelay: 1000,
      ...config
    };
  }

  /**
   * Check metric against alert rules
   */
  checkMetric(metric: WebVitalMetric): Alert[] {
    if (!this.config.enabled) return [];

    const triggeredAlerts: Alert[] = [];
    
    for (const rule of this.config.rules) {
      if (!rule.enabled || rule.metric !== metric.name) continue;

      const shouldAlert = rule.condition === 'greater_than' 
        ? metric.value > rule.threshold
        : metric.value < rule.threshold;

      if (shouldAlert) {
        const alert: Alert = {
          id: this.generateAlertId(),
          rule,
          metric,
          timestamp: Date.now(),
          acknowledged: false,
          message: this.generateAlertMessage(rule, metric)
        };

        triggeredAlerts.push(alert);
        this.alerts.push(alert);
        
        // Notify listeners
        this.listeners.forEach(listener => listener(alert));
        
        // Send notifications
        this.sendNotifications(alert);
      }
    }

    // Clean up old alerts (keep last 100)
    this.alerts = this.alerts.slice(-100);
    
    return triggeredAlerts;
  }

  /**
   * Generate alert message
   */
  private generateAlertMessage(rule: AlertRule, metric: WebVitalMetric): string {
    const unit = this.getMetricUnit(metric.name);
    const formattedValue = metric.value.toFixed(metric.name === 'CLS' ? 3 : 0);
    const formattedThreshold = rule.threshold.toFixed(metric.name === 'CLS' ? 3 : 0);
    
    return `${rule.severity.toUpperCase()}: ${metric.name} performance issue detected. ` +
           `Current: ${formattedValue}${unit}, Threshold: ${formattedThreshold}${unit}. ` +
           `Rating: ${metric.rating}. URL: ${window.location.href}`;
  }

  /**
   * Get metric unit for display
   */
  private getMetricUnit(metricName: string): string {
    switch (metricName) {
      case 'LCP':
      case 'FID':
      case 'FCP':
      case 'TTFB':
        return 'ms';
      case 'CLS':
        return '';
      default:
        return '';
    }
  }

  /**
   * Send notifications for alert
   */
  private async sendNotifications(alert: Alert): Promise<void> {
    const notifications = [];

    // Webhook notification
    if (this.config.webhookUrl) {
      notifications.push(this.sendWebhook(alert));
    }

    // Email notification
    if (this.config.emailEndpoint) {
      notifications.push(this.sendEmail(alert));
    }

    // Slack notification
    if (this.config.slackWebhook) {
      notifications.push(this.sendSlack(alert));
    }

    // Wait for all notifications
    try {
      await Promise.allSettled(notifications);
    } catch (error) {
      console.error('Failed to send some notifications:', error);
    }
  }

  /**
   * Send webhook notification
   */
  private async sendWebhook(alert: Alert): Promise<void> {
    if (!this.config.webhookUrl) return;

    const payload = {
      alert_id: alert.id,
      severity: alert.rule.severity,
      metric: alert.metric.name,
      value: alert.metric.value,
      threshold: alert.rule.threshold,
      rating: alert.metric.rating,
      message: alert.message,
      timestamp: alert.timestamp,
      url: window.location.href,
      user_agent: navigator.userAgent
    };

    await this.retryRequest(() => 
      fetch(this.config.webhookUrl!, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload)
      })
    );
  }

  /**
   * Send email notification
   */
  private async sendEmail(alert: Alert): Promise<void> {
    if (!this.config.emailEndpoint) return;

    const payload = {
      to: process.env.VITE_ALERT_EMAIL || '<EMAIL>',
      subject: `Web Vitals Alert: ${alert.rule.severity.toUpperCase()} - ${alert.metric.name}`,
      body: alert.message,
      alert_data: {
        metric: alert.metric.name,
        value: alert.metric.value,
        threshold: alert.rule.threshold,
        url: window.location.href,
        timestamp: new Date(alert.timestamp).toISOString()
      }
    };

    await this.retryRequest(() =>
      fetch(this.config.emailEndpoint!, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload)
      })
    );
  }

  /**
   * Send Slack notification
   */
  private async sendSlack(alert: Alert): Promise<void> {
    if (!this.config.slackWebhook) return;

    const color = alert.rule.severity === 'critical' ? 'danger' : 'warning';
    const emoji = alert.rule.severity === 'critical' ? '🚨' : '⚠️';
    
    const payload = {
      text: `${emoji} Web Vitals Alert`,
      attachments: [{
        color,
        fields: [
          { title: 'Metric', value: alert.metric.name, short: true },
          { title: 'Value', value: `${alert.metric.value.toFixed(2)}${this.getMetricUnit(alert.metric.name)}`, short: true },
          { title: 'Threshold', value: `${alert.rule.threshold}${this.getMetricUnit(alert.metric.name)}`, short: true },
          { title: 'Rating', value: alert.metric.rating, short: true },
          { title: 'URL', value: window.location.href, short: false },
          { title: 'Time', value: new Date(alert.timestamp).toISOString(), short: true }
        ]
      }]
    };

    await this.retryRequest(() =>
      fetch(this.config.slackWebhook!, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload)
      })
    );
  }

  /**
   * Retry request with exponential backoff
   */
  private async retryRequest(requestFn: () => Promise<Response>): Promise<void> {
    let lastError: Error | null = null;
    
    for (let attempt = 0; attempt < this.config.retryAttempts; attempt++) {
      try {
        const response = await requestFn();
        if (response.ok) return;
        
        lastError = new Error(`HTTP ${response.status}: ${response.statusText}`);
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));
      }
      
      if (attempt < this.config.retryAttempts - 1) {
        await new Promise(resolve => 
          setTimeout(resolve, this.config.retryDelay * Math.pow(2, attempt))
        );
      }
    }
    
    throw lastError;
  }

  /**
   * Generate unique alert ID
   */
  private generateAlertId(): string {
    return `alert_${Date.now()}_${Math.random().toString(36).slice(2)}`;
  }

  /**
   * Subscribe to alerts
   */
  onAlert(callback: (alert: Alert) => void): () => void {
    this.listeners.push(callback);
    return () => {
      const index = this.listeners.indexOf(callback);
      if (index > -1) this.listeners.splice(index, 1);
    };
  }

  /**
   * Get all alerts
   */
  getAlerts(): Alert[] {
    return [...this.alerts];
  }

  /**
   * Acknowledge alert
   */
  acknowledgeAlert(alertId: string): void {
    const alert = this.alerts.find(a => a.id === alertId);
    if (alert) {
      alert.acknowledged = true;
    }
  }

  /**
   * Clear all alerts
   */
  clearAlerts(): void {
    this.alerts = [];
  }

  /**
   * Update configuration
   */
  updateConfig(config: Partial<AlertingConfig>): void {
    this.config = { ...this.config, ...config };
  }
}

// Global alerting instance
export const webVitalsAlerting = new WebVitalsAlerting({
  enabled: process.env.NODE_ENV === 'production' || process.env.VITE_ENABLE_WEB_VITALS_ALERTS === 'true',
  webhookUrl: process.env.VITE_WEBHOOK_URL,
  emailEndpoint: process.env.VITE_EMAIL_ENDPOINT,
  slackWebhook: process.env.VITE_SLACK_WEBHOOK
});
