/**
 * Error Handling Configuration
 * Centralized configuration for error handling across the application
 */

import { errorHandler } from '@/utils/errorHandler';

// Error reporting service configuration
export const configureErrorReporting = () => {
  // Configure error reporting service (e.g., Sentry, LogRocket, etc.)
  if (process.env.NODE_ENV === 'production') {
    errorHandler.setErrorReportingService((error, context) => {
      // Example: Send to external error reporting service
      console.log('Reporting error to external service:', {
        error: {
          name: error.name,
          message: error.message,
          code: error.code,
          category: error.category,
          severity: error.severity,
          correlationId: error.correlationId,
          stack: error.stack
        },
        context,
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent,
        url: window.location.href
      });

      // In a real application, you would send this to your error reporting service:
      // Sentry.captureException(error, { extra: context });
      // LogRocket.captureException(error);
      // Or your custom error reporting endpoint
    });
  }
};

// Global error handlers
export const setupGlobalErrorHandlers = () => {
  // Handle unhandled promise rejections
  window.addEventListener('unhandledrejection', (event) => {
    console.error('Unhandled promise rejection:', event.reason);
    
    errorHandler.handleError(event.reason, {
      context: {
        component: 'Global',
        action: 'unhandledrejection',
        url: window.location.href
      },
      showToast: true,
      reportToService: true
    });

    // Prevent the default browser behavior
    event.preventDefault();
  });

  // Handle uncaught errors
  window.addEventListener('error', (event) => {
    console.error('Uncaught error:', event.error);
    
    errorHandler.handleError(event.error, {
      context: {
        component: 'Global',
        action: 'uncaught_error',
        url: window.location.href,
        metadata: {
          filename: event.filename,
          lineno: event.lineno,
          colno: event.colno
        }
      },
      showToast: true,
      reportToService: true
    });
  });

  // Handle resource loading errors
  window.addEventListener('error', (event) => {
    if (event.target !== window) {
      const target = event.target as HTMLElement;
      console.error('Resource loading error:', target);
      
      errorHandler.handleError(new Error(`Failed to load resource: ${target.tagName}`), {
        context: {
          component: 'Global',
          action: 'resource_error',
          url: window.location.href,
          metadata: {
            tagName: target.tagName,
            src: (target as any).src || (target as any).href
          }
        },
        showToast: false, // Don't show toast for resource errors
        reportToService: true
      });
    }
  }, true);
};

// Development mode error configuration
export const setupDevelopmentErrorHandling = () => {
  if (process.env.NODE_ENV === 'development') {
    // Enhanced console logging in development
    const originalConsoleError = console.error;
    console.error = (...args) => {
      originalConsoleError.apply(console, args);
      
      // Add stack trace enhancement in development
      if (args[0] instanceof Error) {
        console.group('🔍 Error Details');
        console.log('Stack trace:', args[0].stack);
        console.log('Error properties:', Object.getOwnPropertyNames(args[0]));
        console.groupEnd();
      }
    };

    // React DevTools integration
    if (typeof window !== 'undefined' && (window as any).__REACT_DEVTOOLS_GLOBAL_HOOK__) {
      const devTools = (window as any).__REACT_DEVTOOLS_GLOBAL_HOOK__;
      
      // Hook into React DevTools for error tracking
      if (devTools.onCommitFiberRoot) {
        const originalOnCommitFiberRoot = devTools.onCommitFiberRoot;
        devTools.onCommitFiberRoot = (id: any, root: any, ...args: any[]) => {
          try {
            return originalOnCommitFiberRoot(id, root, ...args);
          } catch (error) {
            console.error('React DevTools error:', error);
            errorHandler.handleError(error, {
              context: { component: 'ReactDevTools', action: 'commit' }
            });
          }
        };
      }
    }
  }
};

// Performance monitoring integration
export const setupPerformanceMonitoring = () => {
  // Monitor long tasks
  if ('PerformanceObserver' in window) {
    try {
      const observer = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (entry.duration > 50) { // Tasks longer than 50ms
            console.warn('Long task detected:', entry);
            
            errorHandler.handleError(new Error('Long task detected'), {
              context: {
                component: 'Performance',
                action: 'long_task',
                metadata: {
                  duration: entry.duration,
                  startTime: entry.startTime,
                  name: entry.name
                }
              },
              showToast: false,
              reportToService: process.env.NODE_ENV === 'production'
            });
          }
        }
      });

      observer.observe({ entryTypes: ['longtask'] });
    } catch (error) {
      console.warn('Performance monitoring not available:', error);
    }
  }

  // Monitor memory usage
  if ('memory' in performance) {
    const checkMemoryUsage = () => {
      const memory = (performance as any).memory;
      const usedPercent = (memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100;
      
      if (usedPercent > 90) {
        console.warn('High memory usage detected:', memory);
        
        errorHandler.handleError(new Error('High memory usage'), {
          context: {
            component: 'Performance',
            action: 'memory_warning',
            metadata: {
              usedJSHeapSize: memory.usedJSHeapSize,
              totalJSHeapSize: memory.totalJSHeapSize,
              jsHeapSizeLimit: memory.jsHeapSizeLimit,
              usedPercent
            }
          },
          showToast: false,
          reportToService: true
        });
      }
    };

    // Check memory usage every 30 seconds
    setInterval(checkMemoryUsage, 30000);
  }
};

// Network error monitoring
export const setupNetworkMonitoring = () => {
  // Monitor network status
  const handleOnline = () => {
    console.log('Network connection restored');
  };

  const handleOffline = () => {
    console.warn('Network connection lost');
    
    errorHandler.handleError(new Error('Network connection lost'), {
      context: {
        component: 'Network',
        action: 'offline'
      },
      showToast: true,
      fallbackMessage: 'You are currently offline. Some features may not work properly.',
      reportToService: false
    });
  };

  window.addEventListener('online', handleOnline);
  window.addEventListener('offline', handleOffline);

  // Monitor fetch failures
  const originalFetch = window.fetch;
  window.fetch = async (...args) => {
    try {
      const response = await originalFetch(...args);
      
      if (!response.ok) {
        console.warn('Fetch request failed:', response.status, response.statusText);
      }
      
      return response;
    } catch (error) {
      console.error('Fetch error:', error);
      
      errorHandler.handleError(error, {
        context: {
          component: 'Network',
          action: 'fetch_error',
          metadata: {
            url: typeof args[0] === 'string' ? args[0] : args[0]?.url,
            method: args[1]?.method || 'GET'
          }
        },
        showToast: false, // Let individual components handle toast notifications
        reportToService: true
      });
      
      throw error;
    }
  };
};

// Initialize all error handling
export const initializeErrorHandling = () => {
  console.log('🛡️ Initializing error handling system...');
  
  configureErrorReporting();
  setupGlobalErrorHandlers();
  setupNetworkMonitoring();
  setupPerformanceMonitoring();
  
  if (process.env.NODE_ENV === 'development') {
    setupDevelopmentErrorHandling();
  }
  
  console.log('✅ Error handling system initialized');
};

// Error handling utilities for specific scenarios
export const errorUtils = {
  // Handle API errors with user-friendly messages
  handleApiError: (error: any, operation: string) => {
    return errorHandler.handleError(error, {
      context: {
        component: 'API',
        action: operation
      },
      showToast: true,
      reportToService: true
    });
  },

  // Handle form validation errors
  handleValidationError: (errors: Record<string, string>, formName: string) => {
    const errorMessages = Object.values(errors).join(', ');
    return errorHandler.handleError(new Error(`Validation failed: ${errorMessages}`), {
      context: {
        component: 'Form',
        action: 'validation',
        metadata: { formName, errors }
      },
      showToast: true,
      fallbackMessage: 'Please correct the highlighted fields and try again.',
      reportToService: false
    });
  },

  // Handle file operation errors
  handleFileError: (error: any, operation: string, filename?: string) => {
    return errorHandler.handleError(error, {
      context: {
        component: 'File',
        action: operation,
        metadata: { filename }
      },
      showToast: true,
      reportToService: true
    });
  }
};
