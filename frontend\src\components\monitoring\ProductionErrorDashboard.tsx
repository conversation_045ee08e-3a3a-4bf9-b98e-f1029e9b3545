import React, { useState, useEffect } from 'react';
import { productionErrorTracker } from '@/utils/productionErrorTracking';
import type { ErrorAlert, ErrorMetrics } from '@/utils/productionErrorTracking';
import { 
  ExclamationTriangleIcon, 
  ChartBarIcon, 
  UserGroupIcon,
  ClockIcon,
  XMarkIcon
} from '@heroicons/react/24/outline';

interface ProductionErrorDashboardProps {
  className?: string;
  showMetrics?: boolean;
  showAlerts?: boolean;
  maxAlerts?: number;
}

/**
 * Production error monitoring dashboard
 * Displays real-time error metrics, alerts, and trends
 */
export const ProductionErrorDashboard: React.FC<ProductionErrorDashboardProps> = ({
  className = '',
  showMetrics = true,
  showAlerts = true,
  maxAlerts = 5
}) => {
  const [metrics, setMetrics] = useState<ErrorMetrics>(productionErrorTracker.getMetrics());
  const [alerts, setAlerts] = useState<ErrorAlert[]>(productionErrorTracker.getAlerts());
  const [isExpanded, setIsExpanded] = useState(false);

  useEffect(() => {
    // Subscribe to metrics updates
    const unsubscribeMetrics = productionErrorTracker.onMetricsUpdate(setMetrics);
    
    // Subscribe to new alerts
    const unsubscribeAlerts = productionErrorTracker.onAlert((alert) => {
      setAlerts(prev => [alert, ...prev].slice(0, 20)); // Keep last 20 alerts
    });

    // Load existing alerts
    setAlerts(productionErrorTracker.getAlerts().slice(-20));

    return () => {
      unsubscribeMetrics();
      unsubscribeAlerts();
    };
  }, []);

  const activeAlerts = alerts.filter(a => !a.acknowledged);
  const criticalAlerts = activeAlerts.filter(a => a.severity === 'critical');

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'text-red-600 bg-red-50 border-red-200';
      case 'high': return 'text-orange-600 bg-orange-50 border-orange-200';
      case 'medium': return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'low': return 'text-blue-600 bg-blue-50 border-blue-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'critical': return '🚨';
      case 'high': return '🔴';
      case 'medium': return '🟠';
      case 'low': return '🟡';
      default: return '⚪';
    }
  };

  const acknowledgeAlert = (alertId: string) => {
    productionErrorTracker.acknowledgeAlert(alertId);
    setAlerts(prev => prev.map(alert => 
      alert.id === alertId ? { ...alert, acknowledged: true } : alert
    ));
  };

  const formatErrorRate = (rate: number) => {
    if (rate === 0) return '0';
    if (rate < 1) return rate.toFixed(2);
    return Math.round(rate).toString();
  };

  return (
    <div className={`bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 ${className}`}>
      {/* Header */}
      <div className="p-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <ExclamationTriangleIcon className="w-5 h-5 text-red-600" />
            <h3 className="font-semibold text-gray-900 dark:text-white">Error Monitoring</h3>
            {criticalAlerts.length > 0 && (
              <div className="flex items-center space-x-1">
                <span className="text-red-600 font-bold">🚨</span>
                <span className="text-sm text-red-600 font-medium">{criticalAlerts.length} Critical</span>
              </div>
            )}
            {activeAlerts.length > 0 && (
              <div className="px-2 py-1 bg-red-100 text-red-800 rounded-full text-xs font-medium">
                {activeAlerts.length} Active
              </div>
            )}
          </div>
          <button
            onClick={() => setIsExpanded(!isExpanded)}
            className="text-sm text-blue-600 hover:text-blue-700"
          >
            {isExpanded ? 'Collapse' : 'Expand'}
          </button>
        </div>
      </div>

      {/* Metrics Overview */}
      {showMetrics && (
        <div className="p-4 border-b border-gray-200 dark:border-gray-700">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900 dark:text-white">
                {formatErrorRate(metrics.errorRate)}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Errors/min</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600">
                {metrics.criticalErrors}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Critical</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">
                {metrics.affectedUsers}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Affected Users</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900 dark:text-white">
                {metrics.totalErrors}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Total Errors</div>
            </div>
          </div>
        </div>
      )}

      {/* Active Alerts */}
      {showAlerts && activeAlerts.length > 0 && (
        <div className="p-4">
          <h4 className="font-medium text-gray-900 dark:text-white mb-3 flex items-center">
            <ExclamationTriangleIcon className="w-4 h-4 text-red-500 mr-2" />
            Active Alerts ({activeAlerts.length})
          </h4>
          <div className="space-y-3">
            {activeAlerts.slice(0, maxAlerts).map((alert) => (
              <div key={alert.id} className={`p-3 rounded-lg border ${getSeverityColor(alert.severity)}`}>
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-1">
                      <span className="text-sm">{getSeverityIcon(alert.severity)}</span>
                      <span className="font-medium text-sm">
                        {alert.error.name}
                      </span>
                      <span className="text-xs px-2 py-1 bg-white bg-opacity-50 rounded">
                        {alert.severity.toUpperCase()}
                      </span>
                    </div>
                    <div className="text-sm text-gray-700 dark:text-gray-300 mb-2">
                      {alert.error.message}
                    </div>
                    <div className="flex items-center space-x-4 text-xs text-gray-600 dark:text-gray-400">
                      <div className="flex items-center space-x-1">
                        <ChartBarIcon className="w-3 h-3" />
                        <span>{alert.frequency} occurrences</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <UserGroupIcon className="w-3 h-3" />
                        <span>{alert.affectedUsers} users</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <ClockIcon className="w-3 h-3" />
                        <span>{new Date(alert.timestamp).toLocaleTimeString()}</span>
                      </div>
                      {alert.context.component && (
                        <div className="px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded text-xs">
                          {alert.context.component}
                        </div>
                      )}
                    </div>
                  </div>
                  <button
                    onClick={() => acknowledgeAlert(alert.id)}
                    className="ml-2 p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                    title="Acknowledge alert"
                  >
                    <XMarkIcon className="w-4 h-4" />
                  </button>
                </div>
              </div>
            ))}
            {activeAlerts.length > maxAlerts && (
              <div className="text-center text-sm text-gray-500">
                +{activeAlerts.length - maxAlerts} more alerts
              </div>
            )}
          </div>
        </div>
      )}

      {/* Expanded Details */}
      {isExpanded && (
        <div className="border-t border-gray-200 dark:border-gray-700">
          {/* Top Errors */}
          {metrics.topErrors.length > 0 && (
            <div className="p-4 border-b border-gray-200 dark:border-gray-700">
              <h4 className="font-medium text-gray-900 dark:text-white mb-3">Top Errors</h4>
              <div className="space-y-2">
                {metrics.topErrors.slice(0, 5).map((error, index) => (
                  <div key={index} className="flex items-center justify-between text-sm">
                    <div className="flex-1 truncate pr-2">
                      <span className="text-gray-900 dark:text-white">{error.message}</span>
                    </div>
                    <div className="flex items-center space-x-2 text-gray-600 dark:text-gray-400">
                      <span className="font-medium">{error.count}</span>
                      <span className="text-xs">
                        {new Date(error.lastSeen).toLocaleTimeString()}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Errors by Component */}
          {Object.keys(metrics.errorsByComponent).length > 0 && (
            <div className="p-4">
              <h4 className="font-medium text-gray-900 dark:text-white mb-3">Errors by Component</h4>
              <div className="grid grid-cols-2 gap-2">
                {Object.entries(metrics.errorsByComponent)
                  .sort(([,a], [,b]) => b - a)
                  .slice(0, 6)
                  .map(([component, count]) => (
                    <div key={component} className="flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-700 rounded text-sm">
                      <span className="truncate pr-2">{component}</span>
                      <span className="font-medium text-red-600">{count}</span>
                    </div>
                  ))}
              </div>
            </div>
          )}
        </div>
      )}

      {/* Status Indicator */}
      <div className="flex items-center justify-center p-3 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700">
        <div className="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
          <div className={`w-2 h-2 rounded-full ${
            criticalAlerts.length > 0 ? 'bg-red-500' :
            activeAlerts.length > 0 ? 'bg-yellow-500' : 'bg-green-500'
          }`} />
          <span>
            {criticalAlerts.length > 0 ? 'Critical Issues Detected' :
             activeAlerts.length > 0 ? 'Issues Detected' : 'System Healthy'}
          </span>
        </div>
      </div>
    </div>
  );
};

export default ProductionErrorDashboard;
