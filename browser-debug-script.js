/**
 * 🔍 Execute Button Comprehensive Debug Script
 * 
 * Run this script in the browser console while on https://rag-prompt-library.web.app/
 * to diagnose Execute button issues.
 * 
 * Usage:
 * 1. Open browser developer tools (F12)
 * 2. Go to Console tab
 * 3. Navigate to https://rag-prompt-library.web.app/
 * 4. Copy and paste this entire script
 * 5. Press Enter to run
 */

console.log('🔍 Starting Execute Button Debug Analysis...');

// Debug utility functions
const debug = {
    log: (message, type = 'info') => {
        const styles = {
            info: 'color: #3b82f6; font-weight: bold;',
            success: 'color: #16a34a; font-weight: bold;',
            error: 'color: #dc2626; font-weight: bold;',
            warning: 'color: #ca8a04; font-weight: bold;'
        };
        console.log(`%c${message}`, styles[type]);
    },
    
    section: (title) => {
        console.log(`\n%c🔧 ${title}`, 'color: #1f2937; font-size: 16px; font-weight: bold; background: #f3f4f6; padding: 5px;');
    }
};

// 1. Check Current Page and Authentication
debug.section('1. Authentication & Page Status');

try {
    // Check if we're on the right domain
    const currentUrl = window.location.href;
    debug.log(`Current URL: ${currentUrl}`);
    
    if (!currentUrl.includes('rag-prompt-library.web.app')) {
        debug.log('❌ Not on the correct domain! Navigate to https://rag-prompt-library.web.app/', 'error');
    } else {
        debug.log('✅ On correct domain', 'success');
    }
    
    // Check authentication state
    if (window.firebase && window.firebase.auth) {
        const currentUser = window.firebase.auth().currentUser;
        if (currentUser) {
            debug.log(`✅ User authenticated: ${currentUser.email}`, 'success');
        } else {
            debug.log('❌ User not authenticated', 'error');
        }
    } else {
        debug.log('⚠️ Firebase auth not accessible from window object', 'warning');
    }
    
} catch (error) {
    debug.log(`❌ Error checking authentication: ${error.message}`, 'error');
}

// 2. Check React Router and Navigation
debug.section('2. React Router Analysis');

try {
    // Check if React Router is available
    const reactRouterElements = document.querySelectorAll('[data-testid*="route"], [class*="route"]');
    debug.log(`Found ${reactRouterElements.length} potential router elements`);
    
    // Check current route
    const pathname = window.location.pathname;
    debug.log(`Current pathname: ${pathname}`);
    
    // Test route patterns
    const routePatterns = [
        { pattern: '/prompts', description: 'Prompts list page' },
        { pattern: '/prompts/*/execute', description: 'Execute prompt page' },
        { pattern: '/auth', description: 'Authentication page' },
        { pattern: '/', description: 'Dashboard' }
    ];
    
    routePatterns.forEach(({ pattern, description }) => {
        const matches = pathname.match(pattern.replace('*', '[^/]+'));
        if (matches) {
            debug.log(`✅ Current route matches: ${description}`, 'success');
        }
    });
    
} catch (error) {
    debug.log(`❌ Error analyzing routes: ${error.message}`, 'error');
}

// 3. Find and Analyze Execute Buttons
debug.section('3. Execute Button Analysis');

try {
    // Find all Execute buttons
    const executeButtons = document.querySelectorAll('button:contains("Execute"), [data-testid*="execute"], button[class*="execute"]');
    debug.log(`Found ${executeButtons.length} potential execute buttons`);
    
    // More comprehensive search
    const allButtons = document.querySelectorAll('button');
    const executeButtonsText = Array.from(allButtons).filter(btn => 
        btn.textContent.toLowerCase().includes('execute')
    );
    
    debug.log(`Found ${executeButtonsText.length} buttons with "Execute" text`);
    
    executeButtonsText.forEach((button, index) => {
        debug.log(`Button ${index + 1}: "${button.textContent.trim()}"`);
        debug.log(`  - Disabled: ${button.disabled}`);
        debug.log(`  - Classes: ${button.className}`);
        debug.log(`  - Parent: ${button.parentElement?.tagName}`);
        
        // Check for click handlers
        const hasClickHandler = button.onclick || button.addEventListener;
        debug.log(`  - Has click handler: ${!!hasClickHandler}`);
    });
    
} catch (error) {
    debug.log(`❌ Error analyzing execute buttons: ${error.message}`, 'error');
}

// 4. Test Navigation Function
debug.section('4. Navigation Function Test');

try {
    // Create a test navigation function
    window.debugNavigate = function(path) {
        debug.log(`🧪 Testing navigation to: ${path}`);
        
        const originalPath = window.location.pathname;
        
        // Try different navigation methods
        if (window.history && window.history.pushState) {
            window.history.pushState({}, '', path);
            debug.log(`✅ History API navigation attempted`, 'info');
            
            // Check if URL changed
            setTimeout(() => {
                const newPath = window.location.pathname;
                if (newPath === path) {
                    debug.log(`✅ URL successfully changed to: ${newPath}`, 'success');
                } else {
                    debug.log(`❌ URL did not change. Still: ${newPath}`, 'error');
                }
            }, 100);
        }
    };
    
    debug.log('✅ Test navigation function created: window.debugNavigate(path)', 'success');
    debug.log('💡 Try: debugNavigate("/prompts/test-id/execute")', 'info');
    
} catch (error) {
    debug.log(`❌ Error creating navigation test: ${error.message}`, 'error');
}

// 5. Check for JavaScript Errors
debug.section('5. JavaScript Error Detection');

try {
    // Override console.error to catch errors
    const originalError = console.error;
    const errors = [];
    
    console.error = function(...args) {
        errors.push(args.join(' '));
        originalError.apply(console, args);
    };
    
    // Check for existing errors in the console
    debug.log('✅ Error monitoring enabled', 'success');
    debug.log('💡 Any new JavaScript errors will be tracked', 'info');
    
    // Restore after 30 seconds
    setTimeout(() => {
        console.error = originalError;
        if (errors.length > 0) {
            debug.log(`❌ Detected ${errors.length} errors:`, 'error');
            errors.forEach(error => debug.log(`  - ${error}`, 'error'));
        } else {
            debug.log('✅ No JavaScript errors detected', 'success');
        }
    }, 30000);
    
} catch (error) {
    debug.log(`❌ Error setting up error monitoring: ${error.message}`, 'error');
}

// 6. Firebase Functions Connectivity Test
debug.section('6. Firebase Functions Test');

async function testFirebaseFunctions() {
    try {
        debug.log('🧪 Testing Firebase Functions connectivity...', 'info');
        
        const endpoints = [
            'https://us-central1-rag-prompt-library.cloudfunctions.net/api/health',
            'https://australia-southeast1-rag-prompt-library.cloudfunctions.net/api/health'
        ];
        
        for (const endpoint of endpoints) {
            try {
                const response = await fetch(endpoint, { method: 'GET' });
                if (response.ok) {
                    const data = await response.json();
                    debug.log(`✅ ${endpoint} - Status: ${response.status}`, 'success');
                    debug.log(`   Response: ${JSON.stringify(data)}`, 'info');
                } else {
                    debug.log(`❌ ${endpoint} - Status: ${response.status}`, 'error');
                }
            } catch (error) {
                debug.log(`❌ ${endpoint} - Error: ${error.message}`, 'error');
            }
        }
    } catch (error) {
        debug.log(`❌ Firebase Functions test failed: ${error.message}`, 'error');
    }
}

testFirebaseFunctions();

// 7. Create Manual Test Helper
debug.section('7. Manual Test Helpers');

window.debugExecuteButton = function() {
    debug.log('🧪 Manual Execute Button Test', 'info');
    
    // Find execute buttons
    const buttons = Array.from(document.querySelectorAll('button')).filter(btn => 
        btn.textContent.toLowerCase().includes('execute')
    );
    
    if (buttons.length === 0) {
        debug.log('❌ No Execute buttons found on current page', 'error');
        debug.log('💡 Navigate to the Prompts page first', 'warning');
        return;
    }
    
    buttons.forEach((button, index) => {
        debug.log(`Button ${index + 1}: "${button.textContent.trim()}"`);
        
        // Add click event listener for debugging
        button.addEventListener('click', function(event) {
            debug.log(`🖱️ Execute button clicked!`, 'info');
            debug.log(`  - Button text: ${this.textContent.trim()}`);
            debug.log(`  - Event prevented: ${event.defaultPrevented}`);
            debug.log(`  - Current URL before: ${window.location.href}`);
            
            setTimeout(() => {
                debug.log(`  - Current URL after: ${window.location.href}`);
            }, 100);
        }, { once: false });
    });
    
    debug.log(`✅ Added debug listeners to ${buttons.length} Execute buttons`, 'success');
    debug.log('💡 Now click an Execute button to see debug output', 'info');
};

// 8. Summary and Next Steps
debug.section('8. Debug Summary & Next Steps');

debug.log('🎯 Debug script setup complete!', 'success');
debug.log('', 'info');
debug.log('📋 Manual Testing Steps:', 'info');
debug.log('1. If not authenticated, login first', 'info');
debug.log('2. Navigate to /prompts page', 'info');
debug.log('3. Run: debugExecuteButton()', 'info');
debug.log('4. Click an Execute button', 'info');
debug.log('5. Watch console output for navigation details', 'info');
debug.log('', 'info');
debug.log('🔧 Available Debug Functions:', 'info');
debug.log('- debugNavigate(path) - Test navigation', 'info');
debug.log('- debugExecuteButton() - Monitor Execute button clicks', 'info');
debug.log('', 'info');
debug.log('⚠️ If issues persist, check:', 'warning');
debug.log('- Browser cache (try hard refresh: Ctrl+Shift+R)', 'warning');
debug.log('- Service worker (check Application tab in DevTools)', 'warning');
debug.log('- Network requests (check Network tab)', 'warning');
debug.log('- React DevTools for component state', 'warning');

console.log('\n🔍 Execute Button Debug Analysis Complete!');
console.log('💡 Run debugExecuteButton() after navigating to the Prompts page');
