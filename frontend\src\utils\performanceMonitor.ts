/**
 * Performance Monitoring Utility
 * 
 * Provides real-time performance monitoring, metrics collection, and alerting
 * for production applications. Integrates with Web Vitals and custom metrics.
 * 
 * @module PerformanceMonitor
 * @version 1.0.0
 */

import { getCLS, getFID, getFCP, getLCP, getTTFB } from 'web-vitals';

export interface PerformanceMetric {
  name: string;
  value: number;
  rating: 'good' | 'needs-improvement' | 'poor';
  timestamp: number;
  url: string;
  userAgent: string;
  connectionType?: string;
}

export interface PerformanceThresholds {
  fcp: { good: number; poor: number };
  lcp: { good: number; poor: number };
  fid: { good: number; poor: number };
  cls: { good: number; poor: number };
  ttfb: { good: number; poor: number };
}

export interface PerformanceReport {
  metrics: PerformanceMetric[];
  summary: {
    totalMetrics: number;
    goodMetrics: number;
    needsImprovementMetrics: number;
    poorMetrics: number;
    overallRating: 'good' | 'needs-improvement' | 'poor';
  };
  recommendations: string[];
}

export class PerformanceMonitor {
  private metrics: PerformanceMetric[] = [];
  private thresholds: PerformanceThresholds;
  private reportingEndpoint?: string;
  private alertCallback?: (metric: PerformanceMetric) => void;
  private isMonitoring = false;

  constructor(options: {
    thresholds?: Partial<PerformanceThresholds>;
    reportingEndpoint?: string;
    alertCallback?: (metric: PerformanceMetric) => void;
  } = {}) {
    this.thresholds = {
      fcp: { good: 1800, poor: 3000 },
      lcp: { good: 2500, poor: 4000 },
      fid: { good: 100, poor: 300 },
      cls: { good: 0.1, poor: 0.25 },
      ttfb: { good: 800, poor: 1800 },
      ...options.thresholds,
    };

    this.reportingEndpoint = options.reportingEndpoint;
    this.alertCallback = options.alertCallback;
  }

  /**
   * Start monitoring Web Vitals and custom performance metrics
   */
  public startMonitoring(): void {
    if (this.isMonitoring) {
      console.warn('Performance monitoring is already active');
      return;
    }

    this.isMonitoring = true;

    // Monitor Core Web Vitals
    this.monitorWebVitals();

    // Monitor custom metrics
    this.monitorCustomMetrics();

    // Monitor resource loading
    this.monitorResourceLoading();

    // Monitor navigation timing
    this.monitorNavigationTiming();

    console.log('Performance monitoring started');
  }

  /**
   * Stop monitoring and generate final report
   */
  public stopMonitoring(): PerformanceReport {
    this.isMonitoring = false;
    console.log('Performance monitoring stopped');
    return this.generateReport();
  }

  /**
   * Get current performance metrics
   */
  public getMetrics(): PerformanceMetric[] {
    return [...this.metrics];
  }

  /**
   * Clear all collected metrics
   */
  public clearMetrics(): void {
    this.metrics = [];
  }

  /**
   * Monitor Core Web Vitals
   */
  private monitorWebVitals(): void {
    // First Contentful Paint
    getFCP((metric) => {
      this.recordMetric('FCP', metric.value, this.thresholds.fcp);
    });

    // Largest Contentful Paint
    getLCP((metric) => {
      this.recordMetric('LCP', metric.value, this.thresholds.lcp);
    });

    // First Input Delay
    getFID((metric) => {
      this.recordMetric('FID', metric.value, this.thresholds.fid);
    });

    // Cumulative Layout Shift
    getCLS((metric) => {
      this.recordMetric('CLS', metric.value, this.thresholds.cls);
    });

    // Time to First Byte
    getTTFB((metric) => {
      this.recordMetric('TTFB', metric.value, this.thresholds.ttfb);
    });
  }

  /**
   * Monitor custom performance metrics
   */
  private monitorCustomMetrics(): void {
    // Monitor React component render times
    this.monitorReactPerformance();

    // Monitor memory usage
    this.monitorMemoryUsage();

    // Monitor frame rate
    this.monitorFrameRate();

    // Monitor bundle loading times
    this.monitorBundleLoading();
  }

  /**
   * Monitor React component performance
   */
  private monitorReactPerformance(): void {
    // Use React DevTools Profiler API if available
    if (typeof window !== 'undefined' && (window as any).__REACT_DEVTOOLS_GLOBAL_HOOK__) {
      const hook = (window as any).__REACT_DEVTOOLS_GLOBAL_HOOK__;
      
      hook.onCommitFiberRoot = (id: number, root: any, _priorityLevel: number) => {
        const renderTime = performance.now() - (root.current.actualStartTime || 0);
        
        if (renderTime > 16) { // Longer than one frame (60fps)
          this.recordMetric('React Render Time', renderTime, { good: 16, poor: 50 });
        }
      };
    }
  }

  /**
   * Monitor memory usage
   */
  private monitorMemoryUsage(): void {
    if ('memory' in performance) {
      const checkMemory = () => {
        const memory = (performance as any).memory;
        const usedMB = memory.usedJSHeapSize / 1024 / 1024;
        
        this.recordMetric('Memory Usage (MB)', usedMB, { good: 30, poor: 60 });
        
        if (this.isMonitoring) {
          setTimeout(checkMemory, 10000); // Check every 10 seconds
        }
      };
      
      checkMemory();
    }
  }

  /**
   * Monitor frame rate
   */
  private monitorFrameRate(): void {
    let lastTime = performance.now();
    let frameCount = 0;
    
    const measureFPS = () => {
      frameCount++;
      const currentTime = performance.now();
      
      if (currentTime - lastTime >= 1000) { // Every second
        const fps = frameCount;
        frameCount = 0;
        lastTime = currentTime;
        
        this.recordMetric('FPS', fps, { good: 55, poor: 30 });
      }
      
      if (this.isMonitoring) {
        requestAnimationFrame(measureFPS);
      }
    };
    
    requestAnimationFrame(measureFPS);
  }

  /**
   * Monitor bundle loading times
   */
  private monitorBundleLoading(): void {
    const observer = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        if (entry.entryType === 'resource' && entry.name.includes('.js')) {
          const loadTime = entry.responseEnd - entry.startTime;
          this.recordMetric('Bundle Load Time', loadTime, { good: 500, poor: 2000 });
        }
      }
    });
    
    observer.observe({ entryTypes: ['resource'] });
  }

  /**
   * Monitor resource loading performance
   */
  private monitorResourceLoading(): void {
    const observer = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        if (entry.entryType === 'resource') {
          const resource = entry as PerformanceResourceTiming;
          const loadTime = resource.responseEnd - resource.startTime;
          
          // Monitor different resource types
          if (resource.name.includes('.css')) {
            this.recordMetric('CSS Load Time', loadTime, { good: 200, poor: 1000 });
          } else if (resource.name.includes('.png') || resource.name.includes('.jpg') || resource.name.includes('.svg')) {
            this.recordMetric('Image Load Time', loadTime, { good: 300, poor: 1500 });
          }
        }
      }
    });
    
    observer.observe({ entryTypes: ['resource'] });
  }

  /**
   * Monitor navigation timing
   */
  private monitorNavigationTiming(): void {
    window.addEventListener('load', () => {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      
      if (navigation) {
        // DNS lookup time
        const dnsTime = navigation.domainLookupEnd - navigation.domainLookupStart;
        this.recordMetric('DNS Lookup Time', dnsTime, { good: 50, poor: 200 });
        
        // Connection time
        const connectionTime = navigation.connectEnd - navigation.connectStart;
        this.recordMetric('Connection Time', connectionTime, { good: 100, poor: 500 });
        
        // DOM content loaded time
        const domContentLoadedTime = navigation.domContentLoadedEventEnd - navigation.navigationStart;
        this.recordMetric('DOM Content Loaded', domContentLoadedTime, { good: 1500, poor: 3000 });
        
        // Page load time
        const pageLoadTime = navigation.loadEventEnd - navigation.navigationStart;
        this.recordMetric('Page Load Time', pageLoadTime, { good: 2000, poor: 4000 });
      }
    });
  }

  /**
   * Record a performance metric
   */
  private recordMetric(name: string, value: number, thresholds: { good: number; poor: number }): void {
    const rating = this.getRating(value, thresholds);
    
    const metric: PerformanceMetric = {
      name,
      value,
      rating,
      timestamp: Date.now(),
      url: window.location.href,
      userAgent: navigator.userAgent,
      connectionType: this.getConnectionType(),
    };

    this.metrics.push(metric);

    // Alert on poor performance
    if (rating === 'poor' && this.alertCallback) {
      this.alertCallback(metric);
    }

    // Report to endpoint if configured
    if (this.reportingEndpoint) {
      this.reportMetric(metric);
    }

    console.log(`Performance Metric: ${name} = ${value.toFixed(2)} (${rating})`);
  }

  /**
   * Get performance rating based on thresholds
   */
  private getRating(value: number, thresholds: { good: number; poor: number }): 'good' | 'needs-improvement' | 'poor' {
    if (value <= thresholds.good) return 'good';
    if (value <= thresholds.poor) return 'needs-improvement';
    return 'poor';
  }

  /**
   * Get connection type information
   */
  private getConnectionType(): string | undefined {
    const connection = (navigator as any).connection || (navigator as any).mozConnection || (navigator as any).webkitConnection;
    return connection?.effectiveType;
  }

  /**
   * Report metric to external endpoint
   */
  private async reportMetric(metric: PerformanceMetric): Promise<void> {
    try {
      await fetch(this.reportingEndpoint!, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(metric),
      });
    } catch (error) {
      console.error('Failed to report performance metric:', error);
    }
  }

  /**
   * Generate performance report
   */
  private generateReport(): PerformanceReport {
    const goodMetrics = this.metrics.filter(m => m.rating === 'good').length;
    const needsImprovementMetrics = this.metrics.filter(m => m.rating === 'needs-improvement').length;
    const poorMetrics = this.metrics.filter(m => m.rating === 'poor').length;
    
    const overallRating = this.getOverallRating(goodMetrics, needsImprovementMetrics, poorMetrics);
    const recommendations = this.generateRecommendations();

    return {
      metrics: [...this.metrics],
      summary: {
        totalMetrics: this.metrics.length,
        goodMetrics,
        needsImprovementMetrics,
        poorMetrics,
        overallRating,
      },
      recommendations,
    };
  }

  /**
   * Get overall performance rating
   */
  private getOverallRating(good: number, needsImprovement: number, poor: number): 'good' | 'needs-improvement' | 'poor' {
    const total = good + needsImprovement + poor;
    if (total === 0) return 'good';
    
    const goodPercentage = good / total;
    const poorPercentage = poor / total;
    
    if (goodPercentage >= 0.75) return 'good';
    if (poorPercentage >= 0.25) return 'poor';
    return 'needs-improvement';
  }

  /**
   * Generate performance recommendations
   */
  private generateRecommendations(): string[] {
    const recommendations: string[] = [];
    
    const poorMetrics = this.metrics.filter(m => m.rating === 'poor');
    
    for (const metric of poorMetrics) {
      switch (metric.name) {
        case 'FCP':
          recommendations.push('Optimize critical rendering path and reduce render-blocking resources');
          break;
        case 'LCP':
          recommendations.push('Optimize largest contentful element loading and reduce server response times');
          break;
        case 'FID':
          recommendations.push('Reduce JavaScript execution time and optimize event handlers');
          break;
        case 'CLS':
          recommendations.push('Ensure proper sizing for images and ads, avoid inserting content above existing content');
          break;
        case 'TTFB':
          recommendations.push('Optimize server response times and consider using a CDN');
          break;
        case 'Memory Usage (MB)':
          recommendations.push('Optimize memory usage by reducing object creation and implementing proper cleanup');
          break;
        case 'Bundle Load Time':
          recommendations.push('Implement code splitting and optimize bundle sizes');
          break;
      }
    }
    
    return [...new Set(recommendations)]; // Remove duplicates
  }
}

// Global performance monitor instance
export const performanceMonitor = new PerformanceMonitor({
  reportingEndpoint: process.env.VITE_PERFORMANCE_ENDPOINT,
  alertCallback: (metric) => {
    console.warn(`Performance Alert: ${metric.name} is performing poorly (${metric.value.toFixed(2)})`);
  },
});

// Auto-start monitoring in production
if (process.env.NODE_ENV === 'production' && typeof window !== 'undefined') {
  performanceMonitor.startMonitoring();
}
