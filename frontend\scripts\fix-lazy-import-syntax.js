#!/usr/bin/env node

/**
 * Fix Lazy Import Syntax Errors
 * Fixes incorrectly placed .catch() handlers in lazy import statements
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class LazyImportSyntaxFixer {
  constructor() {
    this.projectRoot = path.resolve(__dirname, '..');
  }

  /**
   * Fix App.tsx lazy import syntax errors
   */
  async fixAppTsx() {
    const filePath = path.join(this.projectRoot, 'src/App.tsx');
    let content = fs.readFileSync(filePath, 'utf8');
    
    console.log('🔧 Fixing lazy import syntax in App.tsx...');
    
    // Pattern: .then(m => ({ default: m.Something }).catch(error => ...))
    // Should be: .then(m => ({ default: m.Something })).catch(error => ...)
    
    const incorrectPattern = /\.then\(m => \(\{ default: m\.(\w+) \}\)\.catch\(error => console\.error\('Promise rejection:', error\)\)\)/g;
    
    content = content.replace(incorrectPattern, (match, componentName) => {
      return `.then(m => ({ default: m.${componentName} })).catch(error => { console.error('Promise rejection:', error); return { default: () => null }; })`;
    });
    
    fs.writeFileSync(filePath, content);
    console.log('✅ Fixed App.tsx lazy import syntax');
  }

  /**
   * Fix lib/queryClient.ts syntax errors
   */
  async fixQueryClient() {
    const filePath = path.join(this.projectRoot, 'src/lib/queryClient.ts');
    let content = fs.readFileSync(filePath, 'utf8');
    
    console.log('🔧 Fixing promise syntax in queryClient.ts...');
    
    // Fix incorrectly placed .catch() handlers
    const incorrectPattern = /\.then\(m => m\.(\w+)\.(\w+)\(([^)]+)\)\)\.catch\(error => console\.error\('Promise rejection:', error\)\)/g;
    
    content = content.replace(incorrectPattern, (match, service, method, params) => {
      return `.then(m => m.${service}.${method}(${params})).catch(error => console.error('Promise rejection:', error))`;
    });
    
    fs.writeFileSync(filePath, content);
    console.log('✅ Fixed queryClient.ts promise syntax');
  }

  /**
   * Fix other files with similar issues
   */
  async fixOtherFiles() {
    const files = [
      'src/utils/requestBatcher.ts',
      'src/utils/performanceBudgets.ts'
    ];

    for (const file of files) {
      const filePath = path.join(this.projectRoot, file);
      
      if (fs.existsSync(filePath)) {
        let content = fs.readFileSync(filePath, 'utf8');
        
        console.log(`🔧 Fixing promise syntax in ${file}...`);
        
        // Fix general promise chain syntax issues
        const incorrectPattern = /\.then\(([^)]+)\)\.catch\(error => console\.error\('Promise rejection:', error\)\)/g;
        
        // This pattern should already be correct, but let's verify
        content = content.replace(incorrectPattern, (match, thenCallback) => {
          // If the .catch is already correctly placed, keep it
          return match;
        });
        
        fs.writeFileSync(filePath, content);
        console.log(`✅ Verified ${file} promise syntax`);
      }
    }
  }

  /**
   * Run all fixes
   */
  async run() {
    console.log('🚀 Starting lazy import syntax fixes...\n');
    
    await this.fixAppTsx();
    await this.fixQueryClient();
    await this.fixOtherFiles();
    
    console.log('\n✅ All lazy import syntax fixes completed');
  }
}

// Run the fixer
const fixer = new LazyImportSyntaxFixer();
fixer.run().catch(console.error);
