import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { promptService } from '@/services/firestore';
import { queryKeys, invalidateQueries } from '@/lib/queryClient';
import { useAuth } from '@/contexts/AuthContext';
import type { Prompt } from '@/types';
import React from 'react';

/**
 * Hook for fetching user prompts with caching
 */
export function usePrompts(options?: {
  enabled?: boolean;
  refetchInterval?: number;
}) {
  const { currentUser } = useAuth();
  const { enabled = true, refetchInterval } = options || {};

  return useQuery({
    queryKey: queryKeys.prompts.list(currentUser?.uid || ''),
    queryFn: () => {
      if (!currentUser) throw new Error('User not authenticated');
      return promptService.getUserPrompts(currentUser.uid);
    },
    enabled: enabled && !!currentUser,
    staleTime: 3 * 60 * 1000, // 3 minutes for prompts
    refetchInterval,
    refetchOnWindowFocus: false,
  });
}

/**
 * Hook for fetching a single prompt
 */
export function usePrompt(promptId: string, options?: { enabled?: boolean }) {
  const { enabled = true } = options || {};

  return useQuery({
    queryKey: queryKeys.prompts.detail(promptId),
    queryFn: () => promptService.getPrompt(promptId),
    enabled: enabled && !!promptId,
    staleTime: 5 * 60 * 1000, // 5 minutes for individual prompts
  });
}

/**
 * Hook for fetching public prompts
 */
export function usePublicPrompts(options?: {
  enabled?: boolean;
  category?: string;
  tags?: string[];
}) {
  const { enabled = true, category, tags } = options || {};

  return useQuery({
    queryKey: ['prompts', 'public', category, tags],
    queryFn: () => promptService.getPublicPrompts({ category, tags }),
    enabled,
    staleTime: 10 * 60 * 1000, // 10 minutes for public prompts (more stable)
  });
}

/**
 * Hook for prompt search with debouncing
 */
export function usePromptSearch(
  query: string,
  filters?: { category?: string; tags?: string[] },
  options?: { enabled?: boolean; debounceMs?: number }
) {
  const { currentUser } = useAuth();
  const { enabled = true, debounceMs = 300 } = options || {};

  // Debounced query
  const [debouncedQuery, setDebouncedQuery] = React.useState(query);

  React.useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedQuery(query);
    }, debounceMs);

    return () => clearTimeout(timer);
  }, [query, debounceMs]);

  return useQuery({
    queryKey: queryKeys.prompts.list(currentUser?.uid || '', { search: debouncedQuery, ...filters }),
    queryFn: async () => {
      if (!currentUser) throw new Error('User not authenticated');
      if (!debouncedQuery.trim()) return [];
      
      return promptService.searchPrompts(currentUser.uid, debouncedQuery, filters);
    },
    enabled: enabled && !!currentUser && debouncedQuery.length > 0,
    staleTime: 1 * 60 * 1000, // 1 minute for search results
  });
}

/**
 * Hook for fetching prompt categories
 */
export function usePromptCategories() {
  const { currentUser } = useAuth();

  return useQuery({
    queryKey: queryKeys.prompts.categories(),
    queryFn: () => promptService.getCategories(),
    enabled: !!currentUser,
    staleTime: 15 * 60 * 1000, // 15 minutes for categories (very stable)
  });
}

/**
 * Hook for fetching prompt tags
 */
export function usePromptTags() {
  const { currentUser } = useAuth();

  return useQuery({
    queryKey: queryKeys.prompts.tags(),
    queryFn: () => promptService.getTags(),
    enabled: !!currentUser,
    staleTime: 10 * 60 * 1000, // 10 minutes for tags
  });
}

/**
 * Hook for creating prompts
 */
export function useCreatePrompt() {
  const queryClient = useQueryClient();
  const { currentUser } = useAuth();

  return useMutation({
    mutationFn: async (promptData: Omit<Prompt, 'id' | 'createdAt' | 'updatedAt' | 'userId'>) => {
      if (!currentUser) throw new Error('User not authenticated');
      return promptService.createPrompt(currentUser.uid, promptData);
    },
    onSuccess: () => {
      // Invalidate prompts list and categories/tags
      if (currentUser) {
        invalidateQueries.prompts.list(currentUser.uid);
        queryClient.invalidateQueries({ queryKey: queryKeys.prompts.categories() });
        queryClient.invalidateQueries({ queryKey: queryKeys.prompts.tags() });
      }
    },
    onError: (error) => {
      console.error('Prompt creation failed:', error);
    },
  });
}

/**
 * Hook for updating prompts
 */
export function useUpdatePrompt() {
  const queryClient = useQueryClient();
  const { currentUser } = useAuth();

  return useMutation({
    mutationFn: async ({ promptId, updates }: { promptId: string; updates: Partial<Prompt> }) => {
      if (!currentUser) throw new Error('User not authenticated');
      return promptService.updatePrompt(currentUser.uid, promptId, updates);
    },
    onSuccess: (updatedPrompt, { promptId }) => {
      // Update the prompt in cache
      queryClient.setQueryData(
        queryKeys.prompts.detail(promptId),
        updatedPrompt
      );
      
      // Update the prompt in the list cache
      if (currentUser) {
        queryClient.setQueryData(
          queryKeys.prompts.list(currentUser.uid),
          (oldData: Prompt[] | undefined) => {
            if (!oldData) return oldData;
            return oldData.map(prompt => 
              prompt.id === promptId ? { ...prompt, ...updatedPrompt } : prompt
            );
          }
        );
      }

      // Invalidate categories/tags if they might have changed
      if (updates.category || updates.tags) {
        queryClient.invalidateQueries({ queryKey: queryKeys.prompts.categories() });
        queryClient.invalidateQueries({ queryKey: queryKeys.prompts.tags() });
      }
    },
    onError: (error) => {
      console.error('Prompt update failed:', error);
    },
  });
}

/**
 * Hook for deleting prompts
 */
export function useDeletePrompt() {
  const queryClient = useQueryClient();
  const { currentUser } = useAuth();

  return useMutation({
    mutationFn: async (promptId: string) => {
      if (!currentUser) throw new Error('User not authenticated');
      return promptService.deletePrompt(currentUser.uid, promptId);
    },
    onSuccess: (_, promptId) => {
      // Remove from cache and invalidate list
      queryClient.removeQueries({ queryKey: queryKeys.prompts.detail(promptId) });
      if (currentUser) {
        invalidateQueries.prompts.list(currentUser.uid);
      }
    },
    onError: (error) => {
      console.error('Prompt deletion failed:', error);
    },
  });
}

/**
 * Hook for duplicating prompts
 */
export function useDuplicatePrompt() {
// const queryClient = useQueryClient(); // Unused assignment
  const { currentUser } = useAuth();

  return useMutation({
    mutationFn: async (promptId: string) => {
      if (!currentUser) throw new Error('User not authenticated');
      
      // Get the original prompt
      const originalPrompt = await promptService.getPrompt(promptId);
      if (!originalPrompt) throw new Error('Prompt not found');
      
      // Create a copy
      const duplicatedPrompt = {
        ...originalPrompt,
        title: `${originalPrompt.title} (Copy)`,
        isPublic: false, // Copies are private by default
      };
      
      // Remove fields that shouldn't be copied
      delete (duplicatedPrompt as any).id;
      delete (duplicatedPrompt as any).createdAt;
      delete (duplicatedPrompt as any).updatedAt;
      delete (duplicatedPrompt as any).userId;
      
      return promptService.createPrompt(currentUser.uid, duplicatedPrompt);
    },
    onSuccess: () => {
      // Invalidate prompts list
      if (currentUser) {
        invalidateQueries.prompts.list(currentUser.uid);
      }
    },
    onError: (error) => {
      console.error('Prompt duplication failed:', error);
    },
  });
}

/**
 * Hook for optimistic updates when editing prompts
 */
export function useOptimisticPromptUpdate() {
  const queryClient = useQueryClient();
  const { currentUser } = useAuth();

  const updatePromptOptimistically = React.useCallback((
    promptId: string,
    updates: Partial<Prompt>
  ) => {
    if (!currentUser) return;

    // Optimistically update the prompt
    queryClient.setQueryData(
      queryKeys.prompts.detail(promptId),
      (oldData: Prompt | undefined) => {
        if (!oldData) return oldData;
        return { ...oldData, ...updates };
      }
    );

    // Also update in the list
    queryClient.setQueryData(
      queryKeys.prompts.list(currentUser.uid),
      (oldData: Prompt[] | undefined) => {
        if (!oldData) return oldData;
        return oldData.map(prompt => 
          prompt.id === promptId ? { ...prompt, ...updates } : prompt
        );
      }
    );
  }, [queryClient, currentUser]);

  return { updatePromptOptimistically };
}
