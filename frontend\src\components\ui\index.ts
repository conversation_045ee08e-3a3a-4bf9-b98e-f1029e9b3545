/**
 * UI Component Library Index
 * Centralized exports for all reusable UI components
 */

// Layout Components
export { Modal, ModalHeader, ModalBody, ModalFooter, ConfirmationModal, useModal } from './Modal';
export { Card, CardHeader, CardBody, CardFooter, StatsCard, FeatureCard, LoadingCard } from './Card';

// Form Components
export { Input, SearchInput, PasswordInput, Textarea } from './Input';
export { Select } from './Select';
export { Checkbox, Radio, RadioGroup, CheckboxGroup } from './Checkbox';

// Data Display Components
export { Table } from './Table';

// Common Components (re-export from common directory for convenience)
export { LoadingSpinner } from '@/components/common/LoadingSpinner';
export { Toast, ToastProvider, useToast } from '@/components/common/Toast';
export { ErrorBoundary } from '@/components/common/ErrorBoundary';

// Types (re-export for convenience)
export type {
  BaseComponentProps,
  Size,
  Variant,
  LoadingProps,
  DisabledProps,
  ModalProps,
  CardProps,
  ButtonProps,
  InputProps,
  TextareaProps,
  SelectProps,
  SelectOption,
  CheckboxProps,
  RadioProps,
  TableProps,
  TableColumn,
  ToastProps,
  PaginationProps,
  SearchProps
} from '@/components/common/types';
