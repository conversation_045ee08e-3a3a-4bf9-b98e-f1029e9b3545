/**
 * Standardized Component Prop Interfaces
 * Provides consistent prop patterns across all components
 */

import React from 'react';

// Firebase and external library types
export interface FirebaseTimestamp {
  seconds: number;
  nanoseconds: number;
  toDate(): Date;
}

export type TimestampType = FirebaseTimestamp | Date | string | number;

// Utility types for better type safety
export type NonEmptyArray<T> = [T, ...T[]];
export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;
export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;

// Event types for better event handler typing
export type MouseEventHandler = (event: React.MouseEvent<HTMLElement>) => void;
export type ChangeEventHandler = (event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => void;
export type FormEventHandler = (event: React.FormEvent<HTMLFormElement>) => void;
export type KeyboardEventHandler = (event: React.KeyboardEvent<HTMLElement>) => void;

// Base component props that all components should extend
export interface BaseComponentProps {
  className?: string;
  'data-testid'?: string;
  id?: string;
}

// Common size variants used across components
export type Size = 'xs' | 'sm' | 'md' | 'lg' | 'xl';

// Common color variants
export type Variant = 'primary' | 'secondary' | 'success' | 'warning' | 'danger' | 'info';

// Loading state props
export interface LoadingProps {
  loading?: boolean;
  loadingText?: string;
}

// Disabled state props
export interface DisabledProps {
  disabled?: boolean;
  disabledReason?: string;
}

// Modal component props
export interface ModalProps extends BaseComponentProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full';
  closeOnOverlayClick?: boolean;
  closeOnEscape?: boolean;
  showCloseButton?: boolean;
  children: React.ReactNode;
}

// Card component props
export interface CardProps extends BaseComponentProps {
  variant?: 'default' | 'elevated' | 'outlined' | 'ghost';
  padding?: Size;
  hoverable?: boolean;
  clickable?: boolean;
  onClick?: () => void;
  header?: React.ReactNode;
  footer?: React.ReactNode;
  actions?: React.ReactNode;
  children: React.ReactNode;
}

// Button component props (extending HTML button attributes)
export interface ButtonProps extends BaseComponentProps, LoadingProps, DisabledProps {
  variant?: Variant | 'outline' | 'ghost';
  size?: Size;
  fullWidth?: boolean;
  icon?: React.ReactNode;
  iconPosition?: 'left' | 'right';
  children: React.ReactNode;
  onClick?: (event: React.MouseEvent<HTMLButtonElement>) => void;
  type?: 'button' | 'submit' | 'reset';
}

// Form field props
export interface FormFieldProps extends BaseComponentProps {
  name: string;
  label?: string;
  placeholder?: string;
  required?: boolean;
  disabled?: boolean;
  error?: string;
  helperText?: string;
  value?: string;
  onChange?: (value: string) => void;
  onBlur?: () => void;
  onFocus?: () => void;
}

// Input component props
export interface InputProps extends FormFieldProps {
  type?: 'text' | 'email' | 'password' | 'number' | 'tel' | 'url' | 'search';
  size?: Size;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  autoComplete?: string;
  autoFocus?: boolean;
  maxLength?: number;
  minLength?: number;
  pattern?: string;
}

// Textarea component props
export interface TextareaProps extends FormFieldProps {
  rows?: number;
  cols?: number;
  resize?: 'none' | 'vertical' | 'horizontal' | 'both';
  autoResize?: boolean;
}

// Select component props
export interface SelectOption {
  value: string | number;
  label: string;
  disabled?: boolean;
  group?: string;
}

export interface SelectProps extends FormFieldProps {
  options: SelectOption[];
  multiple?: boolean;
  searchable?: boolean;
  clearable?: boolean;
  placeholder?: string;
  noOptionsMessage?: string;
  loadingMessage?: string;
  onSearch?: (searchTerm: string) => void;
}

// Checkbox component props
export interface CheckboxProps extends BaseComponentProps {
  name?: string;
  label?: string;
  checked?: boolean;
  indeterminate?: boolean;
  disabled?: boolean;
  required?: boolean;
  error?: string;
  onChange?: (checked: boolean) => void;
  value?: string;
}

// Radio component props
export interface RadioProps extends BaseComponentProps {
  name: string;
  label?: string;
  value: string;
  checked?: boolean;
  disabled?: boolean;
  required?: boolean;
  error?: string;
  onChange?: (value: string) => void;
}

// Table component props
export interface TableColumn<T = Record<string, unknown>> {
  key: string;
  title: string;
  dataIndex?: keyof T;
  render?: (value: T[keyof T], record: T, index: number) => React.ReactNode;
  sortable?: boolean;
  filterable?: boolean;
  width?: string | number;
  align?: 'left' | 'center' | 'right';
  fixed?: 'left' | 'right';
}

export interface TableProps<T = Record<string, unknown>> extends BaseComponentProps {
  columns: TableColumn<T>[];
  data: T[];
  loading?: boolean;
  pagination?: {
    current: number;
    pageSize: number;
    total: number;
    onChange: (page: number, pageSize: number) => void;
  };
  selection?: {
    selectedRowKeys: string[];
    onChange: (selectedRowKeys: string[], selectedRows: T[]) => void;
  };
  onRow?: (record: T, index: number) => React.HTMLAttributes<HTMLTableRowElement>;
  emptyText?: string;
  scroll?: { x?: number; y?: number };
}

// List component props
export interface ListItemProps extends BaseComponentProps {
  title: string;
  description?: string;
  avatar?: React.ReactNode;
  actions?: React.ReactNode[];
  extra?: React.ReactNode;
  onClick?: () => void;
  selected?: boolean;
  disabled?: boolean;
}

export interface ListProps extends BaseComponentProps {
  items: ListItemProps[];
  loading?: boolean;
  emptyText?: string;
  header?: React.ReactNode;
  footer?: React.ReactNode;
  bordered?: boolean;
  split?: boolean;
  size?: Size;
}

// Toast/Notification props
export interface ToastProps extends BaseComponentProps {
  type?: 'success' | 'error' | 'warning' | 'info';
  title?: string;
  message: string;
  duration?: number;
  closable?: boolean;
  onClose?: () => void;
  action?: {
    label: string;
    onClick: () => void;
  };
}

// Pagination props
export interface PaginationProps extends BaseComponentProps {
  current: number;
  total: number;
  pageSize: number;
  showSizeChanger?: boolean;
  showQuickJumper?: boolean;
  showTotal?: (total: number, range: [number, number]) => string;
  onChange: (page: number, pageSize: number) => void;
  disabled?: boolean;
}

// Search props
export interface SearchProps extends BaseComponentProps {
  value?: string;
  placeholder?: string;
  loading?: boolean;
  disabled?: boolean;
  allowClear?: boolean;
  onSearch: (value: string) => void;
  onChange?: (value: string) => void;
  onClear?: () => void;
  size?: Size;
  enterButton?: boolean | string | React.ReactNode;
}

// Dropdown props
export interface DropdownItem {
  key: string;
  label: string;
  icon?: React.ReactNode;
  disabled?: boolean;
  danger?: boolean;
  onClick?: () => void;
}

export interface DropdownProps extends BaseComponentProps {
  items: DropdownItem[];
  trigger?: 'click' | 'hover' | 'contextMenu';
  placement?: 'bottom' | 'bottomLeft' | 'bottomRight' | 'top' | 'topLeft' | 'topRight';
  disabled?: boolean;
  children: React.ReactNode;
}

// Progress props
export interface ProgressProps extends BaseComponentProps {
  percent: number;
  status?: 'normal' | 'success' | 'exception' | 'active';
  showInfo?: boolean;
  format?: (percent: number) => string;
  strokeColor?: string;
  trailColor?: string;
  strokeWidth?: number;
  size?: Size;
  type?: 'line' | 'circle' | 'dashboard';
}

// Avatar props
export interface AvatarProps extends BaseComponentProps {
  src?: string;
  alt?: string;
  size?: Size | number;
  shape?: 'circle' | 'square';
  icon?: React.ReactNode;
  children?: React.ReactNode;
  onClick?: () => void;
}

// Badge props
export interface BadgeProps extends BaseComponentProps {
  count?: number;
  showZero?: boolean;
  overflowCount?: number;
  dot?: boolean;
  status?: 'success' | 'processing' | 'default' | 'error' | 'warning';
  color?: string;
  text?: string;
  offset?: [number, number];
  children?: React.ReactNode;
}

// Tag props
export interface TagProps extends BaseComponentProps {
  color?: string;
  closable?: boolean;
  onClose?: () => void;
  icon?: React.ReactNode;
  children: React.ReactNode;
}

// Skeleton props
export interface SkeletonProps extends BaseComponentProps {
  loading?: boolean;
  active?: boolean;
  avatar?: boolean | { size?: Size; shape?: 'circle' | 'square' };
  paragraph?: boolean | { rows?: number; width?: string | string[] };
  title?: boolean | { width?: string };
  children?: React.ReactNode;
}

// Error boundary props
export interface ErrorBoundaryProps extends BaseComponentProps {
  fallback?: React.ComponentType<{ error: Error; resetError: () => void }>;
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void;
  children: React.ReactNode;
}

// Conditional rendering props
export interface ConditionalProps {
  condition: boolean;
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

// Common callback types
export type EventHandler<T = Event> = (event: T) => void;
export type ChangeHandler<T = string | number | boolean> = (value: T) => void;
export type AsyncHandler<T = unknown> = (value: T) => Promise<void>;

// Common data types
export interface ApiResponse<T = unknown> {
  data: T;
  success: boolean;
  message?: string;
  error?: string;
}

export interface PaginatedResponse<T = unknown> extends ApiResponse<T[]> {
  pagination: {
    current: number;
    pageSize: number;
    total: number;
    totalPages: number;
  };
}

// Form validation types
export interface ValidationRule {
  required?: boolean;
  min?: number;
  max?: number;
  pattern?: RegExp;
  validator?: (value: string | number | boolean) => string | null;
  message?: string;
}

export interface FormValidation {
  [fieldName: string]: ValidationRule[];
}

// Enhanced component prop interfaces with better validation
export interface EnhancedButtonProps extends BaseComponentProps, LoadingProps, DisabledProps {
  variant?: Variant | 'outline' | 'ghost';
  size?: Size;
  fullWidth?: boolean;
  icon?: React.ReactNode;
  iconPosition?: 'left' | 'right';
  children: React.ReactNode;
  onClick?: (event: React.MouseEvent<HTMLButtonElement>) => void;
  onFocus?: (event: React.FocusEvent<HTMLButtonElement>) => void;
  onBlur?: (event: React.FocusEvent<HTMLButtonElement>) => void;
  type?: 'button' | 'submit' | 'reset';
  ariaLabel?: string;
  ariaDescribedBy?: string;
  tabIndex?: number;
}

export interface EnhancedModalProps extends BaseComponentProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full';
  closeOnOverlayClick?: boolean;
  closeOnEscape?: boolean;
  showCloseButton?: boolean;
  children: React.ReactNode;
  initialFocus?: React.RefObject<HTMLElement>;
  finalFocus?: React.RefObject<HTMLElement>;
  ariaLabelledBy?: string;
  ariaDescribedBy?: string;
  role?: 'dialog' | 'alertdialog';
}

export interface EnhancedInputProps extends BaseComponentProps {
  name: string;
  label?: string;
  placeholder?: string;
  type?: 'text' | 'email' | 'password' | 'number' | 'tel' | 'url' | 'search';
  value?: string | number;
  defaultValue?: string | number;
  required?: boolean;
  disabled?: boolean;
  readOnly?: boolean;
  error?: string;
  helperText?: string;
  autoComplete?: string;
  autoFocus?: boolean;
  maxLength?: number;
  minLength?: number;
  pattern?: string;
  onChange?: (event: React.ChangeEvent<HTMLInputElement>) => void;
  onBlur?: (event: React.FocusEvent<HTMLInputElement>) => void;
  onFocus?: (event: React.FocusEvent<HTMLInputElement>) => void;
  onKeyDown?: (event: React.KeyboardEvent<HTMLInputElement>) => void;
  ariaLabel?: string;
  ariaDescribedBy?: string;
  ariaInvalid?: boolean;
}

// Performance monitoring component props
export interface PerformanceMetricProps extends BaseComponentProps {
  name: string;
  value: number;
  unit?: string;
  threshold?: {
    good: number;
    needsImprovement: number;
  };
  trend?: 'up' | 'down' | 'stable';
  showTrend?: boolean;
  precision?: number;
  formatter?: (value: number) => string;
}

// Data visualization props
export interface ChartProps extends BaseComponentProps {
  data: Array<Record<string, unknown>>;
  width?: number;
  height?: number;
  margin?: {
    top: number;
    right: number;
    bottom: number;
    left: number;
  };
  loading?: boolean;
  error?: string;
  emptyMessage?: string;
}

// File upload props
export interface FileUploadProps extends BaseComponentProps {
  accept?: string;
  multiple?: boolean;
  maxSize?: number;
  maxFiles?: number;
  onFileSelect?: (files: File[]) => void;
  onFileRemove?: (file: File) => void;
  onUploadProgress?: (progress: number) => void;
  onUploadComplete?: (result: unknown) => void;
  onUploadError?: (error: Error) => void;
  disabled?: boolean;
  dragAndDrop?: boolean;
  showPreview?: boolean;
}

export interface FormErrors {
  [fieldName: string]: string;
}

// Theme and styling types
export interface ThemeColors {
  primary: string;
  secondary: string;
  success: string;
  warning: string;
  danger: string;
  info: string;
  light: string;
  dark: string;
}

export interface ComponentTheme {
  colors: ThemeColors;
  spacing: Record<Size, string>;
  borderRadius: Record<Size, string>;
  fontSize: Record<Size, string>;
  shadows: Record<string, string>;
}
