/**
 * Custom React Hooks
 * Centralized exports for all custom hooks
 */

// Authentication Hooks
export * from './useAuth';
export * from './useAuthState';

// Data Fetching Hooks
export * from './useQuery';
export * from './useMutation';
export * from './useInfiniteQuery';

// Performance Hooks
export * from './useWebVitals';
export * from './usePerformanceMonitor';
export * from './useMemoryUsage';

// UI State Hooks
export * from './useModal';
export * from './useToast';
export * from './useLocalStorage';
export * from './useSessionStorage';

// Form Hooks
export * from './useForm';
export * from './useFormValidation';
export * from './useFieldArray';

// Error Handling Hooks
export * from './useErrorHandler';
export * from './useAsyncError';
export * from './useAsyncWithRetry';
export * from './useErrorBoundary';

// Navigation Hooks
export * from './useRouter';
export * from './useNavigation';
export * from './useBreadcrumbs';

// Search and Filter Hooks
export * from './useSearch';
export * from './useFilter';
export * from './usePagination';
export * from './useSort';

// Real-time Hooks
export * from './useWebSocket';
export * from './useRealTimeData';
export * from './useSubscription';

// Device and Browser Hooks
export * from './useMediaQuery';
export * from './useViewport';
export * from './useDeviceDetection';
export * from './useOnlineStatus';

// Optimization Hooks
export * from './useDebounce';
export * from './useThrottle';
export * from './useMemoizedCallback';
export * from './useDeepMemo';

// Lifecycle Hooks
export * from './useMount';
export * from './useUnmount';
export * from './useUpdateEffect';
export * from './usePrevious';

// Accessibility Hooks
export * from './useA11y';
export * from './useFocusManagement';
export * from './useKeyboardNavigation';

// Development Hooks
export * from './useDevTools';
export * from './useDebugValue';
export * from './useWhyDidYouUpdate';
