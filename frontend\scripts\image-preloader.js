/**
 * Image Preloading Optimization Script
 * Identifies and preloads critical above-the-fold images
 * for improved LCP (Largest Contentful Paint) performance
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class ImagePreloader {
  constructor() {
    this.projectRoot = path.resolve(__dirname, '..');
    this.srcDir = path.join(this.projectRoot, 'src');
    this.publicDir = path.join(this.projectRoot, 'public');
    this.distDir = path.join(this.projectRoot, 'dist');
    
    // Critical images that should be preloaded (above-the-fold)
    this.criticalImagePatterns = [
      'hero',
      'banner',
      'logo',
      'avatar',
      'featured',
      'main',
      'primary'
    ];
  }

  /**
   * Analyze and add image preloading to HTML files
   */
  async optimizeImagePreloading() {
    console.log('🖼️ Optimizing image preloading...');

    // Find critical images in the project
    const criticalImages = await this.findCriticalImages();
    
    if (criticalImages.length === 0) {
      console.log('⚠️ No critical images found for preloading');
      return;
    }

    console.log(`📊 Found ${criticalImages.length} critical images:`, criticalImages.map(img => img.name));

    // Add preload links to HTML files
    const htmlFiles = this.findHTMLFiles(this.distDir);
    
    for (const htmlFile of htmlFiles) {
      await this.addImagePreloads(htmlFile, criticalImages);
    }

    console.log('✅ Image preloading optimization completed');
  }

  /**
   * Find critical images that should be preloaded
   */
  async findCriticalImages() {
    const criticalImages = [];

    // Check public directory for static images
    const publicImages = this.findImagesInDirectory(this.publicDir);
    
    // Check src/assets directory for bundled images
    const assetsDir = path.join(this.srcDir, 'assets');
    const assetImages = fs.existsSync(assetsDir) ? this.findImagesInDirectory(assetsDir) : [];

    // Check dist/assets directory for built images
    const distAssetsDir = path.join(this.distDir, 'assets');
    const distImages = fs.existsSync(distAssetsDir) ? this.findImagesInDirectory(distAssetsDir) : [];

    // Combine all images
    const allImages = [...publicImages, ...assetImages, ...distImages];

    // Filter for critical images
    for (const image of allImages) {
      if (this.isCriticalImage(image.name)) {
        // Determine the correct URL path
        let urlPath = image.path;
        
        if (image.path.startsWith(this.publicDir)) {
          urlPath = image.path.replace(this.publicDir, '');
        } else if (image.path.startsWith(this.distDir)) {
          urlPath = image.path.replace(this.distDir, '');
        }
        
        // Ensure URL starts with /
        if (!urlPath.startsWith('/')) {
          urlPath = '/' + urlPath;
        }

        criticalImages.push({
          name: image.name,
          path: urlPath,
          size: image.size,
          type: this.getImageType(image.name)
        });
      }
    }

    // Sort by importance and size (smaller images first for faster loading)
    criticalImages.sort((a, b) => {
      const aImportance = this.getImageImportance(a.name);
      const bImportance = this.getImageImportance(b.name);
      
      if (aImportance !== bImportance) {
        return bImportance - aImportance; // Higher importance first
      }
      
      return a.size - b.size; // Smaller size first
    });

    // Limit to top 3 critical images to avoid overwhelming the browser
    return criticalImages.slice(0, 3);
  }

  /**
   * Find images in a directory
   */
  findImagesInDirectory(dir) {
    const images = [];
    
    if (!fs.existsSync(dir)) {
      return images;
    }

    const files = fs.readdirSync(dir, { withFileTypes: true });
    
    for (const file of files) {
      const filePath = path.join(dir, file.name);
      
      if (file.isDirectory()) {
        images.push(...this.findImagesInDirectory(filePath));
      } else if (this.isImageFile(file.name)) {
        const stats = fs.statSync(filePath);
        images.push({
          name: file.name,
          path: filePath,
          size: stats.size
        });
      }
    }
    
    return images;
  }

  /**
   * Check if file is an image
   */
  isImageFile(filename) {
    const imageExtensions = ['.jpg', '.jpeg', '.png', '.webp', '.avif', '.svg'];
    const ext = path.extname(filename).toLowerCase();
    return imageExtensions.includes(ext);
  }

  /**
   * Check if image is critical (above-the-fold)
   */
  isCriticalImage(filename) {
    const lowerName = filename.toLowerCase();
    return this.criticalImagePatterns.some(pattern => lowerName.includes(pattern));
  }

  /**
   * Get image importance score
   */
  getImageImportance(filename) {
    const lowerName = filename.toLowerCase();
    if (lowerName.includes('logo')) return 100;
    if (lowerName.includes('hero')) return 90;
    if (lowerName.includes('banner')) return 80;
    if (lowerName.includes('featured')) return 70;
    if (lowerName.includes('main')) return 60;
    return 50;
  }

  /**
   * Get image MIME type
   */
  getImageType(filename) {
    const ext = path.extname(filename).toLowerCase();
    const types = {
      '.jpg': 'image/jpeg',
      '.jpeg': 'image/jpeg',
      '.png': 'image/png',
      '.webp': 'image/webp',
      '.avif': 'image/avif',
      '.svg': 'image/svg+xml'
    };
    return types[ext] || 'image/jpeg';
  }

  /**
   * Add image preload links to HTML file
   */
  async addImagePreloads(htmlFile, criticalImages) {
    try {
      let html = fs.readFileSync(htmlFile, 'utf8');
      
      // Check if image preloads are already added
      if (html.includes('<!-- Critical image preloads -->')) {
        console.log(`⚠️ Image preloads already added to ${path.basename(htmlFile)}`);
        return;
      }

      // Generate preload links
      const preloadLinks = criticalImages.map(image => {
        return `<link rel="preload" href="${image.path}" as="image" type="${image.type}" />`;
      }).join('\n    ');

      if (preloadLinks) {
        // Insert before closing </head> tag
        const headCloseIndex = html.indexOf('</head>');
        if (headCloseIndex !== -1) {
          const beforeHead = html.substring(0, headCloseIndex);
          const afterHead = html.substring(headCloseIndex);
          
          html = `${beforeHead}    <!-- Critical image preloads -->\n    ${preloadLinks}\n${afterHead}`;
          fs.writeFileSync(htmlFile, html);
          console.log(`✅ Added image preloads to ${path.basename(htmlFile)}`);
        }
      }
    } catch (error) {
      console.error(`❌ Error processing ${htmlFile}:`, error.message);
    }
  }

  /**
   * Find all HTML files in a directory
   */
  findHTMLFiles(dir) {
    const htmlFiles = [];
    
    if (!fs.existsSync(dir)) {
      return htmlFiles;
    }
    
    const files = fs.readdirSync(dir);
    
    for (const file of files) {
      const filePath = path.join(dir, file);
      const stat = fs.statSync(filePath);
      
      if (stat.isDirectory()) {
        htmlFiles.push(...this.findHTMLFiles(filePath));
      } else if (file.endsWith('.html')) {
        htmlFiles.push(filePath);
      }
    }
    
    return htmlFiles;
  }
}

// Run the optimizer if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const optimizer = new ImagePreloader();
  optimizer.optimizeImagePreloading().catch(console.error);
}

export { ImagePreloader };
