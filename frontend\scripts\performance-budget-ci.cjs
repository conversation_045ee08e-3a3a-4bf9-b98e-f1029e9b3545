#!/usr/bin/env node

/**
 * Enhanced Performance Budget Checker for CI/CD
 * Validates build artifacts against performance budgets with detailed reporting
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

class EnhancedPerformanceBudgetChecker {
  constructor() {
    this.budgetConfig = this.loadBudgetConfig();
    this.buildDir = path.join(__dirname, '../dist');
    this.results = {
      passed: [],
      warnings: [],
      errors: [],
      score: 0,
      details: {}
    };
    this.isCI = process.env.CI === 'true';
    this.isPR = process.env.GITHUB_EVENT_NAME === 'pull_request';
  }

  /**
   * Load performance budget configuration
   */
  loadBudgetConfig() {
    const configPath = path.join(__dirname, '../performance-budget.json');
    
    if (!fs.existsSync(configPath)) {
      throw new Error('Performance budget configuration not found');
    }

    return JSON.parse(fs.readFileSync(configPath, 'utf8'));
  }

  /**
   * Main entry point for budget checking
   */
  async checkBudgets() {
    console.log('🔍 Enhanced Performance Budget Check');
    console.log('=====================================');
    console.log(`Environment: ${this.isCI ? 'CI' : 'Local'}`);
    console.log(`Event: ${process.env.GITHUB_EVENT_NAME || 'local'}`);
    console.log('');

    try {
      // Check all budget categories
      await this.checkBundleSizes();
      await this.checkLighthouseScores();
      await this.checkCoreWebVitals();
      await this.checkResourceCounts();
      await this.checkMemoryUsage();
      
      // Generate comprehensive report
      this.generateReport();
      
      // Create CI artifacts
      if (this.isCI) {
        this.createCIArtifacts();
      }
      
      // Determine exit code
      const shouldFail = this.shouldFailCI();
      
      if (shouldFail) {
        console.log('\n❌ Performance budget check failed!');
        console.log('Review the issues above and optimize before merging.');
        process.exit(1);
      } else {
        console.log('\n✅ Performance budget check passed!');
        console.log('All performance targets met.');
        process.exit(0);
      }
      
    } catch (error) {
      console.error('\n💥 Performance budget check error:', error.message);
      if (this.isCI) {
        console.error('Stack trace:', error.stack);
      }
      process.exit(1);
    }
  }

  /**
   * Check bundle size budgets with detailed analysis
   */
  async checkBundleSizes() {
    console.log('📦 Checking bundle sizes...');

    if (!fs.existsSync(this.buildDir)) {
      throw new Error('Build directory not found. Run build first.');
    }

    const bundleSizes = this.analyzeBundleSizes();
    const budgets = this.budgetConfig.budgets.find(b => b.name === 'Bundle Size Budget');
    
    if (!budgets) {
      console.log('  ⚠️  No bundle size budgets configured');
      return;
    }

    let totalSize = 0;
    const details = {};

    budgets.resourceSizes.forEach(budget => {
      const actualSize = bundleSizes[budget.resourceType] || 0;
      const budgetSize = budget.maximumSizeKb;
      totalSize += actualSize;
      
      const result = {
        name: `${budget.resourceType} bundle size`,
        actual: actualSize,
        budget: budgetSize,
        unit: 'KB',
        passed: actualSize <= budgetSize,
        percentage: Math.round((actualSize / budgetSize) * 100)
      };

      details[budget.resourceType] = result;

      if (result.passed) {
        this.results.passed.push(result);
        console.log(`  ✅ ${result.name}: ${result.actual}KB (${result.percentage}% of budget)`);
      } else {
        this.results.errors.push(result);
        console.log(`  ❌ ${result.name}: ${result.actual}KB exceeds ${result.budget}KB budget (${result.percentage}%)`);
      }
    });

    this.results.details.bundleSizes = details;
    this.results.details.totalBundleSize = totalSize;

    console.log(`  📊 Total bundle size: ${totalSize}KB`);
  }

  /**
   * Analyze bundle sizes from build output
   */
  analyzeBundleSizes() {
    const sizes = {
      script: 0,
      stylesheet: 0,
      image: 0,
      font: 0,
      total: 0
    };

    const scanDirectory = (dir) => {
      const files = fs.readdirSync(dir);
      
      files.forEach(file => {
        const filePath = path.join(dir, file);
        const stat = fs.statSync(filePath);
        
        if (stat.isDirectory()) {
          scanDirectory(filePath);
        } else {
          const sizeKB = stat.size / 1024;
          const ext = path.extname(file).toLowerCase();
          
          if (['.js', '.mjs', '.ts'].includes(ext)) {
            sizes.script += sizeKB;
          } else if (['.css', '.scss', '.sass'].includes(ext)) {
            sizes.stylesheet += sizeKB;
          } else if (['.png', '.jpg', '.jpeg', '.gif', '.svg', '.webp'].includes(ext)) {
            sizes.image += sizeKB;
          } else if (['.woff', '.woff2', '.ttf', '.eot'].includes(ext)) {
            sizes.font += sizeKB;
          }
          
          sizes.total += sizeKB;
        }
      });
    };

    scanDirectory(this.buildDir);

    // Round to 2 decimal places
    Object.keys(sizes).forEach(key => {
      sizes[key] = Math.round(sizes[key] * 100) / 100;
    });

    return sizes;
  }

  /**
   * Check Lighthouse scores if available
   */
  async checkLighthouseScores() {
    console.log('\n🔍 Checking Lighthouse scores...');

    const lighthouseDir = path.join(this.buildDir, '../.lighthouseci');
    
    if (!fs.existsSync(lighthouseDir)) {
      console.log('  ⚠️  Lighthouse results not found, skipping score check');
      return;
    }

    try {
      const files = fs.readdirSync(lighthouseDir).filter(f => f.startsWith('lhr-') && f.endsWith('.json'));
      
      if (files.length === 0) {
        console.log('  ⚠️  No Lighthouse report files found');
        return;
      }

      const reportPath = path.join(lighthouseDir, files[0]);
      const report = JSON.parse(fs.readFileSync(reportPath, 'utf8'));
      const categories = report.categories;
      const budgets = this.budgetConfig.lighthouse;

      const scores = {
        performance: Math.round(categories.performance.score * 100),
        accessibility: Math.round(categories.accessibility.score * 100),
        'best-practices': Math.round(categories['best-practices'].score * 100),
        seo: Math.round(categories.seo.score * 100),
        pwa: categories.pwa ? Math.round(categories.pwa.score * 100) : null
      };

      this.results.details.lighthouseScores = scores;

      Object.entries(budgets).forEach(([category, budget]) => {
        const actualScore = scores[category];
        
        if (actualScore === null) {
          console.log(`  ⚠️  ${category}: Not available`);
          return;
        }

        const result = {
          name: `Lighthouse ${category}`,
          actual: actualScore,
          budget: budget,
          unit: 'score',
          passed: actualScore >= budget,
          percentage: Math.round((actualScore / budget) * 100)
        };

        if (result.passed) {
          this.results.passed.push(result);
          console.log(`  ✅ ${result.name}: ${result.actual}/100 (target: ${result.budget})`);
        } else {
          this.results.errors.push(result);
          console.log(`  ❌ ${result.name}: ${result.actual}/100 below target of ${result.budget}`);
        }
      });

    } catch (error) {
      console.log(`  ⚠️  Error reading Lighthouse report: ${error.message}`);
    }
  }

  /**
   * Check Core Web Vitals from Lighthouse
   */
  async checkCoreWebVitals() {
    console.log('\n⚡ Checking Core Web Vitals...');

    const lighthouseDir = path.join(this.buildDir, '../.lighthouseci');
    
    if (!fs.existsSync(lighthouseDir)) {
      console.log('  ⚠️  Lighthouse results not found, skipping Core Web Vitals check');
      return;
    }

    try {
      const files = fs.readdirSync(lighthouseDir).filter(f => f.startsWith('lhr-') && f.endsWith('.json'));
      
      if (files.length === 0) {
        return;
      }

      const reportPath = path.join(lighthouseDir, files[0]);
      const report = JSON.parse(fs.readFileSync(reportPath, 'utf8'));
      const audits = report.audits;

      const vitals = {
        'largest-contentful-paint': {
          name: 'LCP',
          value: audits['largest-contentful-paint']?.numericValue,
          displayValue: audits['largest-contentful-paint']?.displayValue,
          budget: 2500, // 2.5s
          unit: 'ms'
        },
        'max-potential-fid': {
          name: 'FID',
          value: audits['max-potential-fid']?.numericValue,
          displayValue: audits['max-potential-fid']?.displayValue,
          budget: 100, // 100ms
          unit: 'ms'
        },
        'cumulative-layout-shift': {
          name: 'CLS',
          value: audits['cumulative-layout-shift']?.numericValue,
          displayValue: audits['cumulative-layout-shift']?.displayValue,
          budget: 0.1, // 0.1
          unit: 'score'
        }
      };

      this.results.details.coreWebVitals = {};

      Object.entries(vitals).forEach(([key, vital]) => {
        if (vital.value === undefined) {
          console.log(`  ⚠️  ${vital.name}: Not available`);
          return;
        }

        const result = {
          name: `Core Web Vital ${vital.name}`,
          actual: vital.value,
          budget: vital.budget,
          unit: vital.unit,
          displayValue: vital.displayValue,
          passed: vital.value <= vital.budget
        };

        this.results.details.coreWebVitals[vital.name] = result;

        if (result.passed) {
          this.results.passed.push(result);
          console.log(`  ✅ ${result.name}: ${result.displayValue} (target: ≤${result.budget}${result.unit})`);
        } else {
          this.results.errors.push(result);
          console.log(`  ❌ ${result.name}: ${result.displayValue} exceeds ${result.budget}${result.unit} target`);
        }
      });

    } catch (error) {
      console.log(`  ⚠️  Error reading Core Web Vitals: ${error.message}`);
    }
  }

  /**
   * Check resource count budgets
   */
  async checkResourceCounts() {
    console.log('\n📊 Checking resource counts...');

    try {
      const resourceCounts = this.analyzeResourceCounts();
      const budgets = this.budgetConfig.budgets.find(b => b.name === 'Resource Count Budget');
      
      if (!budgets) {
        console.log('  ⚠️  No resource count budgets configured');
        return;
      }

      this.results.details.resourceCounts = resourceCounts;

      budgets.resourceCounts.forEach(budget => {
        const actualCount = resourceCounts[budget.resourceType] || 0;
        
        const result = {
          name: `${budget.resourceType} count`,
          actual: actualCount,
          budget: budget.maximumCount,
          unit: 'files',
          passed: actualCount <= budget.maximumCount
        };

        if (result.passed) {
          this.results.passed.push(result);
          console.log(`  ✅ ${result.name}: ${result.actual} files (limit: ${result.budget})`);
        } else {
          this.results.warnings.push(result);
          console.log(`  ⚠️  ${result.name}: ${result.actual} files exceeds ${result.budget} limit`);
        }
      });

    } catch (error) {
      console.log(`  ⚠️  Error checking resource counts: ${error.message}`);
    }
  }

  /**
   * Analyze resource counts from build output
   */
  analyzeResourceCounts() {
    const counts = {
      script: 0,
      stylesheet: 0,
      image: 0,
      font: 0,
      total: 0
    };

    const scanDirectory = (dir) => {
      const files = fs.readdirSync(dir);
      
      files.forEach(file => {
        const filePath = path.join(dir, file);
        const stat = fs.statSync(filePath);
        
        if (stat.isDirectory()) {
          scanDirectory(filePath);
        } else {
          const ext = path.extname(file).toLowerCase();
          
          if (['.js', '.mjs', '.ts'].includes(ext)) {
            counts.script++;
          } else if (['.css', '.scss', '.sass'].includes(ext)) {
            counts.stylesheet++;
          } else if (['.png', '.jpg', '.jpeg', '.gif', '.svg', '.webp'].includes(ext)) {
            counts.image++;
          } else if (['.woff', '.woff2', '.ttf', '.eot'].includes(ext)) {
            counts.font++;
          }
          
          counts.total++;
        }
      });
    };

    scanDirectory(this.buildDir);
    return counts;
  }

  /**
   * Check memory usage budgets (estimated)
   */
  async checkMemoryUsage() {
    console.log('\n🧠 Checking estimated memory usage...');

    try {
      const totalBundleSize = this.results.details.totalBundleSize || 0;
      const estimatedMemoryMB = Math.round((totalBundleSize * 2.5) / 1024); // Rough estimate
      const budget = this.budgetConfig.monitoring?.alertThresholds?.memoryUsage?.error || 60;

      const result = {
        name: 'Estimated memory usage',
        actual: estimatedMemoryMB,
        budget: budget,
        unit: 'MB',
        passed: estimatedMemoryMB <= budget
      };

      this.results.details.estimatedMemory = result;

      if (result.passed) {
        this.results.passed.push(result);
        console.log(`  ✅ ${result.name}: ~${result.actual}MB (limit: ${result.budget}MB)`);
      } else {
        this.results.warnings.push(result);
        console.log(`  ⚠️  ${result.name}: ~${result.actual}MB may exceed ${result.budget}MB limit`);
      }

    } catch (error) {
      console.log(`  ⚠️  Error estimating memory usage: ${error.message}`);
    }
  }

  /**
   * Generate comprehensive report
   */
  generateReport() {
    console.log('\n📋 Performance Budget Report');
    console.log('============================');

    const totalChecks = this.results.passed.length + this.results.warnings.length + this.results.errors.length;
    const passRate = totalChecks > 0 ? Math.round((this.results.passed.length / totalChecks) * 100) : 0;

    console.log(`Total checks: ${totalChecks}`);
    console.log(`Passed: ${this.results.passed.length} ✅`);
    console.log(`Warnings: ${this.results.warnings.length} ⚠️`);
    console.log(`Errors: ${this.results.errors.length} ❌`);
    console.log(`Pass rate: ${passRate}%`);

    this.results.score = passRate;

    if (this.results.errors.length > 0) {
      console.log('\n❌ Failed Checks:');
      this.results.errors.forEach(error => {
        console.log(`  • ${error.name}: ${error.actual}${error.unit} (budget: ${error.budget}${error.unit})`);
      });
    }

    if (this.results.warnings.length > 0) {
      console.log('\n⚠️  Warnings:');
      this.results.warnings.forEach(warning => {
        console.log(`  • ${warning.name}: ${warning.actual}${warning.unit} (budget: ${warning.budget}${warning.unit})`);
      });
    }
  }

  /**
   * Create CI artifacts
   */
  createCIArtifacts() {
    const artifactsDir = path.join(__dirname, '../performance-artifacts');

    if (!fs.existsSync(artifactsDir)) {
      fs.mkdirSync(artifactsDir, { recursive: true });
    }

    // Create JSON report
    const report = {
      timestamp: new Date().toISOString(),
      environment: {
        ci: this.isCI,
        pr: this.isPR,
        branch: process.env.GITHUB_REF_NAME,
        commit: process.env.GITHUB_SHA
      },
      results: this.results,
      summary: {
        totalChecks: this.results.passed.length + this.results.warnings.length + this.results.errors.length,
        passed: this.results.passed.length,
        warnings: this.results.warnings.length,
        errors: this.results.errors.length,
        score: this.results.score
      }
    };

    fs.writeFileSync(
      path.join(artifactsDir, 'performance-budget-report.json'),
      JSON.stringify(report, null, 2)
    );

    // Create markdown report for PR comments
    if (this.isPR) {
      const markdown = this.generateMarkdownReport(report);
      fs.writeFileSync(
        path.join(artifactsDir, 'performance-budget-pr-comment.md'),
        markdown
      );
    }

    console.log(`\n📁 Artifacts created in ${artifactsDir}`);
  }

  /**
   * Generate markdown report for PR comments
   */
  generateMarkdownReport(report) {
    const { summary, results } = report;

    let markdown = `## 🚀 Performance Budget Check Results\n\n`;

    // Summary
    markdown += `### Summary\n`;
    markdown += `- **Total Checks**: ${summary.totalChecks}\n`;
    markdown += `- **Passed**: ${summary.passed} ✅\n`;
    markdown += `- **Warnings**: ${summary.warnings} ⚠️\n`;
    markdown += `- **Errors**: ${summary.errors} ❌\n`;
    markdown += `- **Pass Rate**: ${summary.score}%\n\n`;

    // Bundle sizes
    if (results.details.bundleSizes) {
      markdown += `### Bundle Sizes\n`;
      Object.entries(results.details.bundleSizes).forEach(([type, data]) => {
        const status = data.passed ? '✅' : '❌';
        markdown += `- **${type}**: ${data.actual}KB (${data.percentage}% of budget) ${status}\n`;
      });
      markdown += `- **Total**: ${results.details.totalBundleSize}KB\n\n`;
    }

    // Lighthouse scores
    if (results.details.lighthouseScores) {
      markdown += `### Lighthouse Scores\n`;
      Object.entries(results.details.lighthouseScores).forEach(([category, score]) => {
        if (score !== null) {
          const budget = this.budgetConfig.lighthouse[category];
          const status = score >= budget ? '✅' : '❌';
          markdown += `- **${category}**: ${score}/100 ${status}\n`;
        }
      });
      markdown += `\n`;
    }

    // Core Web Vitals
    if (results.details.coreWebVitals) {
      markdown += `### Core Web Vitals\n`;
      Object.entries(results.details.coreWebVitals).forEach(([vital, data]) => {
        const status = data.passed ? '✅' : '❌';
        markdown += `- **${vital}**: ${data.displayValue} ${status}\n`;
      });
      markdown += `\n`;
    }

    // Status
    if (summary.errors > 0) {
      markdown += `### ❌ Status: Failed\n`;
      markdown += `Performance budget check failed. Please review and optimize the following issues:\n\n`;
      results.errors.forEach(error => {
        markdown += `- **${error.name}**: ${error.actual}${error.unit} exceeds budget of ${error.budget}${error.unit}\n`;
      });
    } else {
      markdown += `### ✅ Status: Passed\n`;
      markdown += `All performance budgets passed! Great work on maintaining performance standards.\n`;
    }

    return markdown;
  }

  /**
   * Determine if CI should fail
   */
  shouldFailCI() {
    const config = this.budgetConfig.ci || {};

    // Always fail if configured to do so and there are errors
    if (config.failOnBudgetExceeded && this.results.errors.length > 0) {
      return true;
    }

    // Fail if critical thresholds are exceeded
    const criticalFailures = this.results.errors.filter(error => {
      // Critical failures: Lighthouse performance < 70, LCP > 4s, bundle size > 1MB
      if (error.name.includes('Lighthouse performance') && error.actual < 70) return true;
      if (error.name.includes('LCP') && error.actual > 4000) return true;
      if (error.name.includes('script bundle size') && error.actual > 1024) return true;
      return false;
    });

    return criticalFailures.length > 0;
  }
}

// Main execution
if (require.main === module) {
  const checker = new EnhancedPerformanceBudgetChecker();
  checker.checkBudgets().catch(error => {
    console.error('Performance budget check failed:', error);
    process.exit(1);
  });
}

module.exports = EnhancedPerformanceBudgetChecker;
