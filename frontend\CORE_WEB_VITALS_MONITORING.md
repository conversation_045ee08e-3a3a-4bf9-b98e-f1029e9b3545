# Core Web Vitals Monitoring System

## Overview

This document describes the comprehensive Core Web Vitals monitoring system implemented for production deployment. The system provides real-time monitoring, alerting, and reporting for Core Web Vitals metrics to ensure optimal user experience.

## Architecture

### Components

1. **ProductionWebVitalsMonitor** - Headless production monitoring component
2. **CoreWebVitalsDashboard** - Visual dashboard for metrics and alerts
3. **WebVitalsAlerting** - Configurable alerting system with multiple notification channels
4. **WebVitalsTracker** - Core tracking and measurement engine

### Key Features

- ✅ **Real-time Core Web Vitals tracking** (LCP, FID, CLS, FCP, TTFB)
- ✅ **Configurable alert thresholds** with warning and critical levels
- ✅ **Multiple notification channels** (Webhook, Email, Slack)
- ✅ **Automatic metric reporting** to external endpoints
- ✅ **Production-ready monitoring** with minimal performance impact
- ✅ **Development dashboard** for real-time debugging
- ✅ **Retry mechanisms** with exponential backoff for reliability

## Configuration

### Environment Variables

Add these variables to your `.env` file:

```bash
# Core Web Vitals Monitoring
VITE_ENABLE_WEB_VITALS=true
VITE_ENABLE_WEB_VITALS_ALERTS=true

# Monitoring Endpoints
VITE_PERFORMANCE_ENDPOINT=https://your-monitoring-endpoint.com/api/metrics
VITE_WEBHOOK_URL=https://your-webhook-endpoint.com/alerts
VITE_EMAIL_ENDPOINT=https://your-email-service.com/send
VITE_SLACK_WEBHOOK=https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK
VITE_ALERT_EMAIL=<EMAIL>
```

### Alert Thresholds

Default thresholds are configured based on Google's Core Web Vitals recommendations:

```typescript
// Critical thresholds (Core Web Vitals)
LCP: 2500ms  // Largest Contentful Paint
FID: 100ms   // First Input Delay
CLS: 0.1     // Cumulative Layout Shift

// Warning thresholds
LCP: 2000ms
FID: 75ms
CLS: 0.05
FCP: 1800ms  // First Contentful Paint
TTFB: 600ms  // Time to First Byte
```

## Usage

### Automatic Integration

The monitoring system is automatically integrated into the main App component and starts monitoring immediately in production:

```tsx
// Automatically included in App.tsx
<ProductionWebVitalsMonitor />
```

### Manual Dashboard Integration

For custom dashboards, use the CoreWebVitalsDashboard component:

```tsx
import { CoreWebVitalsDashboard } from '@/components/monitoring/CoreWebVitalsDashboard';

function MyDashboard() {
  return (
    <CoreWebVitalsDashboard 
      showAlerts={true}
      showTrends={true}
      className="my-custom-class"
    />
  );
}
```

### Programmatic Access

Access metrics and alerts programmatically:

```typescript
import { webVitalsTracker } from '@/utils/webVitals';
import { webVitalsAlerting } from '@/utils/webVitalsAlerting';

// Get current metrics
const metrics = webVitalsTracker.getMetrics();
const summary = webVitalsTracker.getSummary();

// Subscribe to new metrics
const unsubscribe = webVitalsTracker.onMetric((metric) => {
  console.log('New metric:', metric);
});

// Subscribe to alerts
const unsubscribeAlerts = webVitalsAlerting.onAlert((alert) => {
  console.log('New alert:', alert);
});
```

## Monitoring Endpoints

### Metrics Reporting

The system automatically sends metrics to the configured endpoint every 30 seconds:

```json
POST /api/metrics
{
  "timestamp": 1640995200000,
  "url": "https://rag-prompt-library.web.app/dashboard",
  "userAgent": "Mozilla/5.0...",
  "metrics": [
    {
      "name": "LCP",
      "value": 1850,
      "rating": "good",
      "timestamp": 1640995200000,
      "id": "metric-123",
      "navigationType": "navigate"
    }
  ]
}
```

### Alert Notifications

Critical alerts are sent immediately to all configured channels:

```json
POST /alerts
{
  "alerts": [
    {
      "metric": "LCP",
      "value": 3200,
      "severity": "critical",
      "timestamp": 1640995200000,
      "url": "https://rag-prompt-library.web.app/dashboard"
    }
  ]
}
```

## Alert Channels

### Webhook Notifications

Generic webhook format for integration with monitoring systems:

```json
{
  "alert_id": "alert_1640995200000_abc123",
  "severity": "critical",
  "metric": "LCP",
  "value": 3200,
  "threshold": 2500,
  "rating": "poor",
  "message": "CRITICAL: LCP performance issue detected...",
  "timestamp": 1640995200000,
  "url": "https://rag-prompt-library.web.app/dashboard",
  "user_agent": "Mozilla/5.0..."
}
```

### Slack Integration

Rich Slack notifications with color-coded severity:

- 🚨 **Critical alerts** (red) - Core Web Vitals thresholds exceeded
- ⚠️ **Warning alerts** (yellow) - Performance degradation detected

### Email Notifications

Structured email alerts with detailed metric information and context.

## Performance Impact

The monitoring system is designed for minimal performance impact:

- **Bundle size**: ~15KB (gzipped) for monitoring components
- **Runtime overhead**: <1ms per metric measurement
- **Memory usage**: <5MB for metric storage (last 100 measurements)
- **Network impact**: Batched reporting every 30 seconds

## Development vs Production

### Development Mode
- Visual dashboard with real-time metrics
- Console logging for debugging
- Expanded metric details and trends

### Production Mode
- Headless monitoring (no UI)
- Automatic metric reporting
- Critical alert notifications only
- Optimized for performance

## Troubleshooting

### Common Issues

1. **Metrics not being tracked**
   - Verify `VITE_ENABLE_WEB_VITALS=true` in environment
   - Check browser console for web-vitals library errors
   - Ensure modern browser with PerformanceObserver support

2. **Alerts not being sent**
   - Verify alert endpoints are configured correctly
   - Check network connectivity to notification services
   - Review browser console for HTTP errors

3. **High alert volume**
   - Review and adjust alert thresholds
   - Implement alert rate limiting
   - Consider using warning thresholds instead of critical

### Debug Mode

Enable debug logging in development:

```typescript
// In browser console
localStorage.setItem('webvitals-debug', 'true');
```

## Metrics Reference

### Core Web Vitals

- **LCP (Largest Contentful Paint)**: Time to render the largest content element
- **FID (First Input Delay)**: Time from first user interaction to browser response
- **CLS (Cumulative Layout Shift)**: Visual stability metric for unexpected layout shifts

### Additional Metrics

- **FCP (First Contentful Paint)**: Time to first content render
- **TTFB (Time to First Byte)**: Server response time
- **TBT (Total Blocking Time)**: Main thread blocking time
- **TTI (Time to Interactive)**: Time until page is fully interactive

## Best Practices

1. **Set appropriate thresholds** based on your application's performance characteristics
2. **Monitor trends** over time rather than individual measurements
3. **Implement gradual rollout** for alert configuration changes
4. **Use warning alerts** for proactive monitoring before critical thresholds
5. **Regularly review and update** alert rules based on user feedback and performance data

## Integration with CI/CD

The monitoring system integrates with existing performance budgets and CI/CD pipelines to prevent performance regressions during deployment.

## Support

For issues or questions about the Core Web Vitals monitoring system, refer to:
- Browser console logs (development mode)
- Network tab for API request debugging
- Performance tab for detailed timing analysis
