#!/usr/bin/env node

/**
 * Find Unused Icons Script
 * Identifies icons that are exported but not actually used in the codebase
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const SRC_DIR = path.join(__dirname, '../src');

// Get exported icons from our optimized files
function getExportedIcons() {
  const heroiconsFile = path.join(SRC_DIR, 'components/icons/heroicons.ts');
  const lucideFile = path.join(SRC_DIR, 'components/icons/lucide.ts');
  
  const exported = {
    heroicons: new Set(),
    lucide: new Set()
  };

  // Parse heroicons exports
  if (fs.existsSync(heroiconsFile)) {
    const content = fs.readFileSync(heroiconsFile, 'utf8');
    const exportMatches = content.match(/export\s*{\s*([^}]+)\s*}/g);
    if (exportMatches) {
      exportMatches.forEach(match => {
        const iconsMatch = match.match(/{\s*([^}]+)\s*}/);
        if (iconsMatch) {
          const icons = iconsMatch[1].split(',').map(icon => {
            // Handle aliases like "StarIcon as StarIconSolid"
            const cleanIcon = icon.trim().split(' as ')[0].trim();
            return cleanIcon;
          });
          icons.forEach(icon => {
            if (icon && icon !== '') {
              exported.heroicons.add(icon);
            }
          });
        }
      });
    }
  }

  // Parse lucide exports
  if (fs.existsSync(lucideFile)) {
    const content = fs.readFileSync(lucideFile, 'utf8');
    const exportMatches = content.match(/export\s*{\s*([^}]+)\s*}/g);
    if (exportMatches) {
      exportMatches.forEach(match => {
        const iconsMatch = match.match(/{\s*([^}]+)\s*}/);
        if (iconsMatch) {
          const icons = iconsMatch[1].split(',').map(icon => icon.trim());
          icons.forEach(icon => {
            if (icon && icon !== '') {
              exported.lucide.add(icon);
            }
          });
        }
      });
    }
  }

  return exported;
}

// Find actual icon usage in the codebase
function findIconUsage() {
  const used = {
    heroicons: new Set(),
    lucide: new Set()
  };

  function scanFile(filePath) {
    const content = fs.readFileSync(filePath, 'utf8');
    
    // Find all icon references in JSX and TypeScript
    // Look for icon names followed by common patterns
    const iconPatterns = [
      /<(\w+Icon)\s/g,  // JSX usage like <UserIcon />
      /{\s*(\w+Icon)\s*}/g,  // Object destructuring
      /(\w+Icon)\.className/g,  // Property access
      /(\w+Icon)\s*className/g,  // Props
      /icon:\s*(\w+Icon)/g,  // Object properties
      /Icon:\s*(\w+Icon)/g,  // Type annotations
    ];

    // Lucide icons (no Icon suffix)
    const lucidePatterns = [
      /<(AlertCircle|AlertTriangle|ArrowLeft|ArrowRight|BarChart|BarChart3|BookOpen|Brain|Briefcase|Calendar|CheckCircle|Clock|Code|Database|DollarSign|Download|Edit|Eye|EyeOff|File|FileText|GraduationCap|Hash|HelpCircle|Home|Info|Lightbulb|List|Lock|LogOut|Mail|Menu|MessageSquare|Play|Plus|RefreshCw|Save|Search|Settings|Sparkles|Star|Store|Tag|Target|ToggleLeft|Trash2|TrendingUp|Type|Upload|User|Users|Wand2|X|XCircle|Zap)\s/g,
      /{\s*(AlertCircle|AlertTriangle|ArrowLeft|ArrowRight|BarChart|BarChart3|BookOpen|Brain|Briefcase|Calendar|CheckCircle|Clock|Code|Database|DollarSign|Download|Edit|Eye|EyeOff|File|FileText|GraduationCap|Hash|HelpCircle|Home|Info|Lightbulb|List|Lock|LogOut|Mail|Menu|MessageSquare|Play|Plus|RefreshCw|Save|Search|Settings|Sparkles|Star|Store|Tag|Target|ToggleLeft|Trash2|TrendingUp|Type|Upload|User|Users|Wand2|X|XCircle|Zap)\s*}/g,
    ];

    // Check heroicons
    iconPatterns.forEach(pattern => {
      let match;
      while ((match = pattern.exec(content)) !== null) {
        used.heroicons.add(match[1]);
      }
    });

    // Check lucide icons
    lucidePatterns.forEach(pattern => {
      let match;
      while ((match = pattern.exec(content)) !== null) {
        used.lucide.add(match[1]);
      }
    });
  }

  function scanDirectory(dir) {
    const files = fs.readdirSync(dir);
    
    files.forEach(file => {
      const filePath = path.join(dir, file);
      const stat = fs.statSync(filePath);
      
      if (stat.isDirectory() && !file.startsWith('.') && file !== 'node_modules') {
        scanDirectory(filePath);
      } else if (file.endsWith('.tsx') || file.endsWith('.ts') || file.endsWith('.jsx') || file.endsWith('.js')) {
        // Skip the icon export files themselves
        if (!filePath.includes('components/icons/')) {
          scanFile(filePath);
        }
      }
    });
  }

  scanDirectory(SRC_DIR);
  return used;
}

function findUnusedIcons() {
  console.log('🔍 Analyzing icon usage...');
  
  const exported = getExportedIcons();
  const used = findIconUsage();
  
  console.log(`📊 Exported Heroicons: ${exported.heroicons.size}`);
  console.log(`📊 Used Heroicons: ${used.heroicons.size}`);
  console.log(`📊 Exported Lucide: ${exported.lucide.size}`);
  console.log(`📊 Used Lucide: ${used.lucide.size}`);
  
  // Find unused icons
  const unusedHeroicons = Array.from(exported.heroicons).filter(icon => !used.heroicons.has(icon));
  const unusedLucide = Array.from(exported.lucide).filter(icon => !used.lucide.has(icon));
  
  console.log('\n🗑️  Unused Icons Found:');
  
  if (unusedHeroicons.length > 0) {
    console.log(`\n📦 Heroicons (${unusedHeroicons.length} unused):`);
    unusedHeroicons.forEach(icon => console.log(`  - ${icon}`));
  } else {
    console.log('\n✅ All exported Heroicons are being used!');
  }
  
  if (unusedLucide.length > 0) {
    console.log(`\n📦 Lucide (${unusedLucide.length} unused):`);
    unusedLucide.forEach(icon => console.log(`  - ${icon}`));
  } else {
    console.log('\n✅ All exported Lucide icons are being used!');
  }
  
  // Show some used icons for verification
  console.log('\n✅ Sample of used icons (verification):');
  console.log('Heroicons:', Array.from(used.heroicons).slice(0, 5).join(', '));
  console.log('Lucide:', Array.from(used.lucide).slice(0, 5).join(', '));
  
  return {
    unused: {
      heroicons: unusedHeroicons,
      lucide: unusedLucide
    },
    used: {
      heroicons: Array.from(used.heroicons),
      lucide: Array.from(used.lucide)
    },
    exported: {
      heroicons: Array.from(exported.heroicons),
      lucide: Array.from(exported.lucide)
    }
  };
}

// Run the analysis
const result = findUnusedIcons();

// Generate cleanup recommendations
if (result.unused.heroicons.length > 0 || result.unused.lucide.length > 0) {
  console.log('\n📋 Cleanup Recommendations:');
  
  if (result.unused.heroicons.length > 0) {
    console.log('\n1. Remove these Heroicons from heroicons.ts:');
    result.unused.heroicons.forEach(icon => {
      console.log(`   ${icon},`);
    });
  }
  
  if (result.unused.lucide.length > 0) {
    console.log('\n2. Remove these Lucide icons from lucide.ts:');
    result.unused.lucide.forEach(icon => {
      console.log(`   ${icon},`);
    });
  }
  
  const totalUnused = result.unused.heroicons.length + result.unused.lucide.length;
  console.log(`\n💾 Potential bundle size reduction: ${totalUnused} unused icons`);
} else {
  console.log('\n🎉 No unused icons found! Your icon exports are perfectly optimized.');
}

export { findUnusedIcons };
