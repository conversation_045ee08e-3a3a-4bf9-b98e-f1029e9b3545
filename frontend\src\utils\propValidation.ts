/**
 * Comprehensive Prop Validation System
 * Provides runtime validation for React component props with TypeScript integration
 */

import React from 'react';

// Validation result types
export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

// Validation rule types
export interface PropValidationRule<T = unknown> {
  required?: boolean;
  type?: 'string' | 'number' | 'boolean' | 'object' | 'array' | 'function';
  minLength?: number;
  maxLength?: number;
  min?: number;
  max?: number;
  pattern?: RegExp;
  oneOf?: T[];
  custom?: (value: T) => string | null;
  message?: string;
}

export interface PropValidationSchema {
  [propName: string]: PropValidationRule;
}

// Validation functions
export const validateProp = <T>(
  value: T,
  rule: PropValidationRule<T>,
  propName: string
): ValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Required validation
  if (rule.required && (value === undefined || value === null)) {
    errors.push(`${propName} is required`);
    return { isValid: false, errors, warnings };
  }

  // Skip other validations if value is undefined/null and not required
  if (value === undefined || value === null) {
    return { isValid: true, errors, warnings };
  }

  // Type validation
  if (rule.type) {
    const actualType = Array.isArray(value) ? 'array' : typeof value;
    if (actualType !== rule.type) {
      errors.push(`${propName} must be of type ${rule.type}, got ${actualType}`);
    }
  }

  // String/Array length validation
  if (typeof value === 'string' || Array.isArray(value)) {
    if (rule.minLength !== undefined && value.length < rule.minLength) {
      errors.push(`${propName} must have at least ${rule.minLength} characters/items`);
    }
    if (rule.maxLength !== undefined && value.length > rule.maxLength) {
      errors.push(`${propName} must have at most ${rule.maxLength} characters/items`);
    }
  }

  // Number range validation
  if (typeof value === 'number') {
    if (rule.min !== undefined && value < rule.min) {
      errors.push(`${propName} must be at least ${rule.min}`);
    }
    if (rule.max !== undefined && value > rule.max) {
      errors.push(`${propName} must be at most ${rule.max}`);
    }
  }

  // Pattern validation for strings
  if (typeof value === 'string' && rule.pattern) {
    if (!rule.pattern.test(value)) {
      errors.push(rule.message || `${propName} does not match required pattern`);
    }
  }

  // OneOf validation
  if (rule.oneOf && !rule.oneOf.includes(value)) {
    errors.push(`${propName} must be one of: ${rule.oneOf.join(', ')}`);
  }

  // Custom validation
  if (rule.custom) {
    const customError = rule.custom(value);
    if (customError) {
      errors.push(customError);
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
};

export const validateProps = (
  props: Record<string, unknown>,
  schema: PropValidationSchema
): ValidationResult => {
  const allErrors: string[] = [];
  const allWarnings: string[] = [];

  // Validate each prop according to schema
  Object.entries(schema).forEach(([propName, rule]) => {
    const result = validateProp(props[propName], rule, propName);
    allErrors.push(...result.errors);
    allWarnings.push(...result.warnings);
  });

  // Check for unexpected props (development warning)
  if (process.env.NODE_ENV === 'development') {
    Object.keys(props).forEach(propName => {
      if (!schema[propName] && propName !== 'children') {
        allWarnings.push(`Unexpected prop: ${propName}`);
      }
    });
  }

  return {
    isValid: allErrors.length === 0,
    errors: allErrors,
    warnings: allWarnings
  };
};

// HOC for prop validation
export const withPropValidation = <P extends Record<string, unknown>>(
  Component: React.ComponentType<P>,
  schema: PropValidationSchema,
  options: {
    throwOnError?: boolean;
    logWarnings?: boolean;
    displayName?: string;
  } = {}
) => {
  const {
    throwOnError = false,
    logWarnings = process.env.NODE_ENV === 'development',
    displayName
  } = options;

  const WrappedComponent: React.FC<P> = (props) => {
    const validationResult = validateProps(props as Record<string, unknown>, schema);

    if (!validationResult.isValid) {
      const errorMessage = `Prop validation failed for ${displayName || Component.displayName || Component.name}:\n${validationResult.errors.join('\n')}`;
      
      if (throwOnError) {
        throw new Error(errorMessage);
      } else if (process.env.NODE_ENV === 'development') {
        console.error(errorMessage);
      }
    }

    if (logWarnings && validationResult.warnings.length > 0) {
      console.warn(`Prop validation warnings for ${displayName || Component.displayName || Component.name}:\n${validationResult.warnings.join('\n')}`);
    }

    return React.createElement(Component, props);
  };

  WrappedComponent.displayName = `withPropValidation(${displayName || Component.displayName || Component.name})`;
  
  return WrappedComponent;
};

// Common validation rules
export const commonValidationRules = {
  email: {
    type: 'string' as const,
    pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    message: 'Must be a valid email address'
  },
  url: {
    type: 'string' as const,
    pattern: /^https?:\/\/.+/,
    message: 'Must be a valid URL'
  },
  positiveNumber: {
    type: 'number' as const,
    min: 0,
    message: 'Must be a positive number'
  },
  nonEmptyString: {
    type: 'string' as const,
    minLength: 1,
    message: 'Cannot be empty'
  },
  className: {
    type: 'string' as const,
    required: false
  },
  dataTestId: {
    type: 'string' as const,
    required: false
  },
  children: {
    required: false
  }
};

// Utility for creating component prop schemas
export const createPropSchema = (
  baseSchema: PropValidationSchema,
  additionalRules: PropValidationSchema = {}
): PropValidationSchema => {
  return {
    ...commonValidationRules,
    ...baseSchema,
    ...additionalRules
  };
};

// Hook for runtime prop validation
export const usePropValidation = (
  props: Record<string, unknown>,
  schema: PropValidationSchema,
  componentName?: string
) => {
  React.useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      const result = validateProps(props, schema);
      
      if (!result.isValid) {
        console.error(`Prop validation failed for ${componentName}:`, result.errors);
      }
      
      if (result.warnings.length > 0) {
        console.warn(`Prop validation warnings for ${componentName}:`, result.warnings);
      }
    }
  }, [props, schema, componentName]);
};

// Type-safe prop validation for specific component types
export const createTypedPropValidator = <T extends Record<string, unknown>>() => {
  return (schema: PropValidationSchema) => {
    return (props: T): ValidationResult => {
      return validateProps(props as Record<string, unknown>, schema);
    };
  };
};
