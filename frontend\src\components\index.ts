/**
 * Components
 * Centralized exports for all components
 */

// UI Components (Core Design System)
export * from './ui';

// Layout Components
export * from './layout';

// Common Utilities and Shared Components
export * from './common';

// Feature-Specific Components
export * from './features';

// Monitoring and Performance Components
export * from './monitoring';

// Icon Components
export * from './icons';

// Convenience re-exports for frequently used components
export { Button } from './ui';
export { Modal, useModal } from './ui';
export { Card } from './ui';
export { Input, Select, Checkbox } from './ui';
export { Table } from './ui';
export { LoadingSpinner } from './ui';
export { Toast, useToast } from './ui';
export { ErrorBoundary } from './ui';

// Layout re-exports
export { Header } from './layout';
export { Sidebar } from './layout';
export { Layout } from './layout';

// Monitoring re-exports
export { PerformanceMonitor } from './monitoring';
export { APIPerformanceMonitor } from './monitoring';
export { WebVitalsDashboard } from './monitoring';
export { PerformanceDashboard } from './monitoring';
