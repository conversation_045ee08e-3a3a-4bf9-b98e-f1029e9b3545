import React from 'react';
import { render} from '@testing-library/react';
import { vi, describe, it, expect } from 'vitest';
import { BrowserRouter } from 'react-router-dom';
import { PromptList } from '../PromptList';

// Mock useAuth
vi.mock('../../../contexts/AuthContext', () => ({
  useAuth: vi.fn(() => ({
    currentUser: { uid: 'test-user-id' }
  }))
}));

// Mock useNavigate
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useNavigate: vi.fn(() => vi.fn())
  };
});

// Mock PromptService
vi.mock('../../../services/promptService', () => ({
  PromptService: {
    subscribeToUserPrompts: vi.fn(() => () => {}), // Return unsubscribe function
    deletePrompt: vi.fn(() => Promise.resolve()),
    duplicatePrompt: vi.fn(() => Promise.resolve())
  }
}));

// Mock Toast context
vi.mock('../../common/Toast', () => ({
  useToast: () => ({
    showToast: vi.fn()
  })
}));

describe('PromptList', () => {
  it('renders without crashing', () => {
    render(
      <BrowserRouter>
        <PromptList />
      </BrowserRouter>
    );
    // Component shows loading spinner initially
    expect(document.querySelector('.animate-spin')).toBeDefined();
  });
});
