/**
 * 🚨 COMPREHENSIVE EXECUTE BUTTON DEBUG SCRIPT
 * 
 * This script will identify the EXACT issue preventing the Execute button from working.
 * 
 * INSTRUCTIONS:
 * 1. Open https://rag-prompt-library.web.app/ in your browser
 * 2. Open Developer Tools (F12)
 * 3. Go to Console tab
 * 4. Copy and paste this ENTIRE script
 * 5. Press Enter to run
 * 6. Follow the instructions that appear
 */

console.clear();
console.log('%c🚨 COMPREHENSIVE EXECUTE BUTTON DEBUG ANALYSIS', 'color: #dc2626; font-size: 18px; font-weight: bold; background: #fef2f2; padding: 10px;');

const debug = {
    results: [],
    log: function(message, type = 'info', data = null) {
        const styles = {
            info: 'color: #3b82f6; font-weight: bold;',
            success: 'color: #16a34a; font-weight: bold;',
            error: 'color: #dc2626; font-weight: bold;',
            warning: 'color: #ca8a04; font-weight: bold;',
            critical: 'color: #ffffff; background: #dc2626; padding: 5px; font-weight: bold;'
        };
        console.log(`%c${message}`, styles[type]);
        if (data) console.log(data);
        this.results.push({ message, type, data, timestamp: new Date().toISOString() });
    },
    
    section: function(title) {
        console.log(`\n%c🔧 ${title}`, 'color: #1f2937; font-size: 16px; font-weight: bold; background: #f3f4f6; padding: 8px; border-left: 4px solid #3b82f6;');
    }
};

// STEP 1: Environment Analysis
debug.section('STEP 1: ENVIRONMENT ANALYSIS');

debug.log('🌐 Current URL: ' + window.location.href);
debug.log('🌐 Current Pathname: ' + window.location.pathname);
debug.log('🌐 User Agent: ' + navigator.userAgent);

// Check if we're on the correct domain
if (!window.location.href.includes('rag-prompt-library.web.app')) {
    debug.log('❌ CRITICAL: Not on the correct domain!', 'critical');
    debug.log('Please navigate to: https://rag-prompt-library.web.app/', 'error');
} else {
    debug.log('✅ On correct domain', 'success');
}

// STEP 2: Firebase Configuration Check
debug.section('STEP 2: FIREBASE CONFIGURATION CHECK');

try {
    if (window.firebase) {
        debug.log('✅ Firebase SDK loaded', 'success');
        
        // Check Firebase app configuration
        const apps = window.firebase.apps;
        if (apps && apps.length > 0) {
            const app = apps[0];
            debug.log('✅ Firebase app initialized', 'success');
            debug.log('📋 Project ID: ' + app.options.projectId);
            debug.log('📋 Auth Domain: ' + app.options.authDomain);
        } else {
            debug.log('❌ No Firebase apps found', 'error');
        }
        
        // Check authentication
        const auth = window.firebase.auth();
        if (auth) {
            const currentUser = auth.currentUser;
            if (currentUser) {
                debug.log('✅ User authenticated: ' + currentUser.email, 'success');
                debug.log('📋 User ID: ' + currentUser.uid);
            } else {
                debug.log('❌ User not authenticated', 'error');
                debug.log('🔧 Please sign in first!', 'warning');
            }
        }
        
        // Check Functions configuration
        const functions = window.firebase.functions();
        if (functions) {
            debug.log('✅ Firebase Functions available', 'success');
            debug.log('📋 Functions region: ' + (functions.region || 'default'));
        }
        
    } else {
        debug.log('❌ Firebase SDK not found on window object', 'error');
    }
} catch (error) {
    debug.log('❌ Error checking Firebase: ' + error.message, 'error');
}

// STEP 3: React Router Analysis
debug.section('STEP 3: REACT ROUTER ANALYSIS');

try {
    // Check for React Router elements
    const routerElements = document.querySelectorAll('[data-reactroot], [data-react-helmet]');
    debug.log(`📋 Found ${routerElements.length} React elements`);
    
    // Check for navigation elements
    const navElements = document.querySelectorAll('nav, [role="navigation"]');
    debug.log(`📋 Found ${navElements.length} navigation elements`);
    
    // Check current route
    debug.log('📋 Current route: ' + window.location.pathname);
    
} catch (error) {
    debug.log('❌ Error analyzing React Router: ' + error.message, 'error');
}

// STEP 4: Execute Button Detection
debug.section('STEP 4: EXECUTE BUTTON DETECTION');

function findExecuteButtons() {
    const buttons = Array.from(document.querySelectorAll('button'));
    const executeButtons = buttons.filter(btn => 
        btn.textContent.toLowerCase().includes('execute') ||
        btn.getAttribute('data-testid')?.includes('execute') ||
        btn.className.includes('execute')
    );
    
    debug.log(`📋 Total buttons found: ${buttons.length}`);
    debug.log(`📋 Execute buttons found: ${executeButtons.length}`);
    
    executeButtons.forEach((button, index) => {
        debug.log(`🔘 Execute Button ${index + 1}:`);
        debug.log(`   Text: "${button.textContent.trim()}"`);
        debug.log(`   Disabled: ${button.disabled}`);
        debug.log(`   Classes: ${button.className}`);
        debug.log(`   Data attributes: ${Array.from(button.attributes).filter(attr => attr.name.startsWith('data-')).map(attr => `${attr.name}="${attr.value}"`).join(', ')}`);
        
        // Add click listener for debugging
        button.addEventListener('click', function(event) {
            debug.log(`🖱️ EXECUTE BUTTON CLICKED!`, 'warning');
            debug.log(`   Button: "${this.textContent.trim()}"`);
            debug.log(`   URL before click: ${window.location.href}`);
            debug.log(`   Event prevented: ${event.defaultPrevented}`);
            
            setTimeout(() => {
                debug.log(`   URL after click: ${window.location.href}`);
                debug.log(`   URL changed: ${window.location.href !== event.target.dataset.originalUrl}`);
            }, 100);
            
            // Store original URL for comparison
            event.target.dataset.originalUrl = window.location.href;
        }, { once: false });
    });
    
    return executeButtons;
}

const executeButtons = findExecuteButtons();

// STEP 5: Page Content Analysis
debug.section('STEP 5: PAGE CONTENT ANALYSIS');

try {
    // Check if we're on the prompts page
    const isPromptsPage = window.location.pathname.includes('/prompts') || 
                         document.title.toLowerCase().includes('prompt') ||
                         document.querySelector('h1, h2')?.textContent.toLowerCase().includes('prompt');
    
    debug.log(`📋 On prompts page: ${isPromptsPage}`);
    
    // Check for prompt cards
    const promptCards = document.querySelectorAll('[class*="card"], [class*="prompt"], .bg-white');
    debug.log(`📋 Potential prompt cards: ${promptCards.length}`);
    
    // Check for loading states
    const loadingElements = document.querySelectorAll('[class*="loading"], [class*="spinner"]');
    debug.log(`📋 Loading elements: ${loadingElements.length}`);
    
    // Check for error messages
    const errorElements = document.querySelectorAll('[class*="error"], [role="alert"]');
    debug.log(`📋 Error elements: ${errorElements.length}`);
    
} catch (error) {
    debug.log('❌ Error analyzing page content: ' + error.message, 'error');
}

// STEP 6: Network Connectivity Test
debug.section('STEP 6: NETWORK CONNECTIVITY TEST');

async function testNetworkConnectivity() {
    const endpoints = [
        'https://rag-prompt-library.web.app/',
        'https://us-central1-rag-prompt-library.cloudfunctions.net/api/health',
        'https://australia-southeast1-rag-prompt-library.cloudfunctions.net/api/health'
    ];
    
    for (const endpoint of endpoints) {
        try {
            debug.log(`🧪 Testing: ${endpoint}`);
            const response = await fetch(endpoint, { 
                method: 'HEAD',
                mode: 'no-cors'
            });
            debug.log(`✅ ${endpoint} - Accessible`, 'success');
        } catch (error) {
            debug.log(`❌ ${endpoint} - Error: ${error.message}`, 'error');
        }
    }
}

testNetworkConnectivity();

// STEP 7: Create Manual Test Functions
debug.section('STEP 7: MANUAL TEST FUNCTIONS');

window.debugExecuteFlow = function() {
    debug.log('🧪 MANUAL EXECUTE FLOW TEST', 'warning');
    
    const executeButtons = findExecuteButtons();
    if (executeButtons.length === 0) {
        debug.log('❌ No Execute buttons found. Navigate to Prompts page first!', 'error');
        return;
    }
    
    debug.log('✅ Execute buttons found. Click monitoring enabled.', 'success');
    debug.log('💡 Now click an Execute button and watch the console output.', 'info');
};

window.debugNavigation = function(path) {
    debug.log(`🧪 TESTING NAVIGATION TO: ${path}`, 'warning');
    const originalUrl = window.location.href;
    
    try {
        window.history.pushState({}, '', path);
        debug.log(`✅ Navigation attempted`, 'info');
        
        setTimeout(() => {
            const newUrl = window.location.href;
            if (newUrl.includes(path)) {
                debug.log(`✅ Navigation successful: ${newUrl}`, 'success');
            } else {
                debug.log(`❌ Navigation failed. Still at: ${newUrl}`, 'error');
            }
        }, 100);
    } catch (error) {
        debug.log(`❌ Navigation error: ${error.message}`, 'error');
    }
};

window.debugSummary = function() {
    debug.log('\n📊 DEBUG SUMMARY', 'warning');
    debug.log('================', 'warning');
    
    const issues = debug.results.filter(r => r.type === 'error' || r.type === 'critical');
    const successes = debug.results.filter(r => r.type === 'success');
    
    debug.log(`✅ Successful checks: ${successes.length}`);
    debug.log(`❌ Issues found: ${issues.length}`);
    
    if (issues.length > 0) {
        debug.log('\n🚨 CRITICAL ISSUES:', 'error');
        issues.forEach(issue => {
            debug.log(`   - ${issue.message}`, 'error');
        });
    }
    
    debug.log('\n📋 NEXT STEPS:', 'info');
    debug.log('1. If not authenticated: Sign in first', 'info');
    debug.log('2. Navigate to Prompts page', 'info');
    debug.log('3. Run: debugExecuteFlow()', 'info');
    debug.log('4. Click an Execute button', 'info');
    debug.log('5. Report the console output', 'info');
};

// STEP 8: Final Instructions
debug.section('STEP 8: NEXT STEPS');

debug.log('🎯 DEBUG ANALYSIS COMPLETE!', 'success');
debug.log('', 'info');
debug.log('📋 AVAILABLE FUNCTIONS:', 'info');
debug.log('• debugExecuteFlow() - Monitor Execute button clicks', 'info');
debug.log('• debugNavigation(path) - Test navigation', 'info');
debug.log('• debugSummary() - Show summary of issues', 'info');
debug.log('', 'info');
debug.log('🔧 IMMEDIATE ACTIONS:', 'warning');
debug.log('1. If you see authentication errors above: Sign in first', 'warning');
debug.log('2. Navigate to the Prompts page', 'warning');
debug.log('3. Run: debugExecuteFlow()', 'warning');
debug.log('4. Click an Execute button', 'warning');
debug.log('5. Copy ALL console output and report back', 'warning');

// Auto-run summary
setTimeout(() => {
    window.debugSummary();
}, 2000);
