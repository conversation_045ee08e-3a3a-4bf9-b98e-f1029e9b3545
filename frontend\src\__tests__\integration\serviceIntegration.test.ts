/**
 * Service Layer Integration Tests
 * 
 * Tests integration between different services and their interactions
 * with Firebase, caching, and error handling systems.
 * 
 * @module ServiceIntegrationTests
 * @version 1.0.0
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';

// Mock Firebase
const mockFirestore = {
  collection: vi.fn(),
  doc: vi.fn(),
  getDoc: vi.fn(),
  getDocs: vi.fn(),
  addDoc: vi.fn(),
  updateDoc: vi.fn(),
  deleteDoc: vi.fn(),
  query: vi.fn(),
  where: vi.fn(),
  orderBy: vi.fn(),
  limit: vi.fn(),
};

const mockAuth = {
  currentUser: { uid: 'test-user-id', email: '<EMAIL>' },
  onAuthStateChanged: vi.fn(),
  signInWithEmailAndPassword: vi.fn(),
  signOut: vi.fn(),
};

vi.mock('../../config/firebase', () => ({
  db: mockFirestore,
  auth: mockAuth,
  storage: {},
}));

// Import services after mocking
import { DocumentService } from '@/services/documentService';
import { promptService } from '@/services/firestore';
import { workspaceService } from '@/services/workspaceService';

describe('Service Layer Integration Tests', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    
    // Setup default mock responses
    mockFirestore.getDoc.mockResolvedValue({
      exists: () => true,
      data: () => ({ id: 'test-id', title: 'Test Document' }),
      id: 'test-id',
    });

    mockFirestore.getDocs.mockResolvedValue({
      docs: [
        {
          id: 'doc-1',
          data: () => ({ title: 'Document 1', content: 'Content 1' }),
        },
        {
          id: 'doc-2',
          data: () => ({ title: 'Document 2', content: 'Content 2' }),
        },
      ],
    });

    mockFirestore.addDoc.mockResolvedValue({ id: 'new-doc-id' });
    mockFirestore.updateDoc.mockResolvedValue(undefined);
    mockFirestore.deleteDoc.mockResolvedValue(undefined);
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  describe('Document and Prompt Service Integration', () => {
    it('should create document and associated prompts', async () => {
      // Create a document
      const documentData = {
        title: 'Integration Test Document',
        content: 'Test content for integration',
        userId: 'test-user-id',
      };

      const document = await DocumentService.createDocument(documentData);
      expect(document).toBeDefined();
      expect(mockFirestore.addDoc).toHaveBeenCalled();

      // Create prompts based on the document
      const promptData = {
        title: 'Document-based Prompt',
        content: 'Prompt based on document content',
        documentId: document.id,
        userId: 'test-user-id',
      };

      const prompt = await promptService.createPrompt(promptData);
      expect(prompt).toBeDefined();
      expect(mockFirestore.addDoc).toHaveBeenCalledTimes(2);
    });

    it('should handle document deletion with associated prompts', async () => {
      const documentId = 'test-doc-id';

      // Mock prompts associated with document
      mockFirestore.getDocs.mockResolvedValueOnce({
        docs: [
          {
            id: 'prompt-1',
            data: () => ({ documentId, title: 'Associated Prompt' }),
          },
        ],
      });

      // Delete document (should also handle associated prompts)
      await DocumentService.deleteDocument(documentId);
      
      expect(mockFirestore.deleteDoc).toHaveBeenCalled();
    });
  });

  describe('Workspace and Permission Integration', () => {
    it('should create workspace with proper permissions', async () => {
      const workspaceData = {
        name: 'Test Workspace',
        description: 'Integration test workspace',
        ownerId: 'test-user-id',
      };

      // Mock workspace creation
      mockFirestore.addDoc.mockResolvedValueOnce({ id: 'workspace-id' });

      const workspace = await workspaceService.createWorkspace(workspaceData);
      expect(workspace).toBeDefined();
      expect(mockFirestore.addDoc).toHaveBeenCalled();
    });

    it('should handle workspace member management', async () => {
      const workspaceId = 'test-workspace-id';
      const memberId = 'new-member-id';

      // Add member to workspace
      await workspaceService.addMember(workspaceId, memberId, 'editor');
      expect(mockFirestore.updateDoc).toHaveBeenCalled();

      // Remove member from workspace
      await workspaceService.removeMember(workspaceId, memberId);
      expect(mockFirestore.updateDoc).toHaveBeenCalledTimes(2);
    });
  });

  describe('Cross-Service Data Consistency', () => {
    it('should maintain data consistency across services', async () => {
      const userId = 'test-user-id';

      // Get user documents
      const documents = await DocumentService.getUserDocuments(userId);
      expect(documents).toBeDefined();
      expect(Array.isArray(documents)).toBe(true);

      // Get user prompts
      const prompts = await promptService.getUserPrompts(userId);
      expect(prompts).toBeDefined();
      expect(Array.isArray(prompts)).toBe(true);

      // Verify both services were called
      expect(mockFirestore.getDocs).toHaveBeenCalledTimes(2);
    });

    it('should handle concurrent service operations', async () => {
      const userId = 'test-user-id';

      // Execute multiple service operations concurrently
      const operations = [
        DocumentService.getUserDocuments(userId),
        promptService.getUserPrompts(userId),
        workspaceService.getUserWorkspaces(userId),
      ];

      const results = await Promise.all(operations);
      
      // Verify all operations completed
      expect(results).toHaveLength(3);
      results.forEach(result => {
        expect(result).toBeDefined();
      });
    });
  });

  describe('Error Handling Integration', () => {
    it('should handle Firebase errors consistently', async () => {
      // Mock Firebase error
      const firebaseError = new Error('Firebase error');
      firebaseError.name = 'FirebaseError';
      mockFirestore.getDoc.mockRejectedValue(firebaseError);

      // Test error handling in document service
      await expect(DocumentService.getDocument('invalid-id')).rejects.toThrow();

      // Test error handling in prompt service
      await expect(promptService.getPrompt('invalid-id')).rejects.toThrow();
    });

    it('should handle network errors gracefully', async () => {
      // Mock network error
      const networkError = new Error('Network error');
      mockFirestore.getDocs.mockRejectedValue(networkError);

      // Test network error handling
      await expect(DocumentService.getUserDocuments('user-id')).rejects.toThrow();
    });

    it('should handle authentication errors', async () => {
      // Mock auth error
      mockAuth.currentUser = null;

      // Test auth error handling
      await expect(DocumentService.getUserDocuments('user-id')).rejects.toThrow();
    });
  });

  describe('Caching Integration', () => {
    it('should handle cache invalidation across services', async () => {
// const userId = 'test-user-id'; // Unused assignment
      const documentId = 'test-doc-id';

      // First call should hit the database
      await DocumentService.getDocument(documentId);
      expect(mockFirestore.getDoc).toHaveBeenCalledTimes(1);

      // Update document (should invalidate cache)
      await DocumentService.updateDocument(documentId, { title: 'Updated Title' });
      expect(mockFirestore.updateDoc).toHaveBeenCalled();

      // Next call should hit database again due to cache invalidation
      await DocumentService.getDocument(documentId);
      expect(mockFirestore.getDoc).toHaveBeenCalledTimes(2);
    });

    it('should handle cache consistency across related data', async () => {
      const userId = 'test-user-id';

      // Load user data
      await DocumentService.getUserDocuments(userId);
      await promptService.getUserPrompts(userId);

      // Verify caching behavior
      expect(mockFirestore.getDocs).toHaveBeenCalledTimes(2);
    });
  });

  describe('Performance Integration', () => {
    it('should handle batch operations efficiently', async () => {
      const batchData = Array.from({ length: 10 }, (_, i) => ({
        title: `Batch Document ${i}`,
        content: `Content ${i}`,
        userId: 'test-user-id',
      }));

      // Mock batch responses
      mockFirestore.addDoc.mockImplementation(() => 
        Promise.resolve({ id: `batch-doc-${Date.now()}` })
      );

      // Execute batch operations
      const promises = batchData.map(data => DocumentService.createDocument(data));
      const results = await Promise.all(promises);

      // Verify batch processing
      expect(results).toHaveLength(10);
      expect(mockFirestore.addDoc).toHaveBeenCalledTimes(10);
    });

    it('should handle large dataset queries', async () => {
      // Mock large dataset
      const largeDocs = Array.from({ length: 100 }, (_, i) => ({
        id: `doc-${i}`,
        data: () => ({ title: `Document ${i}`, content: `Content ${i}` }),
      }));

      mockFirestore.getDocs.mockResolvedValueOnce({ docs: largeDocs });

      // Query large dataset
      const documents = await DocumentService.getUserDocuments('test-user-id');
      
      // Verify handling of large datasets
      expect(documents).toHaveLength(100);
      expect(mockFirestore.getDocs).toHaveBeenCalled();
    });
  });

  describe('Real-time Updates Integration', () => {
    it('should handle real-time document updates', async () => {
      const documentId = 'test-doc-id';
      const updateCallback = vi.fn();

      // Mock real-time subscription
      const unsubscribe = vi.fn();
      mockFirestore.doc.mockReturnValue({
        onSnapshot: vi.fn((callback) => {
          // Simulate real-time update
          setTimeout(() => {
            callback({
              exists: () => true,
              data: () => ({ title: 'Updated Title', content: 'Updated Content' }),
              id: documentId,
            });
          }, 100);
          return unsubscribe;
        }),
      });

      // Subscribe to document updates
      DocumentService.subscribeToDocument(documentId, updateCallback);

      // Wait for update
      await new Promise(resolve => setTimeout(resolve, 150));

      // Verify real-time update handling
      expect(updateCallback).toHaveBeenCalled();
    });

    it('should handle real-time workspace updates', async () => {
      const workspaceId = 'test-workspace-id';
      const updateCallback = vi.fn();

      // Mock workspace subscription
      const unsubscribe = vi.fn();
      mockFirestore.doc.mockReturnValue({
        onSnapshot: vi.fn((callback) => {
          callback({
            exists: () => true,
            data: () => ({ name: 'Updated Workspace', members: ['user1', 'user2'] }),
            id: workspaceId,
          });
          return unsubscribe;
        }),
      });

      // Subscribe to workspace updates
      workspaceService.subscribeToWorkspace(workspaceId, updateCallback);

      // Verify subscription setup
      expect(mockFirestore.doc).toHaveBeenCalled();
    });
  });
});
