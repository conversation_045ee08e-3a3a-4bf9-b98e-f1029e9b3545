/**
 * Document Service for RAG Document Management
 * Refactored to use unified service architecture with standardized error handling and caching
 */

import {
  collection,
  doc,
  getDocs,
  getDoc,
  query,
  where,
  orderBy,
  onSnapshot,
  deleteDoc,
  updateDoc,
  serverTimestamp,
} from 'firebase/firestore';
import { ref, deleteObject } from 'firebase/storage';
import { db, storage } from '@/config/firebase';
import { requestBatcher} from '@/utils/requestBatcher';
import type { BaseService,
  ServiceOperation,
  ServiceResponse,
  ServiceConfig,
  ServiceDependencies
 } from './baseService';
import type { CRUDService, SearchService } from './serviceInterfaces';
import type { DocumentService as IDocumentService } from './serviceInterfaces';
import { serviceFactory } from './serviceFactory';

import type { RAGDocument } from '@/types';

// Document-specific operations
class GetUserDocumentsOperation implements ServiceOperation<RAGDocument[]> {
  constructor(private userId: string) {}

  async execute(): Promise<RAGDocument[]> {
    const q = query(
      collection(db, 'rag_documents'),
      where('uploadedBy', '==', this.userId),
      orderBy('uploadedAt', 'desc')
    );

    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    } as RAGDocument));
  }

  getCacheKey(): string {
    return `user-documents:${this.userId}`;
  }

  getContext(): string {
    return `getUserDocuments:${this.userId}`;
  }

  validate(): boolean {
    return !!this.userId && typeof this.userId === 'string';
  }
}

class GetDocumentOperation implements ServiceOperation<RAGDocument | null> {
  constructor(private documentId: string) {}

  async execute(): Promise<RAGDocument | null> {
    const docRef = doc(db, 'rag_documents', this.documentId);
    const docSnap = await getDoc(docRef);

    if (docSnap.exists()) {
      return {
        id: docSnap.id,
        ...docSnap.data()
      } as RAGDocument;
    }

    return null;
  }

  getCacheKey(): string {
    return `document:${this.documentId}`;
  }

  getContext(): string {
    return `getDocument:${this.documentId}`;
  }

  validate(): boolean {
    return !!this.documentId && typeof this.documentId === 'string';
  }
}

class UpdateDocumentStatusOperation implements ServiceOperation<void> {
  constructor(
    private documentId: string,
    private status: RAGDocument['status'],
    private error?: string
  ) {}

  async execute(): Promise<void> {
    const updateData: any = {
      status: this.status,
      updatedAt: serverTimestamp()
    };

    if (this.error) {
      updateData.error = this.error;
    }

    if (this.status === 'processing') {
      updateData.processingStartedAt = serverTimestamp();
    } else if (this.status === 'completed') {
      updateData.processedAt = serverTimestamp();
    }

    await updateDoc(doc(db, 'rag_documents', this.documentId), updateData);
  }

  getContext(): string {
    return `updateDocumentStatus:${this.documentId}:${this.status}`;
  }

  validate(): boolean {
    return !!this.documentId && !!this.status;
  }
}

export class DocumentService extends BaseService implements
  CRUDService<RAGDocument, Partial<RAGDocument>, Partial<RAGDocument>>,
  IDocumentService<RAGDocument>,
  SearchService<RAGDocument> {

  constructor(
    config?: ServiceConfig,
    dependencies?: ServiceDependencies
  ) {
    super('DocumentService', config, dependencies);
  }

  /**
   * Get all documents for a user with unified error handling and caching
   */
  async getUserDocuments(userId: string): Promise<ServiceResponse<RAGDocument[]>> {
    const operation = new GetUserDocumentsOperation(userId);
    return this.executeOperation(operation);
  }

  /**
   * Static method for backward compatibility
   */
  static async getUserDocuments(userId: string): Promise<RAGDocument[]> {
    const service = serviceFactory.createService(DocumentService, 'DocumentService');
    const response = await service.getUserDocuments(userId);

    if (!response.success) {
      throw response.error;
    }

    return response.data;
  }

  /**
   * Get multiple documents by IDs using batching
   */
  static async getDocumentsBatch(documentIds: string[]): Promise<RAGDocument[]> {
    if (documentIds.length === 0) return [];

    // Check cache first
    const cached: RAGDocument[] = [];
    const uncachedIds: string[] = [];

    documentIds.forEach(id => {
      const cacheKey = `document:${id}`;
      const cachedDoc = this.getFromCache(cacheKey);
      if (cachedDoc) {
        cached.push(cachedDoc);
      } else {
        uncachedIds.push(id);
      }
    });

    // Fetch uncached documents
    if (uncachedIds.length > 0) {
      const batchRequests = uncachedIds.map(id => ({
        endpoint: `/documents/${id}`,
        method: 'GET' as const
      }));

      try {
        const results = await Promise.all(
          batchRequests.map(req =>
            requestBatcher.batchRequest<RAGDocument>(req.endpoint, req.method)
          )
        );

        // Cache the results
        results.forEach((doc, index) => {
          if (doc) {
            const cacheKey = `document:${uncachedIds[index]}`;
            this.setCache(cacheKey, doc);
            cached.push(doc);
          }
        });
      } catch (error) {
        console.error('Error fetching documents batch:', error);
        // Fallback to individual requests
        const fallbackResults = await Promise.allSettled(
          uncachedIds.map(id => this.getDocument(id))
        );

        fallbackResults.forEach((result, _index) => {
          if (result.status === 'fulfilled' && result.value) {
            cached.push(result.value);
          }
        });
      }
    }

    return cached;
  }

  /**
   * Get a specific document by ID with unified error handling and caching
   */
  async getDocument(documentId: string): Promise<ServiceResponse<RAGDocument | null>> {
    const operation = new GetDocumentOperation(documentId);
    return this.executeOperation(operation);
  }

  /**
   * Static method for backward compatibility
   */
  static async getDocument(documentId: string): Promise<RAGDocument | null> {
    const service = serviceFactory.createService(DocumentService, 'DocumentService');
    const response = await service.getDocument(documentId);

    if (!response.success) {
      throw response.error;
    }

    return response.data;
  }





  /**
   * Subscribe to a specific document's updates
   */
  static subscribeToDocument(
    documentId: string,
    callback: (document: RAGDocument | null) => void
  ): () => void {
    const docRef = doc(db, 'rag_documents', documentId);
    
    return onSnapshot(docRef, (docSnap) => {
      if (docSnap.exists()) {
        callback({
          id: docSnap.id,
          ...docSnap.data()
        } as RAGDocument);
      } else {
        callback(null);
      }
    }, (error) => {
      console.error('Error in document subscription:', error);
    });
  }

  /**
   * Delete a document and its associated files
   */
  static async deleteDocument(documentId: string): Promise<void> {
    try {
      // Get document data first
      const document = await this.getDocument(documentId);
      if (!document) {
        throw new Error('Document not found');
      }

      // Delete file from Storage
      if (document.filePath) {
        const fileRef = ref(storage, document.filePath);
        try {
          await deleteObject(fileRef);
        } catch (storageError) {
          console.warn('File may already be deleted from storage:', storageError);
        }
      }

      // Delete document from Firestore (this will also trigger cleanup of chunks)
      await deleteDoc(doc(db, 'rag_documents', documentId));
      
    } catch (error) {
      console.error('Error deleting document:', error);
      throw error;
    }
  }

  /**
   * Update document status with unified error handling
   */
  async updateDocumentStatus(
    documentId: string,
    status: RAGDocument['status'],
    error?: string
  ): Promise<ServiceResponse<void>> {
    const operation = new UpdateDocumentStatusOperation(documentId, status, error);
    return this.executeOperation(operation);
  }

  /**
   * Static method for backward compatibility
   */
  static async updateDocumentStatus(
    documentId: string,
    status: RAGDocument['status'],
    error?: string
  ): Promise<void> {
    const service = serviceFactory.createService(DocumentService, 'DocumentService');
    const response = await service.updateDocumentStatus(documentId, status, error);

    if (!response.success) {
      throw response.error;
    }
  }

  /**
   * Get document chunks
   */
  static async getDocumentChunks(documentId: string): Promise<any[]> {
    try {
      const chunksRef = collection(db, 'rag_documents', documentId, 'chunks');
      const querySnapshot = await getDocs(chunksRef);
      
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
      
    } catch (error) {
      console.error('Error fetching document chunks:', error);
      throw error;
    }
  }

  /**
   * Get processing statistics for user's documents
   */
  static async getProcessingStats(userId: string): Promise<{
    total: number;
    completed: number;
    processing: number;
    failed: number;
    totalSize: number;
    totalChunks: number;
  }> {
    try {
      const documents = await this.getUserDocuments(userId);
      
      const stats = documents.reduce((acc, doc) => {
        acc.total++;
        acc.totalSize += doc.size;
        
        if (doc.status === 'completed') {
          acc.completed++;
          acc.totalChunks += doc.metadata.chunk_count || 0;
        } else if (doc.status === 'processing') {
          acc.processing++;
        } else if (doc.status === 'failed') {
          acc.failed++;
        }
        
        return acc;
      }, {
        total: 0,
        completed: 0,
        processing: 0,
        failed: 0,
        totalSize: 0,
        totalChunks: 0
      });
      
      return stats;
      
    } catch (error) {
      console.error('Error calculating processing stats:', error);
      throw error;
    }
  }

  /**
   * Search documents by filename
   */
  static async searchDocuments(userId: string, searchTerm: string): Promise<RAGDocument[]> {
    try {
      const documents = await this.getUserDocuments(userId);
      
      return documents.filter(doc => 
        doc.filename.toLowerCase().includes(searchTerm.toLowerCase()) ||
        doc.originalName.toLowerCase().includes(searchTerm.toLowerCase())
      );
      
    } catch (error) {
      console.error('Error searching documents:', error);
      throw error;
    }
  }

  /**
   * Get documents by status
   */
  static async getDocumentsByStatus(
    userId: string, 
    status: RAGDocument['status']
  ): Promise<RAGDocument[]> {
    try {
      const q = query(
        collection(db, 'rag_documents'),
        where('uploadedBy', '==', userId),
        where('status', '==', status),
        orderBy('uploadedAt', 'desc')
      );
      
      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as RAGDocument));
      
    } catch (error) {
      console.error('Error fetching documents by status:', error);
      throw error;
    }
  }

  /**
   * Format file size for display
   */
  static formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * Get file type icon
   */
  static getFileTypeIcon(contentType: string): string {
    if (contentType.includes('pdf')) return '📄';
    if (contentType.includes('word') || contentType.includes('document')) return '📝';
    if (contentType.includes('text')) return '📃';
    if (contentType.includes('markdown')) return '📋';
    return '📄';
  }

  // CRUD Interface Implementation
  async create(_input: Partial<RAGDocument>): Promise<ServiceResponse<RAGDocument>> {
    // Implementation would go here for creating new documents
    throw new Error('Create operation not implemented yet');
  }

  async read(id: string): Promise<ServiceResponse<RAGDocument | null>> {
    return this.getDocument(id);
  }

  async update(_id: string, _input: Partial<RAGDocument>): Promise<ServiceResponse<RAGDocument>> {
    // Implementation would go here for updating documents
    throw new Error('Update operation not implemented yet');
  }

  async delete(_id: string): Promise<ServiceResponse<boolean>> {
    // Implementation would go here for deleting documents
    throw new Error('Delete operation not implemented yet');
  }

  async list(options?: {
    filters?: Record<string, any>;
    pagination?: { page: number; limit: number };
    sorting?: { field: string; direction: 'asc' | 'desc' };
  }): Promise<ServiceResponse<RAGDocument[]>> {
    // For now, return all documents for the current user
    const userId = options?.filters?.userId;
    if (!userId) {
      throw new Error('userId filter is required for listing documents');
    }
    return this.getUserDocuments(userId);
  }

  // Document Service Interface Implementation
  async upload(_file: File, _metadata?: Record<string, any>): Promise<ServiceResponse<RAGDocument>> {
    throw new Error('Upload operation not implemented yet');
  }

  async download(_id: string): Promise<ServiceResponse<Blob>> {
    throw new Error('Download operation not implemented yet');
  }

  async getMetadata(id: string): Promise<ServiceResponse<RAGDocument>> {
    const response = await this.getDocument(id);
    if (!response.success || !response.data) {
      return {
        ...response,
        data: null as any
      };
    }
    return response as ServiceResponse<RAGDocument>;
  }

  async updateMetadata(id: string, metadata: Partial<RAGDocument>): Promise<ServiceResponse<RAGDocument>> {
    return this.update(id, metadata);
  }

  // Search Service Interface Implementation
  async search(query: string, options?: {
    filters?: Record<string, any>;
    fuzzy?: boolean;
    limit?: number;
    offset?: number;
  }): Promise<ServiceResponse<RAGDocument[]>> {
    throw new Error('Search operation not implemented yet');
  }

  async searchByField(field: string, value: any, options?: {
    exact?: boolean;
    limit?: number;
  }): Promise<ServiceResponse<RAGDocument[]>> {
    throw new Error('SearchByField operation not implemented yet');
  }

  /**
   * Subscribe to real-time document updates
   */
  subscribeToUserDocuments(
    userId: string,
    callback: (documents: RAGDocument[]) => void
  ): () => void {
    const q = query(
      collection(db, 'rag_documents'),
      where('uploadedBy', '==', userId),
      orderBy('uploadedAt', 'desc')
    );

    return onSnapshot(q, (snapshot) => {
      const documents = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as RAGDocument));
      callback(documents);
    });
  }

  /**
   * Static method for backward compatibility
   */
  static subscribeToUserDocuments(
    userId: string,
    callback: (documents: RAGDocument[]) => void
  ): () => void {
    const service = serviceFactory.createService(DocumentService, 'DocumentService');
    return service.subscribeToUserDocuments(userId, callback);
  }
}
