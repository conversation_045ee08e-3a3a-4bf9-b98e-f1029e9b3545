<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="RAG Prompt Library - Smart, modular, RAG-enabled prompt management system for AI workflows" />
    <meta name="keywords" content="AI, prompts, RAG, retrieval augmented generation, prompt library, AI tools" />
    <meta name="author" content="RAG Prompt Library" />

    <!-- Security Headers (Note: CSP and X-Frame-Options are set via HTTP headers in Firebase hosting) -->
    <meta http-equiv="X-Content-Type-Options" content="nosniff" />
    <meta http-equiv="Referrer-Policy" content="strict-origin-when-cross-origin" />
    <meta http-equiv="Permissions-Policy" content="camera=(), microphone=(), geolocation=()" />

    <!-- Performance optimizations -->
    <link rel="dns-prefetch" href="//fonts.googleapis.com" />
    <link rel="dns-prefetch" href="//firebaseapp.com" />
    <link rel="dns-prefetch" href="//openrouter.ai" />
    <link rel="preconnect" href="//fonts.gstatic.com" crossorigin />

    <!-- Critical resource preloading -->
    <!-- Main CSS will be preloaded by Vite plugin -->

    <!-- Critical fonts preloading with optimized loading strategy -->
    <!-- Preload the most critical font weights first -->
    <link rel="preload" href="https://fonts.gstatic.com/s/inter/v13/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuLyfAZ9hiA.woff2" as="font" type="font/woff2" crossorigin />
    <link rel="preload" href="https://fonts.gstatic.com/s/inter/v13/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuI6fAZ9hiA.woff2" as="font" type="font/woff2" crossorigin />

    <!-- Load font CSS with optimized display strategy -->
    <link rel="preload" href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" as="style" onload="this.onload=null;this.rel='stylesheet'" />
    <noscript><link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" /></noscript>

    <!-- Inline critical font-face declarations to prevent FOIT -->
    <style>
      @font-face {
        font-family: 'Inter';
        font-style: normal;
        font-weight: 400;
        font-display: swap;
        src: url('https://fonts.gstatic.com/s/inter/v13/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuLyfAZ9hiA.woff2') format('woff2');
        unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
      }
      @font-face {
        font-family: 'Inter';
        font-style: normal;
        font-weight: 600;
        font-display: swap;
        src: url('https://fonts.gstatic.com/s/inter/v13/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuI6fAZ9hiA.woff2') format('woff2');
        unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
      }
    </style>

    <!-- Critical CSS for above-the-fold content -->
    <style>
      /* Critical layout styles to prevent CLS */
      .min-h-screen { min-height: 100vh; }
      .bg-gradient-to-br { background-image: linear-gradient(to bottom right, var(--tw-gradient-stops)); }
      .from-gray-900 { --tw-gradient-from: #111827; --tw-gradient-to: rgba(17, 24, 39, 0); --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to); }
      .to-gray-800 { --tw-gradient-to: #1f2937; }
      .flex { display: flex; }
      .items-center { align-items: center; }
      .justify-center { justify-content: center; }
      .text-center { text-align: center; }
      .text-white { color: #ffffff; }
      .text-4xl { font-size: 2.25rem; line-height: 2.5rem; }
      .text-5xl { font-size: 3rem; line-height: 1; }
      .font-bold { font-weight: 700; }
      .mb-6 { margin-bottom: 1.5rem; }
      .p-4 { padding: 1rem; }

      /* Critical responsive styles */
      @media (min-width: 1024px) {
        .lg\\:text-5xl { font-size: 3rem; line-height: 1; }
        .lg\\:text-left { text-align: left; }
      }

      /* Prevent layout shift for loading states */
      .h-screen { height: 100vh; }
      .overflow-hidden { overflow: hidden; }
      .bg-gray-100 { background-color: #f3f4f6; }
      .bg-gray-900 { background-color: #111827; }
      .dark\\:bg-gray-900 { background-color: #111827; }

      /* Critical icon and button styles */
      .w-12 { width: 3rem; }
      .h-12 { height: 3rem; }
      .text-blue-400 { color: #60a5fa; }
      .mr-3 { margin-right: 0.75rem; }
    </style>

    <!-- Preload critical JavaScript modules -->
    <!-- These will be automatically added by the Vite plugin -->

    <!-- Theme and styling -->
    <meta name="theme-color" content="#3b82f6" />
    <meta name="color-scheme" content="light dark" />

    <title>RAG Prompt Library - Smart Prompt Management</title>
  </head>
  <body>
    <div id="root">
      <!-- Loading fallback -->
      <div style="display: flex; align-items: center; justify-content: center; height: 100vh; font-family: system-ui, sans-serif;">
        <div style="text-align: center;">
          <div style="width: 40px; height: 40px; border: 4px solid #f3f4f6; border-top: 4px solid #3b82f6; border-radius: 50%; animation: spin 1s linear infinite; margin: 0 auto 16px;"></div>
          <p style="color: #6b7280; margin: 0;">Loading RAG Prompt Library...</p>
        </div>
      </div>
      <style>
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      </style>
    </div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
