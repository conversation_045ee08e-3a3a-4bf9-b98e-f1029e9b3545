/**
 * Enhanced Button Component with Comprehensive Prop Validation
 * Demonstrates best practices for prop validation and TypeScript integration
 */

import React, { forwardRef } from 'react';
import type { EnhancedButtonProps } from '@/components/common/types';
import { withPropValidation, createPropSchema } from '@/utils/propValidation';

// Prop validation schema for the Button component
const buttonPropSchema = createPropSchema({
  variant: {
    type: 'string',
    oneOf: ['primary', 'secondary', 'danger', 'outline', 'ghost'],
    required: false
  },
  size: {
    type: 'string',
    oneOf: ['sm', 'md', 'lg'],
    required: false
  },
  fullWidth: {
    type: 'boolean',
    required: false
  },
  loading: {
    type: 'boolean',
    required: false
  },
  disabled: {
    type: 'boolean',
    required: false
  },
  type: {
    type: 'string',
    oneOf: ['button', 'submit', 'reset'],
    required: false
  },
  iconPosition: {
    type: 'string',
    oneOf: ['left', 'right'],
    required: false
  },
  children: {
    required: true,
    custom: (value) => {
      if (!value && value !== 0) {
        return 'Button must have content (children)';
      }
      return null;
    }
  },
  onClick: {
    type: 'function',
    required: false
  },
  onFocus: {
    type: 'function',
    required: false
  },
  onBlur: {
    type: 'function',
    required: false
  },
  ariaLabel: {
    type: 'string',
    required: false,
    custom: (value, props) => {
      // Require aria-label if children is not a string
      if (!value && typeof (props as any).children !== 'string') {
        return 'aria-label is required when children is not a string';
      }
      return null;
    }
  },
  tabIndex: {
    type: 'number',
    min: -1,
    required: false
  }
});

// Base Button component without validation
const BaseButton = forwardRef<HTMLButtonElement, EnhancedButtonProps>(
  ({
    variant = 'primary',
    size = 'md',
    fullWidth = false,
    loading = false,
    disabled = false,
    type = 'button',
    icon,
    iconPosition = 'left',
    children,
    onClick,
    onFocus,
    onBlur,
    className = '',
    'data-testid': dataTestId,
    id,
    ariaLabel,
    ariaDescribedBy,
    tabIndex
  }, ref) => {
    // Variant styles
    const variantStyles = {
      primary: 'bg-blue-600 hover:bg-blue-700 text-white border-transparent',
      secondary: 'bg-gray-600 hover:bg-gray-700 text-white border-transparent',
      danger: 'bg-red-600 hover:bg-red-700 text-white border-transparent',
      outline: 'bg-transparent hover:bg-gray-50 text-gray-700 border-gray-300',
      ghost: 'bg-transparent hover:bg-gray-100 text-gray-700 border-transparent'
    };

    // Size styles
    const sizeStyles = {
      sm: 'px-3 py-1.5 text-sm',
      md: 'px-4 py-2 text-base',
      lg: 'px-6 py-3 text-lg'
    };

    // Disabled styles
    const disabledStyles = disabled || loading
      ? 'opacity-50 cursor-not-allowed'
      : 'cursor-pointer';

    // Loading styles
    const loadingStyles = loading
      ? 'relative'
      : '';

    // Full width styles
    const widthStyles = fullWidth ? 'w-full' : '';

    const baseClasses = [
      'inline-flex items-center justify-center',
      'border rounded-md font-medium',
      'focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500',
      'transition-colors duration-200',
      'disabled:opacity-50 disabled:cursor-not-allowed',
      variantStyles[variant],
      sizeStyles[size],
      disabledStyles,
      loadingStyles,
      widthStyles,
      className
    ].filter(Boolean).join(' ');

    const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
      if (disabled || loading) {
        event.preventDefault();
        return;
      }
      onClick?.(event);
    };

    const renderIcon = () => {
      if (!icon) return null;
      
      const iconClasses = children ? (iconPosition === 'left' ? 'mr-2' : 'ml-2') : '';
      
      return (
        <span className={iconClasses}>
          {icon}
        </span>
      );
    };

    const renderContent = () => {
      if (loading) {
        return (
          <>
            <svg
              className="animate-spin -ml-1 mr-2 h-4 w-4 text-current"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              aria-hidden="true"
            >
              <circle
                className="opacity-25"
                cx="12"
                cy="12"
                r="10"
                stroke="currentColor"
                strokeWidth="4"
              />
              <path
                className="opacity-75"
                fill="currentColor"
                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
              />
            </svg>
            Loading...
          </>
        );
      }

      return (
        <>
          {iconPosition === 'left' && renderIcon()}
          {children}
          {iconPosition === 'right' && renderIcon()}
        </>
      );
    };

    return (
      <button
        ref={ref}
        type={type}
        className={baseClasses}
        onClick={handleClick}
        onFocus={onFocus}
        onBlur={onBlur}
        disabled={disabled || loading}
        data-testid={dataTestId}
        id={id}
        aria-label={ariaLabel}
        aria-describedby={ariaDescribedBy}
        aria-disabled={disabled || loading}
        aria-busy={loading}
        tabIndex={tabIndex}
      >
        {renderContent()}
      </button>
    );
  }
);

BaseButton.displayName = 'BaseButton';

// Enhanced Button with prop validation
export const EnhancedButton = withPropValidation(
  BaseButton,
  buttonPropSchema,
  {
    displayName: 'EnhancedButton',
    throwOnError: false,
    logWarnings: true
  }
);

// Export both versions for flexibility
export { BaseButton };
export default EnhancedButton;

// Example usage with proper typing:
/*
// ✅ Valid usage
<EnhancedButton variant="primary" size="md" onClick={handleClick}>
  Click me
</EnhancedButton>

// ✅ Valid with icon
<EnhancedButton 
  variant="outline" 
  icon={<PlusIcon className="h-4 w-4" />}
  iconPosition="left"
>
  Add Item
</EnhancedButton>

// ❌ Invalid - will show validation error
<EnhancedButton variant="invalid-variant">
  Invalid Button
</EnhancedButton>

// ❌ Invalid - missing aria-label for non-string children
<EnhancedButton>
  <SomeIcon />
</EnhancedButton>

// ✅ Valid - with proper aria-label for icon-only button
<EnhancedButton ariaLabel="Close dialog">
  <XIcon className="h-4 w-4" />
</EnhancedButton>
*/
