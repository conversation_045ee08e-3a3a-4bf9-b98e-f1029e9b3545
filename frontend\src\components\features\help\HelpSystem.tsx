/**
 * Comprehensive In-App Help System
 * Provides contextual help, tooltips, guided tours, and onboarding
 */

import React, { useState, useEffect, createContext, useContext } from 'react';
import { QuestionMarkCircleIcon, XMarkIcon, ChevronRightIcon, ChevronLeftIcon } from '@heroicons/react/24/outline';

// Help Context for managing help state
interface HelpContextType {
  isHelpMode: boolean;
  toggleHelpMode: () => void;
  showTooltip: (id: string, content: string, position?: TooltipPosition) => void;
  hideTooltip: () => void;
  startTour: (tourId: string) => void;
  currentTour: string | null;
  tourStep: number;
  nextTourStep: () => void;
  previousTourStep: () => void;
  endTour: () => void;
}

const HelpContext = createContext<HelpContextType | null>(null);

export const useHelp = () => {
  const context = useContext(HelpContext);
  if (!context) {
    throw new Error('useHelp must be used within a HelpProvider');
  }
  return context;
};

// Tooltip types and interfaces
type TooltipPosition = 'top' | 'bottom' | 'left' | 'right';

interface TooltipData {
  id: string;
  content: string;
  position: TooltipPosition;
  element?: HTMLElement;
}

interface TourStep {
  id: string;
  title: string;
  content: string;
  target: string;
  position: TooltipPosition;
  action?: () => void;
}

interface Tour {
  id: string;
  title: string;
  description: string;
  steps: TourStep[];
}

// Predefined tours
const TOURS: Record<string, Tour> = {
  'first-time-user': {
    id: 'first-time-user',
    title: 'Welcome to RAG Prompt Library!',
    description: 'Let\'s take a quick tour to get you started',
    steps: [
      {
        id: 'welcome',
        title: 'Welcome!',
        content: 'Welcome to RAG Prompt Library! This tour will show you the key features to get you started quickly.',
        target: 'body',
        position: 'bottom'
      },
      {
        id: 'navigation',
        title: 'Navigation',
        content: 'Use this sidebar to navigate between different sections: Prompts, Documents, Analytics, and Settings.',
        target: '[data-help="main-navigation"]',
        position: 'right'
      },
      {
        id: 'create-prompt',
        title: 'Create Your First Prompt',
        content: 'Click here to create your first prompt. Prompts are reusable templates that can generate dynamic content.',
        target: '[data-help="create-prompt-button"]',
        position: 'bottom'
      },
      {
        id: 'upload-documents',
        title: 'Upload Documents',
        content: 'Upload documents here to enable RAG (Retrieval Augmented Generation) for context-aware AI responses.',
        target: '[data-help="upload-documents"]',
        position: 'bottom'
      },
      {
        id: 'help-center',
        title: 'Need Help?',
        content: 'Click the help icon anytime to access tutorials, documentation, and support. You can also press "?" to toggle help mode.',
        target: '[data-help="help-button"]',
        position: 'left'
      }
    ]
  },
  'prompt-creation': {
    id: 'prompt-creation',
    title: 'Creating Your First Prompt',
    description: 'Learn how to create effective prompts',
    steps: [
      {
        id: 'prompt-title',
        title: 'Prompt Title',
        content: 'Give your prompt a descriptive title that explains what it does.',
        target: '[data-help="prompt-title"]',
        position: 'bottom'
      },
      {
        id: 'prompt-content',
        title: 'Prompt Content',
        content: 'Write your prompt here. Use {{variable_name}} syntax to create dynamic variables.',
        target: '[data-help="prompt-content"]',
        position: 'right'
      },
      {
        id: 'prompt-variables',
        title: 'Variables',
        content: 'Define variables that users can fill in when executing the prompt. This makes prompts reusable.',
        target: '[data-help="prompt-variables"]',
        position: 'left'
      },
      {
        id: 'prompt-settings',
        title: 'Model Settings',
        content: 'Configure which AI model to use and adjust parameters like temperature and max tokens.',
        target: '[data-help="prompt-settings"]',
        position: 'top'
      }
    ]
  },
  'document-upload': {
    id: 'document-upload',
    title: 'Document Upload and RAG',
    description: 'Learn how to upload documents for RAG capabilities',
    steps: [
      {
        id: 'upload-area',
        title: 'Upload Documents',
        content: 'Drag and drop files here or click to browse. Supported formats: PDF, TXT, DOCX, MD.',
        target: '[data-help="upload-area"]',
        position: 'bottom'
      },
      {
        id: 'processing-status',
        title: 'Processing Status',
        content: 'Monitor document processing status here. Documents are automatically chunked and indexed for search.',
        target: '[data-help="processing-status"]',
        position: 'right'
      },
      {
        id: 'document-search',
        title: 'Document Search',
        content: 'Search through your uploaded documents to find relevant information quickly.',
        target: '[data-help="document-search"]',
        position: 'bottom'
      }
    ]
  }
};

// Help Provider Component
export const HelpProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [isHelpMode, setIsHelpMode] = useState(false);
  const [tooltip, setTooltip] = useState<TooltipData | null>(null);
  const [currentTour, setCurrentTour] = useState<string | null>(null);
  const [tourStep, setTourStep] = useState(0);

  // Keyboard shortcut for help mode
  useEffect(() => {
    const handleKeyPress = (event: KeyboardEvent) => {
      if (event.key === '?' && !event.ctrlKey && !event.metaKey) {
        event.preventDefault();
        setIsHelpMode(prev => !prev);
      }
      if (event.key === 'Escape') {
        setIsHelpMode(false);
        setTooltip(null);
        endTour();
      }
    };

    document.addEventListener('keydown', handleKeyPress);
    return () => document.removeEventListener('keydown', handleKeyPress);
  }, []);

  // Check if user is new and should see onboarding
  useEffect(() => {
    const hasSeenOnboarding = localStorage.getItem('hasSeenOnboarding');
    if (!hasSeenOnboarding) {
      setTimeout(() => {
        startTour('first-time-user');
      }, 1000);
    }
  }, []);

  const toggleHelpMode = () => {
    setIsHelpMode(prev => !prev);
    setTooltip(null);
  };

  const showTooltip = (id: string, content: string, position: TooltipPosition = 'top') => {
    setTooltip({ id, content, position });
  };

  const hideTooltip = () => {
    setTooltip(null);
  };

  const startTour = (tourId: string) => {
    setCurrentTour(tourId);
    setTourStep(0);
    setIsHelpMode(false);
  };

  const nextTourStep = () => {
    if (currentTour) {
      const tour = TOURS[currentTour];
      if (tourStep < tour.steps.length - 1) {
        setTourStep(prev => prev + 1);
      } else {
        endTour();
      }
    }
  };

  const previousTourStep = () => {
    if (tourStep > 0) {
      setTourStep(prev => prev - 1);
    }
  };

  const endTour = () => {
    setCurrentTour(null);
    setTourStep(0);
    if (currentTour === 'first-time-user') {
      localStorage.setItem('hasSeenOnboarding', 'true');
    }
  };

  const contextValue: HelpContextType = {
    isHelpMode,
    toggleHelpMode,
    showTooltip,
    hideTooltip,
    startTour,
    currentTour,
    tourStep,
    nextTourStep,
    previousTourStep,
    endTour
  };

  return (
    <HelpContext.Provider value={contextValue}>
      {children}
      <HelpOverlay />
      <TooltipRenderer tooltip={tooltip} />
      <TourRenderer />
    </HelpContext.Provider>
  );
};

// Help Overlay Component
const HelpOverlay: React.FC = () => {
  const { isHelpMode, toggleHelpMode } = useHelp();

  if (!isHelpMode) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center">
      <div className="bg-white rounded-lg p-6 max-w-md mx-4">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold">Help Mode Active</h2>
          <button
            onClick={toggleHelpMode}
            className="text-gray-400 hover:text-gray-600"
          >
            <XMarkIcon className="h-6 w-6" />
          </button>
        </div>
        <p className="text-gray-600 mb-4">
          Click on any element to see helpful information about it. Press ESC or click outside to exit help mode.
        </p>
        <div className="space-y-2">
          <button
            onClick={() => {
              toggleHelpMode();
              setTimeout(() => startTour('first-time-user'), 100);
            }}
            className="w-full text-left p-2 rounded hover:bg-gray-50"
          >
            🎯 Take the Getting Started Tour
          </button>
          <button
            onClick={() => {
              toggleHelpMode();
              setTimeout(() => startTour('prompt-creation'), 100);
            }}
            className="w-full text-left p-2 rounded hover:bg-gray-50"
          >
            ✏️ Learn About Creating Prompts
          </button>
          <button
            onClick={() => {
              toggleHelpMode();
              setTimeout(() => startTour('document-upload'), 100);
            }}
            className="w-full text-left p-2 rounded hover:bg-gray-50"
          >
            📄 Learn About Document Upload
          </button>
        </div>
      </div>
    </div>
  );
};

// Tooltip Renderer Component
const TooltipRenderer: React.FC<{ tooltip: TooltipData | null }> = ({ tooltip }) => {
  if (!tooltip) return null;

  return (
    <div className="fixed z-50 bg-gray-900 text-white text-sm rounded-lg px-3 py-2 max-w-xs">
      {tooltip.content}
      <div className="absolute w-2 h-2 bg-gray-900 transform rotate-45"></div>
    </div>
  );
};

// Tour Renderer Component
const TourRenderer: React.FC = () => {
  const { currentTour, tourStep, nextTourStep, previousTourStep, endTour } = useHelp();

  if (!currentTour) return null;

  const tour = TOURS[currentTour];
  const step = tour.steps[tourStep];

  return (
    <div className="fixed inset-0 z-50">
      {/* Backdrop */}
      <div className="absolute inset-0 bg-black bg-opacity-50" />
      
      {/* Tour Step Content */}
      <div className="absolute bg-white rounded-lg shadow-xl p-6 max-w-sm">
        <div className="mb-4">
          <h3 className="text-lg font-semibold mb-2">{step.title}</h3>
          <p className="text-gray-600">{step.content}</p>
        </div>
        
        <div className="flex items-center justify-between">
          <div className="text-sm text-gray-500">
            Step {tourStep + 1} of {tour.steps.length}
          </div>
          
          <div className="flex space-x-2">
            {tourStep > 0 && (
              <button
                onClick={previousTourStep}
                className="flex items-center px-3 py-1 text-sm border rounded hover:bg-gray-50"
              >
                <ChevronLeftIcon className="h-4 w-4 mr-1" />
                Back
              </button>
            )}
            
            {tourStep < tour.steps.length - 1 ? (
              <button
                onClick={nextTourStep}
                className="flex items-center px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700"
              >
                Next
                <ChevronRightIcon className="h-4 w-4 ml-1" />
              </button>
            ) : (
              <button
                onClick={endTour}
                className="px-3 py-1 text-sm bg-green-600 text-white rounded hover:bg-green-700"
              >
                Finish
              </button>
            )}
          </div>
        </div>
        
        <button
          onClick={endTour}
          className="absolute top-2 right-2 text-gray-400 hover:text-gray-600"
        >
          <XMarkIcon className="h-5 w-5" />
        </button>
      </div>
    </div>
  );
};

// Help Button Component
export const HelpButton: React.FC = () => {
  const { toggleHelpMode } = useHelp();

  return (
    <button
      data-help="help-button"
      onClick={toggleHelpMode}
      className="fixed bottom-4 right-4 bg-blue-600 text-white p-3 rounded-full shadow-lg hover:bg-blue-700 transition-colors z-40"
      title="Help (Press ? for help mode)"
    >
      <QuestionMarkCircleIcon className="h-6 w-6" />
    </button>
  );
};

// Help Trigger Component for specific elements
export const HelpTrigger: React.FC<{
  helpId: string;
  content: string;
  position?: TooltipPosition;
  children: React.ReactNode;
}> = ({ helpId, content, position = 'top', children }) => {
  const { isHelpMode, showTooltip, hideTooltip } = useHelp();

  const handleClick = () => {
    if (isHelpMode) {
      showTooltip(helpId, content, position);
    }
  };

  return (
    <div
      data-help={helpId}
      onClick={handleClick}
      className={isHelpMode ? 'cursor-help ring-2 ring-blue-300 ring-opacity-50' : ''}
    >
      {children}
    </div>
  );
};

// Contextual Help Components
export const ContextualHelp: React.FC<{
  title: string;
  content: string;
  links?: Array<{ text: string; url: string }>;
}> = ({ title, content, links }) => {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className="relative inline-block">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="text-gray-400 hover:text-gray-600 ml-1"
        title="Click for help"
      >
        <QuestionMarkCircleIcon className="h-4 w-4" />
      </button>

      {isOpen && (
        <>
          <div
            className="fixed inset-0 z-10"
            onClick={() => setIsOpen(false)}
          />
          <div className="absolute z-20 w-80 p-4 bg-white border border-gray-200 rounded-lg shadow-lg top-6 right-0">
            <h4 className="font-semibold mb-2">{title}</h4>
            <p className="text-sm text-gray-600 mb-3">{content}</p>
            {links && links.length > 0 && (
              <div className="space-y-1">
                {links.map((link, index) => (
                  <a
                    key={index}
                    href={link.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="block text-sm text-blue-600 hover:text-blue-800"
                  >
                    {link.text} →
                  </a>
                ))}
              </div>
            )}
          </div>
        </>
      )}
    </div>
  );
};

// Smart Tooltip Component
export const SmartTooltip: React.FC<{
  content: string;
  position?: TooltipPosition;
  delay?: number;
  children: React.ReactNode;
}> = ({ content, position = 'top', delay = 500, children }) => {
  const [isVisible, setIsVisible] = useState(false);
  const [timeoutId, setTimeoutId] = useState<NodeJS.Timeout | null>(null);

  const showTooltip = () => {
    const id = setTimeout(() => setIsVisible(true), delay);
    setTimeoutId(id);
  };

  const hideTooltip = () => {
    if (timeoutId) {
      clearTimeout(timeoutId);
      setTimeoutId(null);
    }
    setIsVisible(false);
  };

  return (
    <div
      className="relative inline-block"
      onMouseEnter={showTooltip}
      onMouseLeave={hideTooltip}
    >
      {children}
      {isVisible && (
        <div className={`absolute z-50 px-2 py-1 text-xs text-white bg-gray-900 rounded whitespace-nowrap ${
          position === 'top' ? 'bottom-full mb-1 left-1/2 transform -translate-x-1/2' :
          position === 'bottom' ? 'top-full mt-1 left-1/2 transform -translate-x-1/2' :
          position === 'left' ? 'right-full mr-1 top-1/2 transform -translate-y-1/2' :
          'left-full ml-1 top-1/2 transform -translate-y-1/2'
        }`}>
          {content}
          <div className={`absolute w-1 h-1 bg-gray-900 transform rotate-45 ${
            position === 'top' ? 'top-full left-1/2 -translate-x-1/2 -mt-0.5' :
            position === 'bottom' ? 'bottom-full left-1/2 -translate-x-1/2 -mb-0.5' :
            position === 'left' ? 'left-full top-1/2 -translate-y-1/2 -ml-0.5' :
            'right-full top-1/2 -translate-y-1/2 -mr-0.5'
          }`} />
        </div>
      )}
    </div>
  );
};

// Progress Indicator for Tours
export const TourProgress: React.FC<{
  currentStep: number;
  totalSteps: number;
}> = ({ currentStep, totalSteps }) => {
  return (
    <div className="flex items-center space-x-1">
      {Array.from({ length: totalSteps }, (_, index) => (
        <div
          key={index}
          className={`w-2 h-2 rounded-full ${
            index <= currentStep ? 'bg-blue-600' : 'bg-gray-300'
          }`}
        />
      ))}
    </div>
  );
};

// Main HelpSystem Component
const HelpSystem: React.FC = () => {
  return (
    <>
      <HelpOverlay />
      <TooltipRenderer tooltip={null} />
      <TourRenderer />
    </>
  );
};

export default HelpSystem;
