/**
 * Reusable Checkbox and Radio Components
 * Standardized form controls with consistent styling and behavior
 */

import React, { forwardRef } from 'react';
import { CheckboxProps, RadioProps } from '@/components/common/types';

export const Checkbox = forwardRef<HTMLInputElement, CheckboxProps>(({
  name,
  label,
  checked = false,
  indeterminate = false,
  disabled = false,
  required = false,
  error,
  onChange,
  value,
  className = '',
  'data-testid': testId,
  id,
  ...props
}, ref) => {
  const checkboxId = id || `checkbox-${name || Math.random().toString(36).substr(2, 9)}`;

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (onChange) {
      onChange(e.target.checked);
    }
  };

  // Set indeterminate state
  React.useEffect(() => {
    if (ref && typeof ref === 'object' && ref.current) {
      ref.current.indeterminate = indeterminate;
    }
  }, [indeterminate, ref]);

  const baseClasses = 'h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-600 focus:ring-2 focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 dark:border-gray-600 dark:bg-gray-800 dark:focus:ring-blue-500 dark:focus:ring-offset-gray-900';
  
  const errorClasses = error 
    ? 'border-red-300 text-red-600 focus:ring-red-500 dark:border-red-600 dark:focus:ring-red-500' 
    : '';

  const checkboxClasses = `${baseClasses} ${errorClasses} ${className}`.trim();

  return (
    <div className="flex items-start">
      <div className="flex h-6 items-center">
        <input
          ref={ref}
          type="checkbox"
          id={checkboxId}
          name={name}
          value={value}
          checked={checked}
          onChange={handleChange}
          disabled={disabled}
          required={required}
          className={checkboxClasses}
          data-testid={testId}
          aria-invalid={error ? 'true' : 'false'}
          aria-describedby={error ? `${checkboxId}-error` : undefined}
          {...props}
        />
      </div>
      
      {label && (
        <div className="ml-3 text-sm leading-6">
          <label 
            htmlFor={checkboxId}
            className={`font-medium text-gray-900 dark:text-white ${
              disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'
            } ${required ? "after:content-['*'] after:ml-0.5 after:text-red-500" : ''}`}
          >
            {label}
          </label>
          
          {error && (
            <p 
              id={`${checkboxId}-error`}
              className="mt-1 text-sm text-red-600 dark:text-red-400"
            >
              {error}
            </p>
          )}
        </div>
      )}
    </div>
  );
});

Checkbox.displayName = 'Checkbox';

// Radio Component
export const Radio = forwardRef<HTMLInputElement, RadioProps>(({
  name,
  label,
  value,
  checked = false,
  disabled = false,
  required = false,
  error,
  onChange,
  className = '',
  'data-testid': testId,
  id,
  ...props
}, ref) => {
  const radioId = id || `radio-${name}-${value}`;

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (onChange && e.target.checked) {
      onChange(value);
    }
  };

  const baseClasses = 'h-4 w-4 border-gray-300 text-blue-600 focus:ring-blue-600 focus:ring-2 focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 dark:border-gray-600 dark:bg-gray-800 dark:focus:ring-blue-500 dark:focus:ring-offset-gray-900';
  
  const errorClasses = error 
    ? 'border-red-300 text-red-600 focus:ring-red-500 dark:border-red-600 dark:focus:ring-red-500' 
    : '';

  const radioClasses = `${baseClasses} ${errorClasses} ${className}`.trim();

  return (
    <div className="flex items-start">
      <div className="flex h-6 items-center">
        <input
          ref={ref}
          type="radio"
          id={radioId}
          name={name}
          value={value}
          checked={checked}
          onChange={handleChange}
          disabled={disabled}
          required={required}
          className={radioClasses}
          data-testid={testId}
          aria-invalid={error ? 'true' : 'false'}
          aria-describedby={error ? `${radioId}-error` : undefined}
          {...props}
        />
      </div>
      
      {label && (
        <div className="ml-3 text-sm leading-6">
          <label 
            htmlFor={radioId}
            className={`font-medium text-gray-900 dark:text-white ${
              disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'
            } ${required ? "after:content-['*'] after:ml-0.5 after:text-red-500" : ''}`}
          >
            {label}
          </label>
          
          {error && (
            <p 
              id={`${radioId}-error`}
              className="mt-1 text-sm text-red-600 dark:text-red-400"
            >
              {error}
            </p>
          )}
        </div>
      )}
    </div>
  );
});

Radio.displayName = 'Radio';

// Radio Group Component for managing multiple radio buttons
export interface RadioGroupProps {
  name: string;
  value?: string;
  onChange?: (value: string) => void;
  options: Array<{
    value: string;
    label: string;
    disabled?: boolean;
  }>;
  label?: string;
  required?: boolean;
  error?: string;
  disabled?: boolean;
  direction?: 'horizontal' | 'vertical';
  className?: string;
  'data-testid'?: string;
}

export const RadioGroup: React.FC<RadioGroupProps> = ({
  name,
  value,
  onChange,
  options,
  label,
  required = false,
  error,
  disabled = false,
  direction = 'vertical',
  className = '',
  'data-testid': testId
}) => {
  const groupId = `radio-group-${name}`;

  const containerClasses = direction === 'horizontal' 
    ? 'flex flex-wrap gap-6' 
    : 'space-y-4';

  return (
    <fieldset className={`w-full ${className}`} data-testid={testId}>
      {label && (
        <legend className={`text-sm font-medium leading-6 text-gray-900 dark:text-white mb-4 ${
          required ? "after:content-['*'] after:ml-0.5 after:text-red-500" : ''
        }`}>
          {label}
        </legend>
      )}
      
      <div className={containerClasses} role="radiogroup" aria-labelledby={label ? groupId : undefined}>
        {options.map((option) => (
          <Radio
            key={option.value}
            name={name}
            value={option.value}
            label={option.label}
            checked={value === option.value}
            disabled={disabled || option.disabled}
            required={required}
            error={error}
            onChange={onChange}
          />
        ))}
      </div>
      
      {error && (
        <p className="mt-2 text-sm text-red-600 dark:text-red-400">
          {error}
        </p>
      )}
    </fieldset>
  );
};

// Checkbox Group Component for managing multiple checkboxes
export interface CheckboxGroupProps {
  name: string;
  value?: string[];
  onChange?: (values: string[]) => void;
  options: Array<{
    value: string;
    label: string;
    disabled?: boolean;
  }>;
  label?: string;
  required?: boolean;
  error?: string;
  disabled?: boolean;
  direction?: 'horizontal' | 'vertical';
  className?: string;
  'data-testid'?: string;
}

export const CheckboxGroup: React.FC<CheckboxGroupProps> = ({
  name,
  value = [],
  onChange,
  options,
  label,
  required = false,
  error,
  disabled = false,
  direction = 'vertical',
  className = '',
  'data-testid': testId
}) => {
  const groupId = `checkbox-group-${name}`;

  const handleCheckboxChange = (optionValue: string, checked: boolean) => {
    if (!onChange) return;

    const newValues = checked
      ? [...value, optionValue]
      : value.filter(v => v !== optionValue);
    
    onChange(newValues);
  };

  const containerClasses = direction === 'horizontal' 
    ? 'flex flex-wrap gap-6' 
    : 'space-y-4';

  return (
    <fieldset className={`w-full ${className}`} data-testid={testId}>
      {label && (
        <legend className={`text-sm font-medium leading-6 text-gray-900 dark:text-white mb-4 ${
          required ? "after:content-['*'] after:ml-0.5 after:text-red-500" : ''
        }`}>
          {label}
        </legend>
      )}
      
      <div className={containerClasses} role="group" aria-labelledby={label ? groupId : undefined}>
        {options.map((option) => (
          <Checkbox
            key={option.value}
            name={`${name}[]`}
            value={option.value}
            label={option.label}
            checked={value.includes(option.value)}
            disabled={disabled || option.disabled}
            required={required}
            error={error}
            onChange={(checked) => handleCheckboxChange(option.value, checked)}
          />
        ))}
      </div>
      
      {error && (
        <p className="mt-2 text-sm text-red-600 dark:text-red-400">
          {error}
        </p>
      )}
    </fieldset>
  );
};
