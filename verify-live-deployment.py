#!/usr/bin/env python3
"""
Live Deployment Verification Script
Tests the deployed Firebase Functions to verify AI integration is working
"""

import requests
import json
import time
from datetime import datetime

# Configuration
PROJECT_ID = "rag-prompt-library"
BASE_URL = f"https://australia-southeast1-{PROJECT_ID}.cloudfunctions.net"
WEBSITE_URL = "https://rag-prompt-library.web.app"

def test_function_endpoint(endpoint, data=None, method="GET"):
    """Test a Firebase Function endpoint"""
    url = f"{BASE_URL}/{endpoint}"
    
    try:
        if method == "POST":
            response = requests.post(url, json=data, timeout=30)
        else:
            response = requests.get(url, timeout=30)
        
        return {
            'success': True,
            'status_code': response.status_code,
            'response': response.json() if response.headers.get('content-type', '').startswith('application/json') else response.text,
            'response_time': response.elapsed.total_seconds()
        }
    except Exception as e:
        return {
            'success': False,
            'error': str(e)
        }

def verify_live_deployment():
    """Verify the live deployment is working correctly"""
    print("🌐 Live Deployment Verification")
    print("=" * 50)
    print(f"Website: {WEBSITE_URL}")
    print(f"Functions: {BASE_URL}")
    print(f"Timestamp: {datetime.now().isoformat()}")
    print()
    
    results = {}
    
    # Test 1: Health Check
    print("🏥 Testing Health Check...")
    health_result = test_function_endpoint("health")
    if health_result['success']:
        print(f"✅ Health Check: {health_result['status_code']} - {health_result['response_time']:.2f}s")
        results['health'] = True
    else:
        print(f"❌ Health Check: {health_result['error']}")
        results['health'] = False
    
    # Test 2: OpenRouter Connection Test
    print("\n🔗 Testing OpenRouter Connection...")
    openrouter_result = test_function_endpoint("test_openrouter_connection", method="POST")
    if openrouter_result['success']:
        response = openrouter_result['response']
        if isinstance(response, dict):
            if 'mock' in str(response).lower() or 'migration' in str(response).lower():
                print("❌ OpenRouter Test: Still returning mock responses")
                print(f"   Response: {response}")
                results['openrouter'] = False
            else:
                print("✅ OpenRouter Test: Real API response detected")
                print(f"   Model: {response.get('model', 'Unknown')}")
                print(f"   Tokens: {response.get('tokens', 'Unknown')}")
                print(f"   Time: {openrouter_result['response_time']:.2f}s")
                results['openrouter'] = True
        else:
            print(f"⚠️  OpenRouter Test: Unexpected response format: {response}")
            results['openrouter'] = False
    else:
        print(f"❌ OpenRouter Test: {openrouter_result['error']}")
        results['openrouter'] = False
    
    # Test 3: Prompt Execution
    print("\n📝 Testing Prompt Execution...")
    prompt_data = {
        "prompt": "Hello! Please respond with a brief greeting.",
        "provider": "openrouter",
        "use_rag": False
    }
    
    prompt_result = test_function_endpoint("execute_prompt", prompt_data, method="POST")
    if prompt_result['success']:
        response = prompt_result['response']
        if isinstance(response, dict):
            if 'mock' in str(response).lower() or 'migration' in str(response).lower():
                print("❌ Prompt Execution: Still returning mock responses")
                print(f"   Response: {response}")
                results['prompt_execution'] = False
            else:
                print("✅ Prompt Execution: Real AI response detected")
                print(f"   Response length: {len(str(response.get('response', '')))}")
                print(f"   Model: {response.get('metadata', {}).get('model', 'Unknown')}")
                print(f"   Tokens: {response.get('metadata', {}).get('tokens_used', 'Unknown')}")
                print(f"   Time: {prompt_result['response_time']:.2f}s")
                results['prompt_execution'] = True
        else:
            print(f"⚠️  Prompt Execution: Unexpected response format: {response}")
            results['prompt_execution'] = False
    else:
        print(f"❌ Prompt Execution: {prompt_result['error']}")
        results['prompt_execution'] = False
    
    # Test 4: Website Accessibility
    print("\n🌐 Testing Website Accessibility...")
    try:
        website_response = requests.get(WEBSITE_URL, timeout=10)
        if website_response.status_code == 200:
            print(f"✅ Website: Accessible ({website_response.status_code})")
            results['website'] = True
        else:
            print(f"⚠️  Website: Status {website_response.status_code}")
            results['website'] = False
    except Exception as e:
        print(f"❌ Website: {e}")
        results['website'] = False
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 VERIFICATION SUMMARY")
    print("=" * 50)
    
    passed_tests = sum(1 for result in results.values() if result)
    total_tests = len(results)
    
    for test_name, passed in results.items():
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{test_name.replace('_', ' ').title():.<25} {status}")
    
    print(f"\nOverall Score: {passed_tests}/{total_tests}")
    
    if passed_tests == total_tests:
        print("🎉 All tests passed! AI integration is working correctly.")
        return True
    elif passed_tests >= total_tests * 0.75:
        print("⚠️  Most tests passed, but some issues need attention.")
        return False
    else:
        print("❌ Multiple failures detected. AI integration needs fixing.")
        return False

def print_manual_verification_guide():
    """Print manual verification steps for the user"""
    print("\n" + "=" * 60)
    print("🧪 MANUAL VERIFICATION GUIDE")
    print("=" * 60)
    print(f"Website URL: {WEBSITE_URL}")
    print()
    print("Please manually verify the following:")
    print()
    print("1️⃣ Navigate to Prompt Execution Page:")
    print("   • Go to the website")
    print("   • Find and click on 'Prompt Execution' or similar")
    print("   • Check for any error messages")
    print()
    print("2️⃣ Test OpenRouter Connection:")
    print("   • Click 'Test OpenRouter Connection' button")
    print("   • ✅ Should show: Real model name, token count, response time")
    print("   • ❌ Should NOT show: 'Mock' or 'migration' messages")
    print()
    print("3️⃣ Execute Test Prompt:")
    print("   • Enter: 'Hello! Please respond with a brief greeting.'")
    print("   • Click Execute/Submit")
    print("   • ✅ Should show: AI-generated greeting with metadata")
    print("   • ❌ Should NOT show: Mock responses or identical responses")
    print()
    print("4️⃣ Check Response Metadata:")
    print("   • Look for: Model name, token count, response time")
    print("   • Verify: Numbers are realistic (not 0 or fake)")
    print()
    print("5️⃣ Test Multiple Times:")
    print("   • Execute the same prompt 2-3 times")
    print("   • ✅ Responses should vary slightly")
    print("   • ❌ Identical responses indicate mock data")
    print()
    print("🔍 If you see any issues, please report:")
    print("   • Exact error messages")
    print("   • Screenshots of responses")
    print("   • Browser console errors (F12 → Console)")

if __name__ == "__main__":
    print("🚀 Starting Live Deployment Verification...")
    print()
    
    # Run automated tests
    success = verify_live_deployment()
    
    # Print manual verification guide
    print_manual_verification_guide()
    
    if success:
        print("\n🎉 Automated tests passed! Please complete manual verification.")
    else:
        print("\n⚠️  Some automated tests failed. Manual verification is crucial.")
    
    print(f"\n📅 Verification completed: {datetime.now().isoformat()}")
