#!/usr/bin/env node

/**
 * Async/Await Error Handling Fixer
 * Identifies and fixes async/await error handling issues:
 * 1. useEffect hooks calling async functions without error handling
 * 2. Event handlers with async operations without try-catch
 * 3. Missing await keywords on async operations
 * 4. Unhandled promise rejections
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { glob } from 'glob';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class AsyncAwaitErrorFixer {
  constructor() {
    this.projectRoot = path.resolve(__dirname, '..');
    this.srcDir = path.join(this.projectRoot, 'src');
    this.fixedFiles = [];
    this.issues = [];
    this.fixedCount = 0;
  }

  /**
   * Scan for async/await error handling issues
   */
  async scanForIssues() {
    console.log('🔍 Scanning for async/await error handling issues...\n');

    const patterns = [
      'src/**/*.tsx',
      'src/**/*.ts',
      '!src/**/*.test.ts',
      '!src/**/*.test.tsx',
      '!src/**/__tests__/**/*'
    ];

    const files = await glob(patterns, { cwd: this.projectRoot });
    
    for (const file of files) {
      await this.analyzeFile(file);
    }

    console.log(`📊 Found ${this.issues.length} async/await error handling issues\n`);
    return this.issues;
  }

  /**
   * Analyze a single file for async/await issues
   */
  async analyzeFile(filePath) {
    const fullPath = path.join(this.projectRoot, filePath);
    
    try {
      const content = fs.readFileSync(fullPath, 'utf8');
      const lines = content.split('\n');

      // Check for useEffect with async calls without error handling
      this.checkUseEffectAsyncIssues(filePath, content, lines);
      
      // Check for event handlers with async operations without try-catch
      this.checkEventHandlerAsyncIssues(filePath, content, lines);
      
      // Check for missing await keywords
      this.checkMissingAwaitKeywords(filePath, content, lines);
      
      // Check for unhandled promise rejections
      this.checkUnhandledPromises(filePath, content, lines);

    } catch (error) {
      console.error(`❌ Error analyzing ${filePath}: ${error.message}`);
    }
  }

  /**
   * Check for useEffect hooks calling async functions without error handling
   */
  checkUseEffectAsyncIssues(filePath, content, lines) {
    // Pattern: useEffect(() => { asyncFunction(); }, [])
    const useEffectPattern = /useEffect\s*\(\s*\(\s*\)\s*=>\s*\{([^}]*)\}/g;
    let match;

    while ((match = useEffectPattern.exec(content)) !== null) {
      const effectBody = match[1];
      
      // Check if it calls async functions without error handling
      if (this.hasAsyncCallsWithoutErrorHandling(effectBody)) {
        const lineNumber = content.substring(0, match.index).split('\n').length;
        this.issues.push({
          file: filePath,
          line: lineNumber,
          type: 'useEffect-async-no-error-handling',
          message: 'useEffect calls async function without error handling',
          code: match[0],
          severity: 'high'
        });
      }
    }
  }

  /**
   * Check for event handlers with async operations without try-catch
   */
  checkEventHandlerAsyncIssues(filePath, content, lines) {
    // Pattern: const handleSomething = async () => { ... }
    const asyncHandlerPattern = /const\s+handle\w+\s*=\s*async\s*\([^)]*\)\s*=>\s*\{([^}]*(?:\{[^}]*\}[^}]*)*)\}/g;
    let match;

    while ((match = asyncHandlerPattern.exec(content)) !== null) {
      const handlerBody = match[1];
      
      // Check if it has proper try-catch
      if (!handlerBody.includes('try') || !handlerBody.includes('catch')) {
        const lineNumber = content.substring(0, match.index).split('\n').length;
        this.issues.push({
          file: filePath,
          line: lineNumber,
          type: 'async-handler-no-try-catch',
          message: 'Async event handler missing try-catch block',
          code: match[0].substring(0, 100) + '...',
          severity: 'medium'
        });
      }
    }
  }

  /**
   * Check for missing await keywords
   */
  checkMissingAwaitKeywords(filePath, content, lines) {
    // Pattern: async function calls without await
    const asyncCallPattern = /(?<!await\s+)(\w+\.\w+\([^)]*\)(?:\s*\.then\s*\(|\s*\.catch\s*\())/g;
    let match;

    while ((match = asyncCallPattern.exec(content)) !== null) {
      const lineNumber = content.substring(0, match.index).split('\n').length;
      
      // Skip if it's already in a .then() or .catch() chain
      if (!match[0].includes('.then') && !match[0].includes('.catch')) {
        this.issues.push({
          file: filePath,
          line: lineNumber,
          type: 'missing-await',
          message: 'Potential missing await keyword on async operation',
          code: match[0],
          severity: 'low'
        });
      }
    }
  }

  /**
   * Check for unhandled promise rejections
   */
  checkUnhandledPromises(filePath, content, lines) {
    // Pattern: Promise chains without .catch()
    const promiseChainPattern = /\w+\([^)]*\)\.then\([^)]*\)(?!.*\.catch)/g;
    let match;

    while ((match = promiseChainPattern.exec(content)) !== null) {
      const lineNumber = content.substring(0, match.index).split('\n').length;
      this.issues.push({
        file: filePath,
        line: lineNumber,
        type: 'unhandled-promise-rejection',
        message: 'Promise chain without .catch() handler',
        code: match[0],
        severity: 'medium'
      });
    }
  }

  /**
   * Check if code has async calls without error handling
   */
  hasAsyncCallsWithoutErrorHandling(code) {
    // Check for async function calls
    const hasAsyncCalls = /\w+\s*\([^)]*\)(?:\s*\.then|\s*await|\s*\.\w+\s*\()/.test(code);
    
    // Check for error handling
    const hasErrorHandling = /try|catch|\.catch\(|handleError|onError/.test(code);
    
    return hasAsyncCalls && !hasErrorHandling;
  }

  /**
   * Fix specific async/await issues
   */
  async fixIssues() {
    console.log('🔧 Fixing async/await error handling issues...\n');

    // Group issues by file
    const issuesByFile = {};
    for (const issue of this.issues) {
      if (!issuesByFile[issue.file]) {
        issuesByFile[issue.file] = [];
      }
      issuesByFile[issue.file].push(issue);
    }

    // Fix each file
    for (const [filePath, fileIssues] of Object.entries(issuesByFile)) {
      await this.fixFileIssues(filePath, fileIssues);
    }

    console.log(`\n✅ Fixed ${this.fixedCount} async/await error handling issues`);
  }

  /**
   * Fix issues in a specific file
   */
  async fixFileIssues(filePath, issues) {
    const fullPath = path.join(this.projectRoot, filePath);
    
    try {
      let content = fs.readFileSync(fullPath, 'utf8');
      let modified = false;

      console.log(`🔧 Fixing ${path.relative(this.projectRoot, fullPath)} (${issues.length} issues):`);

      // Sort issues by line number in descending order to avoid line shifts
      const sortedIssues = issues.sort((a, b) => b.line - a.line);

      for (const issue of sortedIssues) {
        const fixResult = await this.applyFix(content, issue);
        if (fixResult.modified) {
          content = fixResult.content;
          modified = true;
          this.fixedCount++;
          console.log(`  ✅ Fixed: ${issue.message} (line ${issue.line})`);
        }
      }

      if (modified) {
        fs.writeFileSync(fullPath, content);
        this.fixedFiles.push(filePath);
      }

    } catch (error) {
      console.error(`  ❌ Error fixing ${filePath}: ${error.message}`);
    }
  }

  /**
   * Apply specific fix based on issue type
   */
  async applyFix(content, issue) {
    switch (issue.type) {
      case 'useEffect-async-no-error-handling':
        return this.fixUseEffectAsync(content, issue);
      
      case 'async-handler-no-try-catch':
        return this.fixAsyncHandlerTryCatch(content, issue);
      
      case 'unhandled-promise-rejection':
        return this.fixUnhandledPromise(content, issue);
      
      default:
        return { content, modified: false };
    }
  }

  /**
   * Fix useEffect with async calls
   */
  fixUseEffectAsync(content, issue) {
    // Replace useEffect(() => { asyncCall(); }, [])
    // with useEffect(() => { const loadData = async () => { try { await asyncCall(); } catch (error) { console.error(error); } }; loadData(); }, [])
    
    const lines = content.split('\n');
    const lineIndex = issue.line - 1;
    
    if (lineIndex >= 0 && lineIndex < lines.length) {
      const line = lines[lineIndex];
      
      // Simple fix: wrap async call in try-catch
      if (line.includes('useEffect(() => {') && !line.includes('try')) {
        const indentation = line.match(/^\s*/)[0];
        const nextLineIndex = lineIndex + 1;
        
        if (nextLineIndex < lines.length) {
          const nextLine = lines[nextLineIndex];
          if (nextLine.trim() && !nextLine.includes('try')) {
            // Insert error handling wrapper
            lines.splice(nextLineIndex, 0, 
              `${indentation}  const loadData = async () => {`,
              `${indentation}    try {`
            );
            
            // Find the closing of useEffect and add catch block
            let closingIndex = nextLineIndex + 2;
            while (closingIndex < lines.length && !lines[closingIndex].includes('}, [')) {
              closingIndex++;
            }
            
            if (closingIndex < lines.length) {
              lines.splice(closingIndex, 0,
                `${indentation}    } catch (error) {`,
                `${indentation}      console.error('Error in useEffect:', error);`,
                `${indentation}    }`,
                `${indentation}  };`,
                `${indentation}  loadData();`
              );
            }
            
            return { content: lines.join('\n'), modified: true };
          }
        }
      }
    }
    
    return { content, modified: false };
  }

  /**
   * Fix async handler without try-catch
   */
  fixAsyncHandlerTryCatch(content, issue) {
    // This is more complex and might need manual review
    // For now, just add a comment
    const lines = content.split('\n');
    const lineIndex = issue.line - 1;
    
    if (lineIndex >= 0 && lineIndex < lines.length) {
      const line = lines[lineIndex];
      const indentation = line.match(/^\s*/)[0];
      
      // Add comment above the handler
      lines.splice(lineIndex, 0, `${indentation}// TODO: Add try-catch error handling to this async handler`);
      return { content: lines.join('\n'), modified: true };
    }
    
    return { content, modified: false };
  }

  /**
   * Fix unhandled promise rejection
   */
  fixUnhandledPromise(content, issue) {
    // Add .catch() to promise chains
    const fixedContent = content.replace(
      new RegExp(issue.code.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')),
      `${issue.code}.catch(error => console.error('Promise rejection:', error))`
    );
    
    return { 
      content: fixedContent, 
      modified: fixedContent !== content 
    };
  }

  /**
   * Generate report
   */
  generateReport() {
    const report = {
      totalIssues: this.issues.length,
      fixedIssues: this.fixedCount,
      issuesByType: {},
      issuesBySeverity: {},
      fixedFiles: this.fixedFiles.length
    };

    // Group by type
    for (const issue of this.issues) {
      report.issuesByType[issue.type] = (report.issuesByType[issue.type] || 0) + 1;
      report.issuesBySeverity[issue.severity] = (report.issuesBySeverity[issue.severity] || 0) + 1;
    }

    return report;
  }

  /**
   * Run the complete analysis and fixing process
   */
  async run() {
    console.log('🚀 Starting async/await error handling analysis and fixes...\n');
    
    await this.scanForIssues();
    await this.fixIssues();
    
    const report = this.generateReport();
    
    console.log('\n📋 Summary:');
    console.log(`Total issues found: ${report.totalIssues}`);
    console.log(`Issues fixed: ${report.fixedIssues}`);
    console.log(`Files modified: ${report.fixedFiles}`);
    
    console.log('\n📊 Issues by type:');
    Object.entries(report.issuesByType).forEach(([type, count]) => {
      console.log(`  ${type}: ${count}`);
    });
    
    console.log('\n📊 Issues by severity:');
    Object.entries(report.issuesBySeverity).forEach(([severity, count]) => {
      console.log(`  ${severity}: ${count}`);
    });
  }
}

// Run the fixer
const fixer = new AsyncAwaitErrorFixer();
fixer.run().catch(console.error);
