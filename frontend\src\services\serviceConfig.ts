/**
 * Service Configuration
 * Centralized configuration for all services with environment-specific settings
 */

import type { ServiceConfig  } from './baseService';

// Environment detection
export const getEnvironment = (): 'development' | 'production' | 'test' => {
  if (typeof process !== 'undefined' && process.env) {
    return process.env.NODE_ENV as 'development' | 'production' | 'test' || 'development';
  }
  
  // Browser environment detection
  if (typeof window !== 'undefined') {
    if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
      return 'development';
    }
    if (window.location.hostname.includes('test') || window.location.hostname.includes('staging')) {
      return 'test';
    }
  }
  
  return 'production';
};

// Base configuration for different environments
export const EnvironmentConfigs: Record<string, ServiceConfig> = {
  development: {
    enableCaching: true,
    defaultTTL: 60000, // 1 minute for faster development iteration
    enableRetry: true,
    maxRetries: 2, // Fewer retries in development
    enableMetrics: true,
    enableLogging: true
  },
  
  test: {
    enableCaching: false, // Disable caching in tests for predictable behavior
    defaultTTL: 0,
    enableRetry: false, // Disable retries in tests for faster execution
    maxRetries: 0,
    enableMetrics: false,
    enableLogging: false
  },
  
  production: {
    enableCaching: true,
    defaultTTL: 300000, // 5 minutes
    enableRetry: true,
    maxRetries: 3,
    enableMetrics: true,
    enableLogging: false // Disable verbose logging in production
  }
};

// Service-specific configurations
export const ServiceConfigs = {
  // Document Service Configuration
  DocumentService: {
    development: {
      ...EnvironmentConfigs.development,
      defaultTTL: 30000, // 30 seconds for documents in development
      maxRetries: 1
    },
    test: {
      ...EnvironmentConfigs.test
    },
    production: {
      ...EnvironmentConfigs.production,
      defaultTTL: 600000, // 10 minutes for documents in production
      maxRetries: 5 // More retries for critical document operations
    }
  },

  // Template Service Configuration
  TemplateService: {
    development: {
      ...EnvironmentConfigs.development,
      defaultTTL: 120000, // 2 minutes for templates
    },
    test: {
      ...EnvironmentConfigs.test
    },
    production: {
      ...EnvironmentConfigs.production,
      defaultTTL: 1800000, // 30 minutes for templates (they change less frequently)
    }
  },

  // Prompt Generation Service Configuration
  PromptGenerationService: {
    development: {
      ...EnvironmentConfigs.development,
      enableCaching: false, // Disable caching for prompt generation in development
      maxRetries: 1
    },
    test: {
      ...EnvironmentConfigs.test
    },
    production: {
      ...EnvironmentConfigs.production,
      enableCaching: false, // Prompts should be fresh, not cached
      maxRetries: 2
    }
  },

  // AI Service Configuration
  AIService: {
    development: {
      ...EnvironmentConfigs.development,
      enableCaching: true,
      defaultTTL: 300000, // 5 minutes for AI responses
      maxRetries: 2
    },
    test: {
      ...EnvironmentConfigs.test,
      enableCaching: false
    },
    production: {
      ...EnvironmentConfigs.production,
      enableCaching: true,
      defaultTTL: 900000, // 15 minutes for AI responses
      maxRetries: 3
    }
  },

  // Analytics Service Configuration
  AnalyticsService: {
    development: {
      ...EnvironmentConfigs.development,
      enableCaching: false, // Don't cache analytics in development
      enableMetrics: false // Disable metrics collection in development
    },
    test: {
      ...EnvironmentConfigs.test
    },
    production: {
      ...EnvironmentConfigs.production,
      enableCaching: false, // Analytics should be real-time
      enableMetrics: true
    }
  },

  // Auth Service Configuration
  AuthService: {
    development: {
      ...EnvironmentConfigs.development,
      defaultTTL: 60000, // 1 minute for auth tokens in development
      maxRetries: 1
    },
    test: {
      ...EnvironmentConfigs.test
    },
    production: {
      ...EnvironmentConfigs.production,
      defaultTTL: 3600000, // 1 hour for auth tokens in production
      maxRetries: 2
    }
  },

  // Workspace Service Configuration
  WorkspaceService: {
    development: {
      ...EnvironmentConfigs.development,
      defaultTTL: 120000, // 2 minutes for workspace data
    },
    test: {
      ...EnvironmentConfigs.test
    },
    production: {
      ...EnvironmentConfigs.production,
      defaultTTL: 600000, // 10 minutes for workspace data
    }
  },

  // Settings Service Configuration
  SettingsService: {
    development: {
      ...EnvironmentConfigs.development,
      defaultTTL: 300000, // 5 minutes for settings
    },
    test: {
      ...EnvironmentConfigs.test
    },
    production: {
      ...EnvironmentConfigs.production,
      defaultTTL: 1800000, // 30 minutes for settings
    }
  }
};

// Cache TTL configurations for different data types
export const CacheTTLConfigs = {
  // User data
  userProfile: 300000, // 5 minutes
  userSettings: 1800000, // 30 minutes
  userDocuments: 120000, // 2 minutes
  
  // Document data
  documentMetadata: 600000, // 10 minutes
  documentContent: 1800000, // 30 minutes
  documentChunks: 3600000, // 1 hour
  
  // Template data
  templates: 1800000, // 30 minutes
  templateCategories: 3600000, // 1 hour
  
  // AI responses
  aiResponses: 900000, // 15 minutes
  promptSuggestions: 600000, // 10 minutes
  
  // Analytics data
  usageStats: 300000, // 5 minutes
  performanceMetrics: 120000, // 2 minutes
  
  // System data
  systemConfig: 3600000, // 1 hour
  featureFlags: 600000, // 10 minutes
};

// Retry configurations for different operation types
export const RetryConfigs = {
  // Network operations
  networkRequest: {
    maxRetries: 3,
    baseDelay: 1000,
    maxDelay: 10000,
    backoffFactor: 2
  },
  
  // Database operations
  databaseOperation: {
    maxRetries: 5,
    baseDelay: 500,
    maxDelay: 5000,
    backoffFactor: 1.5
  },
  
  // File operations
  fileOperation: {
    maxRetries: 3,
    baseDelay: 2000,
    maxDelay: 15000,
    backoffFactor: 2
  },
  
  // AI API calls
  aiApiCall: {
    maxRetries: 2,
    baseDelay: 2000,
    maxDelay: 20000,
    backoffFactor: 3
  },
  
  // Authentication operations
  authOperation: {
    maxRetries: 2,
    baseDelay: 1000,
    maxDelay: 5000,
    backoffFactor: 2
  }
};

// Get configuration for a specific service
export function getServiceConfig(serviceName: string): ServiceConfig {
  const environment = getEnvironment();
  const serviceConfig = ServiceConfigs[serviceName as keyof typeof ServiceConfigs];
  
  if (serviceConfig && serviceConfig[environment]) {
    return serviceConfig[environment];
  }
  
  // Fallback to environment default
  return EnvironmentConfigs[environment];
}

// Get cache TTL for a specific data type
export function getCacheTTL(dataType: string): number {
  return CacheTTLConfigs[dataType as keyof typeof CacheTTLConfigs] || 300000; // Default 5 minutes
}

// Get retry configuration for a specific operation type
export function getRetryConfig(operationType: string) {
  return RetryConfigs[operationType as keyof typeof RetryConfigs] || RetryConfigs.networkRequest;
}

// Configuration validation
export function validateServiceConfig(config: ServiceConfig): boolean {
  if (typeof config !== 'object' || config === null) {
    return false;
  }
  
  // Check required properties
  const requiredProps = ['enableCaching', 'enableRetry', 'enableMetrics', 'enableLogging'];
  for (const prop of requiredProps) {
    if (typeof config[prop as keyof ServiceConfig] !== 'boolean') {
      return false;
    }
  }
  
  // Check numeric properties
  if (config.defaultTTL !== undefined && (typeof config.defaultTTL !== 'number' || config.defaultTTL < 0)) {
    return false;
  }
  
  if (config.maxRetries !== undefined && (typeof config.maxRetries !== 'number' || config.maxRetries < 0)) {
    return false;
  }
  
  return true;
}

// Configuration merging utility
export function mergeConfigs(baseConfig: ServiceConfig, overrideConfig: Partial<ServiceConfig>): ServiceConfig {
  return {
    ...baseConfig,
    ...overrideConfig
  };
}

// Export current environment configuration
export const currentEnvironment = getEnvironment();
export const currentEnvironmentConfig = EnvironmentConfigs[currentEnvironment];

// Configuration change detection
let configChangeListeners: Array<(newConfig: ServiceConfig) => void> = [];

export function onConfigChange(listener: (newConfig: ServiceConfig) => void): () => void {
  configChangeListeners.push(listener);
  
  // Return unsubscribe function
  return () => {
    configChangeListeners = configChangeListeners.filter(l => l !== listener);
  };
}

export function notifyConfigChange(newConfig: ServiceConfig): void {
  configChangeListeners.forEach(listener => {
    try {
      listener(newConfig);
    } catch (error) {
      console.error('Error in config change listener:', error);
    }
  });
}
