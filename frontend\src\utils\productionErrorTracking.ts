import type { StandardError } from './errorHandler';

export interface ErrorAlert {
  id: string;
  error: StandardError;
  timestamp: number;
  severity: 'low' | 'medium' | 'high' | 'critical';
  frequency: number;
  affectedUsers: number;
  acknowledged: boolean;
  context: {
    url: string;
    userAgent: string;
    sessionId: string;
    userId?: string;
    component?: string;
    action?: string;
  };
}

export interface ErrorMetrics {
  totalErrors: number;
  errorRate: number; // errors per minute
  criticalErrors: number;
  affectedUsers: number;
  topErrors: Array<{
    message: string;
    count: number;
    lastSeen: number;
  }>;
  errorsByComponent: Record<string, number>;
  errorsByType: Record<string, number>;
}

export interface AlertingConfig {
  enabled: boolean;
  thresholds: {
    errorRate: number; // errors per minute
    criticalErrorRate: number; // critical errors per hour
    userImpactPercentage: number; // percentage of users affected
    componentErrorRate: number; // errors per component per hour
  };
  channels: {
    webhook?: string;
    email?: string;
    slack?: string;
  };
  retryConfig: {
    maxRetries: number;
    retryDelay: number;
    backoffMultiplier: number;
  };
}

/**
 * Production-ready error tracking and alerting system
 * Aggregates errors, calculates metrics, and sends intelligent alerts
 */
export class ProductionErrorTracker {
  private errors: StandardError[] = [];
  private alerts: ErrorAlert[] = [];
  private metrics: ErrorMetrics;
  private config: AlertingConfig;
  private sessionId: string;
  private userId?: string;
  private listeners: Array<(alert: ErrorAlert) => void> = [];
  private metricsListeners: Array<(metrics: ErrorMetrics) => void> = [];

  constructor(config: Partial<AlertingConfig> = {}) {
    this.sessionId = this.generateSessionId();
    this.config = {
      enabled: process.env.NODE_ENV === 'production' || process.env.VITE_ENABLE_ERROR_ALERTS === 'true',
      thresholds: {
        errorRate: 5, // 5 errors per minute
        criticalErrorRate: 1, // 1 critical error per hour
        userImpactPercentage: 10, // 10% of users affected
        componentErrorRate: 3, // 3 errors per component per hour
      },
      channels: {
        webhook: process.env.VITE_ERROR_WEBHOOK_URL,
        email: process.env.VITE_ERROR_EMAIL_ENDPOINT,
        slack: process.env.VITE_ERROR_SLACK_WEBHOOK,
      },
      retryConfig: {
        maxRetries: 3,
        retryDelay: 1000,
        backoffMultiplier: 2,
      },
      ...config
    };

    this.metrics = this.initializeMetrics();
    this.startMetricsCalculation();
  }

  /**
   * Track a new error
   */
  trackError(error: StandardError, context?: Record<string, any>): void {
    if (!this.config.enabled) return;

    const enhancedError = {
      ...error,
      timestamp: Date.now(),
      sessionId: this.sessionId,
      userId: this.userId,
      context: {
        url: window.location.href,
        userAgent: navigator.userAgent,
        ...context
      }
    };

    this.errors.push(enhancedError);
    
    // Keep only last 1000 errors in memory
    if (this.errors.length > 1000) {
      this.errors = this.errors.slice(-1000);
    }

    // Update metrics
    this.updateMetrics();

    // Check for alert conditions
    this.checkAlertConditions(enhancedError);

    // Store in localStorage for persistence
    this.persistError(enhancedError);
  }

  /**
   * Set current user ID for tracking
   */
  setUserId(userId: string): void {
    this.userId = userId;
  }

  /**
   * Check if error should trigger an alert
   */
  private checkAlertConditions(error: StandardError): void {
    const now = Date.now();
    const oneHour = 60 * 60 * 1000;
    const oneMinute = 60 * 1000;

    // Check error rate (errors per minute)
    const recentErrors = this.errors.filter(e => now - e.timestamp < oneMinute);
    if (recentErrors.length >= this.config.thresholds.errorRate) {
      this.createAlert(error, 'high', `High error rate: ${recentErrors.length} errors in the last minute`);
    }

    // Check critical error rate
    if (error.severity === 'critical') {
      const recentCriticalErrors = this.errors.filter(
        e => e.severity === 'critical' && now - e.timestamp < oneHour
      );
      if (recentCriticalErrors.length >= this.config.thresholds.criticalErrorRate) {
        this.createAlert(error, 'critical', `Critical error threshold exceeded: ${recentCriticalErrors.length} critical errors in the last hour`);
      }
    }

    // Check component error rate
    if (error.context?.component) {
      const componentErrors = this.errors.filter(
        e => e.context?.component === error.context?.component && now - e.timestamp < oneHour
      );
      if (componentErrors.length >= this.config.thresholds.componentErrorRate) {
        this.createAlert(error, 'medium', `High error rate in component ${error.context.component}: ${componentErrors.length} errors in the last hour`);
      }
    }

    // Check user impact (simplified - in real implementation, track unique users)
    const uniqueSessionsWithErrors = new Set(
      this.errors.filter(e => now - e.timestamp < oneHour).map(e => e.sessionId)
    ).size;
    
    // Estimate total active sessions (simplified)
    const estimatedActiveSessions = Math.max(uniqueSessionsWithErrors * 10, 100);
    const userImpactPercentage = (uniqueSessionsWithErrors / estimatedActiveSessions) * 100;
    
    if (userImpactPercentage >= this.config.thresholds.userImpactPercentage) {
      this.createAlert(error, 'high', `High user impact: ${userImpactPercentage.toFixed(1)}% of users affected by errors`);
    }
  }

  /**
   * Create and send alert
   */
  private createAlert(error: StandardError, severity: ErrorAlert['severity'], message: string): void {
    const alert: ErrorAlert = {
      id: this.generateAlertId(),
      error,
      timestamp: Date.now(),
      severity,
      frequency: this.getErrorFrequency(error),
      affectedUsers: this.getAffectedUsersCount(error),
      acknowledged: false,
      context: {
        url: window.location.href,
        userAgent: navigator.userAgent,
        sessionId: this.sessionId,
        userId: this.userId,
        component: error.context?.component,
        action: error.context?.action,
      }
    };

    this.alerts.push(alert);
    
    // Keep only last 100 alerts
    if (this.alerts.length > 100) {
      this.alerts = this.alerts.slice(-100);
    }

    // Notify listeners
    this.listeners.forEach(listener => listener(alert));

    // Send alert notifications
    this.sendAlertNotifications(alert, message);
  }

  /**
   * Send alert to configured channels
   */
  private async sendAlertNotifications(alert: ErrorAlert, message: string): Promise<void> {
    const notifications = [];

    if (this.config.channels.webhook) {
      notifications.push(this.sendWebhookAlert(alert, message));
    }

    if (this.config.channels.email) {
      notifications.push(this.sendEmailAlert(alert, message));
    }

    if (this.config.channels.slack) {
      notifications.push(this.sendSlackAlert(alert, message));
    }

    try {
      await Promise.allSettled(notifications);
    } catch (error) {
      console.error('Failed to send some alert notifications:', error);
    }
  }

  /**
   * Send webhook alert
   */
  private async sendWebhookAlert(alert: ErrorAlert, message: string): Promise<void> {
    if (!this.config.channels.webhook) return;

    const payload = {
      alert_id: alert.id,
      severity: alert.severity,
      message,
      error: {
        name: alert.error.name,
        message: alert.error.message,
        code: alert.error.code,
        category: alert.error.category,
        stack: alert.error.stack?.substring(0, 1000), // Limit stack trace
      },
      context: alert.context,
      metrics: {
        frequency: alert.frequency,
        affected_users: alert.affectedUsers,
      },
      timestamp: alert.timestamp,
    };

    await this.retryRequest(() =>
      fetch(this.config.channels.webhook!, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload)
      })
    );
  }

  /**
   * Send email alert
   */
  private async sendEmailAlert(alert: ErrorAlert, message: string): Promise<void> {
    if (!this.config.channels.email) return;

    const payload = {
      to: process.env.VITE_ALERT_EMAIL || '<EMAIL>',
      subject: `${alert.severity.toUpperCase()} Error Alert: ${alert.error.name}`,
      body: `
        ${message}
        
        Error Details:
        - Message: ${alert.error.message}
        - Component: ${alert.context.component || 'Unknown'}
        - URL: ${alert.context.url}
        - Frequency: ${alert.frequency} occurrences
        - Affected Users: ${alert.affectedUsers}
        - Time: ${new Date(alert.timestamp).toISOString()}
        
        Stack Trace:
        ${alert.error.stack || 'Not available'}
      `,
      alert_data: {
        alert_id: alert.id,
        severity: alert.severity,
        error_name: alert.error.name,
        error_message: alert.error.message,
        component: alert.context.component,
        url: alert.context.url,
        timestamp: new Date(alert.timestamp).toISOString(),
      }
    };

    await this.retryRequest(() =>
      fetch(this.config.channels.email!, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload)
      })
    );
  }

  /**
   * Send Slack alert
   */
  private async sendSlackAlert(alert: ErrorAlert, message: string): Promise<void> {
    if (!this.config.channels.slack) return;

    const color = {
      low: 'good',
      medium: 'warning',
      high: 'warning',
      critical: 'danger'
    }[alert.severity];

    const emoji = {
      low: '🟡',
      medium: '🟠',
      high: '🔴',
      critical: '🚨'
    }[alert.severity];

    const payload = {
      text: `${emoji} Error Alert - ${alert.severity.toUpperCase()}`,
      attachments: [{
        color,
        fields: [
          { title: 'Error', value: alert.error.name, short: true },
          { title: 'Message', value: alert.error.message, short: true },
          { title: 'Component', value: alert.context.component || 'Unknown', short: true },
          { title: 'Frequency', value: `${alert.frequency} occurrences`, short: true },
          { title: 'Affected Users', value: alert.affectedUsers.toString(), short: true },
          { title: 'Time', value: new Date(alert.timestamp).toLocaleString(), short: true },
          { title: 'URL', value: alert.context.url, short: false },
        ]
      }]
    };

    await this.retryRequest(() =>
      fetch(this.config.channels.slack!, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload)
      })
    );
  }

  /**
   * Retry request with exponential backoff
   */
  private async retryRequest(requestFn: () => Promise<Response>): Promise<void> {
    let lastError: Error | null = null;
    
    for (let attempt = 0; attempt < this.config.retryConfig.maxRetries; attempt++) {
      try {
        const response = await requestFn();
        if (response.ok) return;
        
        lastError = new Error(`HTTP ${response.status}: ${response.statusText}`);
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));
      }
      
      if (attempt < this.config.retryConfig.maxRetries - 1) {
        const delay = this.config.retryConfig.retryDelay * 
                     Math.pow(this.config.retryConfig.backoffMultiplier, attempt);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
    
    if (lastError) {
      console.error('Failed to send alert after retries:', lastError);
    }
  }

  /**
   * Get error frequency (how many times this error has occurred)
   */
  private getErrorFrequency(error: StandardError): number {
    return this.errors.filter(e =>
      e.message === error.message &&
      e.name === error.name &&
      e.context?.component === error.context?.component
    ).length;
  }

  /**
   * Get affected users count (simplified - count unique sessions)
   */
  private getAffectedUsersCount(error: StandardError): number {
    const uniqueSessions = new Set(
      this.errors
        .filter(e => e.message === error.message && e.name === error.name)
        .map(e => e.sessionId)
    );
    return uniqueSessions.size;
  }

  /**
   * Initialize metrics object
   */
  private initializeMetrics(): ErrorMetrics {
    return {
      totalErrors: 0,
      errorRate: 0,
      criticalErrors: 0,
      affectedUsers: 0,
      topErrors: [],
      errorsByComponent: {},
      errorsByType: {},
    };
  }

  /**
   * Update metrics based on current errors
   */
  private updateMetrics(): void {
    const now = Date.now();
    const oneHour = 60 * 60 * 1000;
    const oneMinute = 60 * 1000;

    // Recent errors (last hour)
    const recentErrors = this.errors.filter(e => now - e.timestamp < oneHour);

    // Error rate (per minute)
    const lastMinuteErrors = this.errors.filter(e => now - e.timestamp < oneMinute);

    this.metrics = {
      totalErrors: this.errors.length,
      errorRate: lastMinuteErrors.length,
      criticalErrors: recentErrors.filter(e => e.severity === 'critical').length,
      affectedUsers: new Set(recentErrors.map(e => e.sessionId)).size,
      topErrors: this.getTopErrors(recentErrors),
      errorsByComponent: this.getErrorsByComponent(recentErrors),
      errorsByType: this.getErrorsByType(recentErrors),
    };

    // Notify metrics listeners
    this.metricsListeners.forEach(listener => listener(this.metrics));
  }

  /**
   * Get top errors by frequency
   */
  private getTopErrors(errors: StandardError[]): ErrorMetrics['topErrors'] {
    const errorCounts = new Map<string, { count: number; lastSeen: number }>();

    errors.forEach(error => {
      const key = `${error.name}: ${error.message}`;
      const existing = errorCounts.get(key);
      errorCounts.set(key, {
        count: (existing?.count || 0) + 1,
        lastSeen: Math.max(existing?.lastSeen || 0, error.timestamp)
      });
    });

    return Array.from(errorCounts.entries())
      .map(([message, data]) => ({ message, ...data }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);
  }

  /**
   * Get errors grouped by component
   */
  private getErrorsByComponent(errors: StandardError[]): Record<string, number> {
    const componentCounts: Record<string, number> = {};

    errors.forEach(error => {
      const component = error.context?.component || 'Unknown';
      componentCounts[component] = (componentCounts[component] || 0) + 1;
    });

    return componentCounts;
  }

  /**
   * Get errors grouped by type
   */
  private getErrorsByType(errors: StandardError[]): Record<string, number> {
    const typeCounts: Record<string, number> = {};

    errors.forEach(error => {
      const type = error.category || error.name || 'Unknown';
      typeCounts[type] = (typeCounts[type] || 0) + 1;
    });

    return typeCounts;
  }

  /**
   * Start periodic metrics calculation
   */
  private startMetricsCalculation(): void {
    // Update metrics every 30 seconds
    setInterval(() => {
      this.updateMetrics();
    }, 30000);
  }

  /**
   * Persist error to localStorage
   */
  private persistError(error: StandardError): void {
    try {
      const existingErrors = JSON.parse(localStorage.getItem('production_errors') || '[]');
      existingErrors.push({
        ...error,
        timestamp: error.timestamp,
        sessionId: this.sessionId,
      });

      // Keep only last 500 errors in localStorage
      if (existingErrors.length > 500) {
        existingErrors.splice(0, existingErrors.length - 500);
      }

      localStorage.setItem('production_errors', JSON.stringify(existingErrors));
    } catch (e) {
      // Silently fail - don't let error persistence break the app
      console.debug('Failed to persist error to localStorage:', e);
    }
  }

  /**
   * Generate unique session ID
   */
  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).slice(2)}`;
  }

  /**
   * Generate unique alert ID
   */
  private generateAlertId(): string {
    return `alert_${Date.now()}_${Math.random().toString(36).slice(2)}`;
  }

  /**
   * Subscribe to alerts
   */
  onAlert(callback: (alert: ErrorAlert) => void): () => void {
    this.listeners.push(callback);
    return () => {
      const index = this.listeners.indexOf(callback);
      if (index > -1) this.listeners.splice(index, 1);
    };
  }

  /**
   * Subscribe to metrics updates
   */
  onMetricsUpdate(callback: (metrics: ErrorMetrics) => void): () => void {
    this.metricsListeners.push(callback);
    return () => {
      const index = this.metricsListeners.indexOf(callback);
      if (index > -1) this.metricsListeners.splice(index, 1);
    };
  }

  /**
   * Get current metrics
   */
  getMetrics(): ErrorMetrics {
    return { ...this.metrics };
  }

  /**
   * Get all alerts
   */
  getAlerts(): ErrorAlert[] {
    return [...this.alerts];
  }

  /**
   * Acknowledge alert
   */
  acknowledgeAlert(alertId: string): void {
    const alert = this.alerts.find(a => a.id === alertId);
    if (alert) {
      alert.acknowledged = true;
    }
  }

  /**
   * Clear all alerts
   */
  clearAlerts(): void {
    this.alerts = [];
  }

  /**
   * Update configuration
   */
  updateConfig(config: Partial<AlertingConfig>): void {
    this.config = { ...this.config, ...config };
  }
}

// Global production error tracker instance
export const productionErrorTracker = new ProductionErrorTracker();
