import React, { useState, useEffect } from 'react';
import { useWebVitals } from '@/hooks/useWebVitals';
import { webVitalsAlerting } from '@/utils/webVitalsAlerting';
import type { Alert } from '@/utils/webVitalsAlerting';
import type { ChartBarIcon, ExclamationTriangleIcon, CheckCircleIcon } from '@heroicons/react/24/outline';

interface CoreWebVitalsDashboardProps {
  className?: string;
  showAlerts?: boolean;
  showTrends?: boolean;
}

/**
 * Comprehensive Core Web Vitals dashboard for production monitoring
 * Displays real-time metrics, trends, and alerts
 */
export const CoreWebVitalsDashboard: React.FC<CoreWebVitalsDashboardProps> = ({
  className = '',
  showAlerts = true,
  showTrends = true
}) => {
  const { summary, metrics, getOverallScore } = useWebVitals();
  const [alerts, setAlerts] = useState<Alert[]>([]);
  const [isExpanded, setIsExpanded] = useState(false);

  // Subscribe to alerts
  useEffect(() => {
    const unsubscribe = webVitalsAlerting.onAlert((alert) => {
      setAlerts(prev => [alert, ...prev].slice(0, 10)); // Keep last 10 alerts
    });

    // Load existing alerts
    setAlerts(webVitalsAlerting.getAlerts().slice(-10));

    return unsubscribe;
  }, []);

  const overallScore = getOverallScore();
  const activeAlerts = alerts.filter(a => !a.acknowledged);

  const getScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-600';
    if (score >= 50) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getScoreBackground = (score: number) => {
    if (score >= 90) return 'bg-green-50 border-green-200';
    if (score >= 50) return 'bg-yellow-50 border-yellow-200';
    return 'bg-red-50 border-red-200';
  };

  const getRatingColor = (rating: string) => {
    switch (rating) {
      case 'good': return 'text-green-600';
      case 'needs-improvement': return 'text-yellow-600';
      case 'poor': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  const formatValue = (metricName: string, value: number) => {
    if (metricName === 'CLS') return value.toFixed(3);
    return Math.round(value);
  };

  const getUnit = (metricName: string) => {
    if (metricName === 'CLS') return '';
    return 'ms';
  };

  const coreMetrics = ['LCP', 'FID', 'CLS'];
  const otherMetrics = ['FCP', 'TTFB'];

  return (
    <div className={`bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 ${className}`}>
      {/* Header */}
      <div className="p-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <ChartBarIcon className="w-5 h-5 text-blue-600" />
            <h3 className="font-semibold text-gray-900 dark:text-white">Core Web Vitals</h3>
            {activeAlerts.length > 0 && (
              <div className="flex items-center space-x-1">
                <ExclamationTriangleIcon className="w-4 h-4 text-red-500" />
                <span className="text-sm text-red-600 font-medium">{activeAlerts.length}</span>
              </div>
            )}
          </div>
          <div className="flex items-center space-x-3">
            <div className={`px-3 py-1 rounded-full border ${getScoreBackground(overallScore)}`}>
              <span className={`text-sm font-semibold ${getScoreColor(overallScore)}`}>
                Score: {overallScore}
              </span>
            </div>
            <button
              onClick={() => setIsExpanded(!isExpanded)}
              className="text-sm text-blue-600 hover:text-blue-700"
            >
              {isExpanded ? 'Collapse' : 'Expand'}
            </button>
          </div>
        </div>
      </div>

      {/* Core Metrics */}
      <div className="p-4">
        <div className="grid grid-cols-3 gap-4 mb-4">
          {coreMetrics.map(metricName => {
            const metric = summary[metricName];
            return (
              <div key={metricName} className="text-center">
                <div className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">
                  {metricName}
                </div>
                {metric ? (
                  <>
                    <div className={`text-2xl font-bold ${getRatingColor(metric.rating)}`}>
                      {formatValue(metricName, metric.value)}{getUnit(metricName)}
                    </div>
                    <div className="text-xs text-gray-500 capitalize">
                      {metric.rating.replace('-', ' ')}
                    </div>
                  </>
                ) : (
                  <div className="text-2xl font-bold text-gray-400">-</div>
                )}
              </div>
            );
          })}
        </div>

        {/* Other Metrics */}
        {isExpanded && (
          <div className="border-t border-gray-200 dark:border-gray-700 pt-4">
            <div className="grid grid-cols-2 gap-4 mb-4">
              {otherMetrics.map(metricName => {
                const metric = summary[metricName];
                return (
                  <div key={metricName} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded">
                    <span className="font-medium text-gray-900 dark:text-white">{metricName}</span>
                    {metric ? (
                      <div className="text-right">
                        <div className={`font-bold ${getRatingColor(metric.rating)}`}>
                          {formatValue(metricName, metric.value)}{getUnit(metricName)}
                        </div>
                        <div className="text-xs text-gray-500 capitalize">
                          {metric.rating.replace('-', ' ')}
                        </div>
                      </div>
                    ) : (
                      <span className="text-gray-400">-</span>
                    )}
                  </div>
                );
              })}
            </div>
          </div>
        )}

        {/* Alerts Section */}
        {showAlerts && activeAlerts.length > 0 && (
          <div className="border-t border-gray-200 dark:border-gray-700 pt-4">
            <h4 className="font-medium text-gray-900 dark:text-white mb-3 flex items-center">
              <ExclamationTriangleIcon className="w-4 h-4 text-red-500 mr-2" />
              Active Alerts ({activeAlerts.length})
            </h4>
            <div className="space-y-2">
              {activeAlerts.slice(0, 3).map((alert) => (
                <div key={alert.id} className="p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="text-sm font-medium text-red-800 dark:text-red-200">
                        {alert.metric.name} Alert
                      </div>
                      <div className="text-xs text-red-600 dark:text-red-300 mt-1">
                        {formatValue(alert.metric.name, alert.metric.value)}{getUnit(alert.metric.name)} 
                        (threshold: {alert.rule.threshold}{getUnit(alert.metric.name)})
                      </div>
                    </div>
                    <div className="text-xs text-red-500">
                      {new Date(alert.timestamp).toLocaleTimeString()}
                    </div>
                  </div>
                </div>
              ))}
              {activeAlerts.length > 3 && (
                <div className="text-xs text-gray-500 text-center">
                  +{activeAlerts.length - 3} more alerts
                </div>
              )}
            </div>
          </div>
        )}

        {/* Trends Section */}
        {showTrends && isExpanded && metrics.length > 0 && (
          <div className="border-t border-gray-200 dark:border-gray-700 pt-4">
            <h4 className="font-medium text-gray-900 dark:text-white mb-3">Recent Measurements</h4>
            <div className="space-y-2">
              {metrics.slice(-5).reverse().map((metric, index) => (
                <div key={`${metric.name}-${index}`} className="flex items-center justify-between text-sm">
                  <div className="flex items-center space-x-2">
                    <span className="font-medium">{metric.name}</span>
                    <span className={`px-2 py-1 rounded text-xs ${getRatingColor(metric.rating)}`}>
                      {metric.rating}
                    </span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className={getRatingColor(metric.rating)}>
                      {formatValue(metric.name, metric.value)}{getUnit(metric.name)}
                    </span>
                    <span className="text-gray-400 text-xs">
                      {new Date(metric.timestamp).toLocaleTimeString()}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Status Indicator */}
        <div className="flex items-center justify-center mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
          <div className="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
            <CheckCircleIcon className="w-4 h-4 text-green-500" />
            <span>Monitoring Active</span>
            <span className="text-xs">({metrics.length} measurements)</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CoreWebVitalsDashboard;
