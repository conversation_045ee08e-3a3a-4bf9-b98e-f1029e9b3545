/**
 * Quality Gates Configuration
 * 
 * Defines quality thresholds and gates for the application to ensure
 * consistent code quality, performance, and maintainability standards.
 * 
 * @module QualityGatesConfig
 * @version 1.0.0
 */

module.exports = {
  // Test Coverage Thresholds
  coverage: {
    global: {
      branches: 75,
      functions: 80,
      lines: 80,
      statements: 80,
    },
    // Per-file thresholds for critical components
    perFile: {
      branches: 70,
      functions: 75,
      lines: 75,
      statements: 75,
    },
    // Specific thresholds for different file types
    thresholds: {
      'src/services/**/*.ts': {
        branches: 85,
        functions: 90,
        lines: 85,
        statements: 85,
      },
      'src/utils/**/*.ts': {
        branches: 90,
        functions: 95,
        lines: 90,
        statements: 90,
      },
      'src/components/**/*.tsx': {
        branches: 70,
        functions: 75,
        lines: 75,
        statements: 75,
      },
    },
  },

  // Code Quality Metrics
  codeQuality: {
    // ESLint error tolerance
    eslint: {
      maxErrors: 0,
      maxWarnings: 5,
      allowedRules: [
        'react-hooks/exhaustive-deps', // Allow some flexibility for optimization
      ],
    },
    
    // TypeScript strict mode requirements
    typescript: {
      strict: true,
      noImplicitAny: true,
      noImplicitReturns: true,
      noUnusedLocals: true,
      noUnusedParameters: true,
    },
    
    // Code complexity thresholds
    complexity: {
      maxCyclomaticComplexity: 10,
      maxCognitiveComplexity: 15,
      maxLinesPerFunction: 50,
      maxParametersPerFunction: 5,
    },
  },

  // Performance Budgets
  performance: {
    // Bundle size limits (in KB)
    bundleSize: {
      maxTotalSize: 1024, // 1MB total
      maxChunkSize: 512,  // 512KB per chunk
      maxAssetSize: 256,  // 256KB per asset
    },
    
    // Runtime performance thresholds
    runtime: {
      maxRenderTime: 16,     // 16ms for 60fps
      maxMemoryUsage: 50,    // 50MB max memory usage
      maxNetworkRequests: 10, // Max concurrent requests
    },
    
    // Lighthouse score thresholds
    lighthouse: {
      performance: 90,
      accessibility: 95,
      bestPractices: 90,
      seo: 85,
      pwa: 80,
    },
  },

  // Security Requirements
  security: {
    // Dependency vulnerability tolerance
    vulnerabilities: {
      maxCritical: 0,
      maxHigh: 0,
      maxModerate: 2,
      maxLow: 5,
    },
    
    // Security headers requirements
    headers: [
      'Content-Security-Policy',
      'X-Frame-Options',
      'X-Content-Type-Options',
      'Referrer-Policy',
      'Permissions-Policy',
    ],
    
    // Sensitive data patterns to avoid
    sensitivePatterns: [
      /api[_-]?key/i,
      /secret/i,
      /password/i,
      /token/i,
      /private[_-]?key/i,
    ],
  },

  // Accessibility Standards
  accessibility: {
    // WCAG compliance level
    wcagLevel: 'AA',
    
    // Required accessibility features
    requirements: [
      'alt-text-images',
      'keyboard-navigation',
      'screen-reader-support',
      'color-contrast',
      'focus-indicators',
      'semantic-html',
    ],
    
    // Accessibility testing thresholds
    axeCore: {
      maxViolations: 0,
      allowedRules: [
        // Allow some flexibility for complex components
        'color-contrast-enhanced',
      ],
    },
  },

  // Documentation Requirements
  documentation: {
    // JSDoc coverage thresholds
    jsdoc: {
      minCoverage: 80,
      requiredTags: ['param', 'returns', 'example'],
      requiredForPublicAPI: true,
    },
    
    // README and documentation files
    requiredFiles: [
      'README.md',
      'CONTRIBUTING.md',
      'CHANGELOG.md',
      'docs/API.md',
      'docs/DEPLOYMENT.md',
    ],
    
    // Code comment requirements
    comments: {
      minComplexityForComments: 5,
      requireHeaderComments: true,
      requireInlineComments: false,
    },
  },

  // Git and Version Control Standards
  git: {
    // Commit message format (enforced by commitlint)
    commitFormat: 'conventional',
    
    // Branch naming conventions
    branchNaming: {
      pattern: '^(feature|bugfix|hotfix|release)\/[a-z0-9-]+$',
      maxLength: 50,
    },
    
    // Pull request requirements
    pullRequest: {
      minReviewers: 1,
      requireTests: true,
      requireDocumentation: true,
      blockMergeOnFailedChecks: true,
    },
  },

  // CI/CD Pipeline Requirements
  cicd: {
    // Required pipeline stages
    requiredStages: [
      'quality-checks',
      'security-scan',
      'test',
      'test-coverage',
      'build',
    ],
    
    // Deployment gates
    deployment: {
      requireAllTestsPass: true,
      requireCoverageThreshold: true,
      requireSecurityScan: true,
      requirePerformanceCheck: true,
    },
    
    // Environment-specific requirements
    environments: {
      staging: {
        autoDeployOnMerge: true,
        requireApproval: false,
      },
      production: {
        autoDeployOnMerge: false,
        requireApproval: true,
        requireManualTesting: true,
      },
    },
  },

  // Quality Gate Enforcement
  enforcement: {
    // Block actions on quality gate failures
    blockOnFailure: {
      commit: true,
      push: true,
      merge: true,
      deploy: true,
    },
    
    // Warning thresholds (before blocking)
    warnings: {
      coverageDecrease: 5,  // Warn if coverage drops by 5%
      bundleSizeIncrease: 10, // Warn if bundle size increases by 10%
      performanceDecrease: 10, // Warn if performance drops by 10%
    },
    
    // Exemption process
    exemptions: {
      allowEmergencyBypass: true,
      requireJustification: true,
      requireApproval: true,
      maxExemptionDuration: '24h',
    },
  },

  // Monitoring and Reporting
  monitoring: {
    // Quality metrics tracking
    metrics: [
      'test-coverage',
      'code-quality',
      'performance',
      'security-vulnerabilities',
      'accessibility-violations',
    ],
    
    // Reporting frequency
    reports: {
      daily: ['test-coverage', 'security-scan'],
      weekly: ['code-quality', 'performance'],
      monthly: ['accessibility', 'documentation'],
    },
    
    // Alert thresholds
    alerts: {
      coverageDropBelow: 70,
      securityVulnerabilities: 'any',
      performanceDegradation: 15,
      buildFailureRate: 10,
    },
  },
};
