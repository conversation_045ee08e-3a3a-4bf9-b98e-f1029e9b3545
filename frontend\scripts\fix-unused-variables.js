#!/usr/bin/env node

/**
 * Automated Unused Variables Fixer
 * Systematically fixes ESLint no-unused-vars warnings by:
 * 1. Removing unused imports
 * 2. Prefixing unused parameters with underscore
 * 3. Removing unused variable declarations
 * 4. Commenting out unused code blocks
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { execSync } from 'child_process';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class UnusedVariableFixer {
  constructor() {
    this.projectRoot = path.resolve(__dirname, '..');
    this.srcDir = path.join(this.projectRoot, 'src');
    this.fixedFiles = [];
    this.errors = [];
    
    // Patterns for different types of unused variables
    this.patterns = {
      // Unused imports
      unusedImport: /^(\s*)import\s+.*?['"][^'"]*['"];?\s*$/gm,
      
      // Unused function parameters
      unusedParam: /(\w+)(?=\s*[,)])/g,
      
      // Unused variable declarations
      unusedVar: /^(\s*)(const|let|var)\s+(\w+).*?;?\s*$/gm,
      
      // Unused destructured variables
      unusedDestructure: /^(\s*)(const|let|var)\s*{\s*([^}]+)\s*}\s*=.*?;?\s*$/gm,
    };
  }

  /**
   * Get ESLint unused variable warnings
   */
  getUnusedVariables() {
    try {
      console.log('🔍 Running ESLint to identify unused variables...');
      const result = execSync('npm run lint', { 
        cwd: this.projectRoot, 
        encoding: 'utf8',
        stdio: 'pipe'
      });
      return this.parseESLintOutput(result);
    } catch (error) {
      // ESLint returns non-zero exit code when there are warnings/errors
      return this.parseESLintOutput(error.stdout || error.message);
    }
  }

  /**
   * Parse ESLint output to extract unused variable information
   */
  parseESLintOutput(output) {
    const unusedVars = [];
    const lines = output.split('\n');
    
    let currentFile = null;
    
    for (const line of lines) {
      // Check for file path
      const fileMatch = line.match(/^([A-Z]:\\.*\.tsx?)$/);
      if (fileMatch) {
        currentFile = fileMatch[1];
        continue;
      }
      
      // Check for unused variable warning
      const warningMatch = line.match(/^\s*(\d+):(\d+)\s+warning\s+(.+?)\s+@typescript-eslint\/no-unused-vars/);
      if (warningMatch && currentFile) {
        const [, lineNum, colNum, message] = warningMatch;
        unusedVars.push({
          file: currentFile,
          line: parseInt(lineNum),
          column: parseInt(colNum),
          message: message.trim(),
          type: this.categorizeUnusedVar(message)
        });
      }
    }
    
    return unusedVars;
  }

  /**
   * Categorize the type of unused variable
   */
  categorizeUnusedVar(message) {
    if (message.includes('is defined but never used')) {
      if (message.includes('import')) return 'import';
      return 'variable';
    }
    if (message.includes('is assigned a value but never used')) {
      return 'assignment';
    }
    return 'unknown';
  }

  /**
   * Fix unused variables in a file
   */
  async fixFileUnusedVars(filePath, unusedVars) {
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      const lines = content.split('\n');
      let modified = false;
      
      // Sort by line number in descending order to avoid line number shifts
      const sortedVars = unusedVars.sort((a, b) => b.line - a.line);
      
      for (const unusedVar of sortedVars) {
        const lineIndex = unusedVar.line - 1;
        if (lineIndex >= 0 && lineIndex < lines.length) {
          const originalLine = lines[lineIndex];
          const fixedLine = this.fixUnusedVarLine(originalLine, unusedVar);
          
          if (fixedLine !== originalLine) {
            lines[lineIndex] = fixedLine;
            modified = true;
            console.log(`  ✅ Fixed: ${unusedVar.message}`);
          }
        }
      }
      
      if (modified) {
        fs.writeFileSync(filePath, lines.join('\n'));
        this.fixedFiles.push(filePath);
        return true;
      }
      
      return false;
    } catch (error) {
      this.errors.push({ file: filePath, error: error.message });
      console.error(`  ❌ Error fixing ${filePath}: ${error.message}`);
      return false;
    }
  }

  /**
   * Fix a specific line with unused variable
   */
  fixUnusedVarLine(line, unusedVar) {
    const { type, message } = unusedVar;
    
    // Extract variable name from message
    const varNameMatch = message.match(/'([^']+)'/);
    if (!varNameMatch) return line;
    
    const varName = varNameMatch[1];
    
    switch (type) {
      case 'import':
        return this.fixUnusedImport(line, varName);
      
      case 'variable':
        return this.fixUnusedVariable(line, varName);
      
      case 'assignment':
        return this.fixUnusedAssignment(line, varName);
      
      default:
        return this.fixGenericUnused(line, varName);
    }
  }

  /**
   * Fix unused import
   */
  fixUnusedImport(line, varName) {
    // Remove the entire import if it's a single import
    if (line.includes(`import ${varName}`) || line.includes(`import { ${varName} }`)) {
      return ''; // Remove the line
    }
    
    // Remove from destructured import
    if (line.includes(`{ ${varName}`) || line.includes(`${varName} }`)) {
      return line
        .replace(new RegExp(`\\s*,?\\s*${varName}\\s*,?\\s*`, 'g'), '')
        .replace(/{\s*,/, '{')
        .replace(/,\s*}/, '}')
        .replace(/{\s*}/, '{}');
    }
    
    return line;
  }

  /**
   * Fix unused variable declaration
   */
  fixUnusedVariable(line, varName) {
    // If it's a parameter, prefix with underscore
    if (line.includes('(') && line.includes(')')) {
      return line.replace(new RegExp(`\\b${varName}\\b`, 'g'), `_${varName}`);
    }
    
    // If it's a destructured variable, remove it
    if (line.includes('{') && line.includes('}')) {
      return line
        .replace(new RegExp(`\\s*,?\\s*${varName}\\s*,?\\s*`, 'g'), '')
        .replace(/{\s*,/, '{')
        .replace(/,\s*}/, '}');
    }
    
    // Comment out the entire line for standalone variables
    if (line.trim().startsWith('const') || line.trim().startsWith('let') || line.trim().startsWith('var')) {
      return `// ${line.trim()} // Unused variable`;
    }
    
    return line;
  }

  /**
   * Fix unused assignment
   */
  fixUnusedAssignment(line, varName) {
    // Comment out unused assignments
    if (line.includes(`${varName} =`)) {
      return `// ${line.trim()} // Unused assignment`;
    }
    
    return line;
  }

  /**
   * Generic unused variable fix
   */
  fixGenericUnused(line, varName) {
    // Prefix parameters with underscore
    if (line.includes('(') && line.includes(')')) {
      return line.replace(new RegExp(`\\b${varName}\\b`, 'g'), `_${varName}`);
    }
    
    return line;
  }

  /**
   * Run the complete fixing process
   */
  async run() {
    console.log('🚀 Starting unused variables cleanup...\n');
    
    // Get all unused variables
    const unusedVars = this.getUnusedVariables();
    console.log(`📊 Found ${unusedVars.length} unused variable warnings\n`);
    
    if (unusedVars.length === 0) {
      console.log('✅ No unused variables found!');
      return;
    }
    
    // Group by file
    const fileGroups = {};
    for (const unusedVar of unusedVars) {
      if (!fileGroups[unusedVar.file]) {
        fileGroups[unusedVar.file] = [];
      }
      fileGroups[unusedVar.file].push(unusedVar);
    }
    
    // Fix each file
    for (const [filePath, vars] of Object.entries(fileGroups)) {
      console.log(`🔧 Fixing ${path.relative(this.projectRoot, filePath)} (${vars.length} issues):`);
      await this.fixFileUnusedVars(filePath, vars);
      console.log('');
    }
    
    // Summary
    console.log('📋 Summary:');
    console.log(`✅ Fixed files: ${this.fixedFiles.length}`);
    console.log(`❌ Errors: ${this.errors.length}`);
    
    if (this.errors.length > 0) {
      console.log('\n❌ Errors encountered:');
      this.errors.forEach(error => {
        console.log(`  ${error.file}: ${error.error}`);
      });
    }
    
    // Run ESLint again to check results
    console.log('\n🔍 Running ESLint again to verify fixes...');
    try {
      execSync('npm run lint', { cwd: this.projectRoot, stdio: 'inherit' });
    } catch (error) {
      console.log('Some issues may remain - this is expected for complex cases');
    }
  }
}

// Run the fixer
const fixer = new UnusedVariableFixer();
fixer.run().catch(console.error);
