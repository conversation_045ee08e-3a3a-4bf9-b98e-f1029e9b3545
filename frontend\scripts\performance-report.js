#!/usr/bin/env node

/**
 * Performance Report Generator
 * 
 * Generates comprehensive performance reports by running all performance tests
 * and collecting metrics from various sources.
 * 
 * @module PerformanceReportGenerator
 * @version 1.0.0
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

class PerformanceReportGenerator {
  constructor() {
    this.reportData = {
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV || 'development',
      version: this.getPackageVersion(),
      tests: {},
      summary: {},
      recommendations: [],
    };
  }

  /**
   * Get package version from package.json
   */
  getPackageVersion() {
    try {
      const packageJson = JSON.parse(fs.readFileSync(path.join(__dirname, '../package.json'), 'utf8'));
      return packageJson.version;
    } catch (error) {
      return 'unknown';
    }
  }

  /**
   * Run all performance tests and generate report
   */
  async generateReport() {
    console.log('🚀 Starting Performance Report Generation...\n');

    try {
      // Run component performance tests
      await this.runComponentPerformanceTests();

      // Run bundle size analysis
      await this.runBundleSizeTests();

      // Run Lighthouse tests (if build exists)
      await this.runLighthouseTests();

      // Analyze performance budget compliance
      await this.analyzePerformanceBudget();

      // Generate summary and recommendations
      this.generateSummary();

      // Save report
      this.saveReport();

      console.log('\n✅ Performance report generated successfully!');
      console.log(`📊 Report saved to: ${this.getReportPath()}`);

    } catch (error) {
      console.error('❌ Error generating performance report:', error.message);
      process.exit(1);
    }
  }

  /**
   * Run component performance tests
   */
  async runComponentPerformanceTests() {
    console.log('🧪 Running component performance tests...');
    
    try {
      const output = execSync('npm run test:component-perf', { 
        encoding: 'utf8',
        stdio: 'pipe'
      });
      
      this.reportData.tests.componentPerformance = {
        status: 'passed',
        output: this.parseTestOutput(output),
        metrics: this.extractComponentMetrics(output),
      };
      
      console.log('✅ Component performance tests completed');
    } catch (error) {
      this.reportData.tests.componentPerformance = {
        status: 'failed',
        error: error.message,
        output: error.stdout || '',
      };
      
      console.log('⚠️  Component performance tests failed');
    }
  }

  /**
   * Run bundle size tests
   */
  async runBundleSizeTests() {
    console.log('📦 Running bundle size analysis...');
    
    try {
      const output = execSync('npm run test:bundle-size', { 
        encoding: 'utf8',
        stdio: 'pipe'
      });
      
      this.reportData.tests.bundleSize = {
        status: 'passed',
        output: this.parseTestOutput(output),
        metrics: this.extractBundleMetrics(output),
      };
      
      console.log('✅ Bundle size analysis completed');
    } catch (error) {
      this.reportData.tests.bundleSize = {
        status: 'failed',
        error: error.message,
        output: error.stdout || '',
      };
      
      console.log('⚠️  Bundle size analysis failed');
    }
  }

  /**
   * Run Lighthouse tests
   */
  async runLighthouseTests() {
    console.log('🔍 Running Lighthouse performance audit...');
    
    // Check if build exists
    const distPath = path.join(__dirname, '../dist');
    if (!fs.existsSync(distPath)) {
      console.log('⚠️  Build not found, skipping Lighthouse tests');
      this.reportData.tests.lighthouse = {
        status: 'skipped',
        reason: 'Build not found',
      };
      return;
    }
    
    try {
      const output = execSync('npm run test:lighthouse', { 
        encoding: 'utf8',
        stdio: 'pipe',
        timeout: 120000, // 2 minutes timeout
      });
      
      this.reportData.tests.lighthouse = {
        status: 'passed',
        output: this.parseTestOutput(output),
        metrics: this.extractLighthouseMetrics(output),
      };
      
      console.log('✅ Lighthouse audit completed');
    } catch (error) {
      this.reportData.tests.lighthouse = {
        status: 'failed',
        error: error.message,
        output: error.stdout || '',
      };
      
      console.log('⚠️  Lighthouse audit failed');
    }
  }

  /**
   * Analyze performance budget compliance
   */
  async analyzePerformanceBudget() {
    console.log('💰 Analyzing performance budget compliance...');
    
    try {
      const budgetPath = path.join(__dirname, '../performance-budget.json');
      const budget = JSON.parse(fs.readFileSync(budgetPath, 'utf8'));
      
      this.reportData.budget = {
        thresholds: budget,
        compliance: this.checkBudgetCompliance(budget),
      };
      
      console.log('✅ Performance budget analysis completed');
    } catch (error) {
      console.log('⚠️  Performance budget analysis failed:', error.message);
      this.reportData.budget = {
        status: 'failed',
        error: error.message,
      };
    }
  }

  /**
   * Check budget compliance based on test results
   */
  checkBudgetCompliance(budget) {
    const compliance = {
      overall: 'unknown',
      details: {},
    };

    // Check Lighthouse scores against budget
    if (this.reportData.tests.lighthouse?.metrics) {
      const lighthouse = this.reportData.tests.lighthouse.metrics;
      const budgetLighthouse = budget.lighthouse;
      
      compliance.details.lighthouse = {
        performance: {
          actual: lighthouse.performance,
          threshold: budgetLighthouse.performance,
          compliant: lighthouse.performance >= budgetLighthouse.performance,
        },
        accessibility: {
          actual: lighthouse.accessibility,
          threshold: budgetLighthouse.accessibility,
          compliant: lighthouse.accessibility >= budgetLighthouse.accessibility,
        },
        bestPractices: {
          actual: lighthouse.bestPractices,
          threshold: budgetLighthouse['best-practices'],
          compliant: lighthouse.bestPractices >= budgetLighthouse['best-practices'],
        },
        seo: {
          actual: lighthouse.seo,
          threshold: budgetLighthouse.seo,
          compliant: lighthouse.seo >= budgetLighthouse.seo,
        },
      };
    }

    // Check bundle sizes against budget
    if (this.reportData.tests.bundleSize?.metrics) {
      const bundle = this.reportData.tests.bundleSize.metrics;
      const budgetSizes = budget.budgets[0].resourceSizes;
      
      compliance.details.bundleSize = {};
      
      for (const resourceType of budgetSizes) {
        const threshold = resourceType.maximumSizeKb * 1024;
        let actual = 0;
        
        switch (resourceType.resourceType) {
          case 'total':
            actual = bundle.totalSize;
            break;
          case 'script':
            actual = bundle.scriptSize;
            break;
          case 'stylesheet':
            actual = bundle.stylesheetSize;
            break;
          // Add other resource types as needed
        }
        
        compliance.details.bundleSize[resourceType.resourceType] = {
          actual: actual,
          threshold: threshold,
          compliant: actual <= threshold,
        };
      }
    }

    // Determine overall compliance
    const allCompliant = Object.values(compliance.details).every(category =>
      Object.values(category).every(item => item.compliant !== false)
    );
    
    compliance.overall = allCompliant ? 'compliant' : 'non-compliant';
    
    return compliance;
  }

  /**
   * Generate summary and recommendations
   */
  generateSummary() {
    console.log('📋 Generating summary and recommendations...');
    
    const summary = {
      totalTests: Object.keys(this.reportData.tests).length,
      passedTests: Object.values(this.reportData.tests).filter(t => t.status === 'passed').length,
      failedTests: Object.values(this.reportData.tests).filter(t => t.status === 'failed').length,
      skippedTests: Object.values(this.reportData.tests).filter(t => t.status === 'skipped').length,
      overallStatus: 'unknown',
    };

    // Determine overall status
    if (summary.failedTests === 0) {
      summary.overallStatus = summary.skippedTests > 0 ? 'partial' : 'passed';
    } else {
      summary.overallStatus = 'failed';
    }

    this.reportData.summary = summary;

    // Generate recommendations
    this.generateRecommendations();
  }

  /**
   * Generate performance recommendations
   */
  generateRecommendations() {
    const recommendations = [];

    // Analyze failed tests for recommendations
    Object.entries(this.reportData.tests).forEach(([testName, testResult]) => {
      if (testResult.status === 'failed') {
        switch (testName) {
          case 'componentPerformance':
            recommendations.push('Optimize React component rendering with React.memo, useMemo, and useCallback');
            break;
          case 'bundleSize':
            recommendations.push('Implement code splitting and tree shaking to reduce bundle size');
            break;
          case 'lighthouse':
            recommendations.push('Improve Core Web Vitals scores through performance optimizations');
            break;
        }
      }
    });

    // Analyze budget compliance for recommendations
    if (this.reportData.budget?.compliance?.details) {
      const compliance = this.reportData.budget.compliance.details;
      
      if (compliance.lighthouse) {
        Object.entries(compliance.lighthouse).forEach(([metric, data]) => {
          if (!data.compliant) {
            switch (metric) {
              case 'performance':
                recommendations.push('Optimize loading performance and reduce JavaScript execution time');
                break;
              case 'accessibility':
                recommendations.push('Improve accessibility compliance with ARIA labels and semantic HTML');
                break;
              case 'bestPractices':
                recommendations.push('Follow web development best practices for security and performance');
                break;
              case 'seo':
                recommendations.push('Optimize SEO with proper meta tags and structured data');
                break;
            }
          }
        });
      }
      
      if (compliance.bundleSize) {
        Object.entries(compliance.bundleSize).forEach(([resourceType, data]) => {
          if (!data.compliant) {
            switch (resourceType) {
              case 'script':
                recommendations.push('Reduce JavaScript bundle size through code splitting and optimization');
                break;
              case 'stylesheet':
                recommendations.push('Optimize CSS bundle size by removing unused styles');
                break;
              case 'total':
                recommendations.push('Reduce overall bundle size across all resource types');
                break;
            }
          }
        });
      }
    }

    this.reportData.recommendations = [...new Set(recommendations)]; // Remove duplicates
  }

  /**
   * Parse test output for key information
   */
  parseTestOutput(output) {
    const lines = output.split('\n');
    return {
      summary: lines.filter(line => line.includes('✓') || line.includes('✗') || line.includes('Test')).slice(0, 10),
      errors: lines.filter(line => line.includes('Error') || line.includes('Failed')).slice(0, 5),
    };
  }

  /**
   * Extract component performance metrics from test output
   */
  extractComponentMetrics(output) {
    // This would parse actual metrics from test output
    // For now, return placeholder structure
    return {
      renderTime: 'N/A',
      memoryUsage: 'N/A',
      reRenderCount: 'N/A',
    };
  }

  /**
   * Extract bundle size metrics from test output
   */
  extractBundleMetrics(output) {
    // This would parse actual metrics from test output
    // For now, return placeholder structure
    return {
      totalSize: 'N/A',
      scriptSize: 'N/A',
      stylesheetSize: 'N/A',
      compressionRatio: 'N/A',
    };
  }

  /**
   * Extract Lighthouse metrics from test output
   */
  extractLighthouseMetrics(output) {
    // This would parse actual metrics from test output
    // For now, return placeholder structure
    return {
      performance: 'N/A',
      accessibility: 'N/A',
      bestPractices: 'N/A',
      seo: 'N/A',
      firstContentfulPaint: 'N/A',
      largestContentfulPaint: 'N/A',
    };
  }

  /**
   * Save report to file
   */
  saveReport() {
    const reportPath = this.getReportPath();
    const reportDir = path.dirname(reportPath);
    
    // Ensure reports directory exists
    if (!fs.existsSync(reportDir)) {
      fs.mkdirSync(reportDir, { recursive: true });
    }
    
    // Save JSON report
    fs.writeFileSync(reportPath, JSON.stringify(this.reportData, null, 2));
    
    // Generate HTML report
    this.generateHtmlReport();
  }

  /**
   * Get report file path
   */
  getReportPath() {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    return path.join(__dirname, '../reports', `performance-report-${timestamp}.json`);
  }

  /**
   * Generate HTML report
   */
  generateHtmlReport() {
    const htmlPath = this.getReportPath().replace('.json', '.html');
    
    const html = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Performance Report - ${this.reportData.timestamp}</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #f5f5f5; padding: 20px; border-radius: 8px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 8px; }
        .passed { color: green; }
        .failed { color: red; }
        .skipped { color: orange; }
        .metric { display: flex; justify-content: space-between; margin: 5px 0; }
        .recommendation { background: #fff3cd; padding: 10px; margin: 5px 0; border-radius: 4px; }
    </style>
</head>
<body>
    <div class="header">
        <h1>Performance Report</h1>
        <p><strong>Generated:</strong> ${this.reportData.timestamp}</p>
        <p><strong>Environment:</strong> ${this.reportData.environment}</p>
        <p><strong>Version:</strong> ${this.reportData.version}</p>
    </div>
    
    <div class="section">
        <h2>Summary</h2>
        <div class="metric"><span>Total Tests:</span><span>${this.reportData.summary.totalTests}</span></div>
        <div class="metric"><span>Passed:</span><span class="passed">${this.reportData.summary.passedTests}</span></div>
        <div class="metric"><span>Failed:</span><span class="failed">${this.reportData.summary.failedTests}</span></div>
        <div class="metric"><span>Skipped:</span><span class="skipped">${this.reportData.summary.skippedTests}</span></div>
        <div class="metric"><span>Overall Status:</span><span class="${this.reportData.summary.overallStatus}">${this.reportData.summary.overallStatus}</span></div>
    </div>
    
    <div class="section">
        <h2>Recommendations</h2>
        ${this.reportData.recommendations.map(rec => `<div class="recommendation">${rec}</div>`).join('')}
    </div>
    
    <div class="section">
        <h2>Detailed Results</h2>
        <pre>${JSON.stringify(this.reportData, null, 2)}</pre>
    </div>
</body>
</html>`;
    
    fs.writeFileSync(htmlPath, html);
  }
}

// Run the report generator
if (require.main === module) {
  const generator = new PerformanceReportGenerator();
  generator.generateReport().catch(console.error);
}

module.exports = PerformanceReportGenerator;
