import React, { useState, useEffect } from 'react';
import {
  KeyIcon,
  PlusIcon,
  ClipboardIcon,
  TrashIcon,
  CheckIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';
import { functions } from '@/config/firebase';
import { httpsCallable } from 'firebase/functions';
import { useAuth } from '@/contexts/AuthContext';
import { BaseComponentProps, TimestampType } from '@/components/common/types';

interface APIKey {
  id: string;
  name: string;
  tier: string;
  status: 'active' | 'inactive' | 'expired';
  created_at: TimestampType;
  last_used: TimestampType | null;
  expires_at: TimestampType | null;
  permissions: string[];
  usage_stats: {
    total_requests: number;
    requests_today: number;
  };
}

interface APIKeyResponse {
  success: boolean;
  api_keys?: APIKey[];
  api_key?: APIKey;
  message?: string;
  error?: string;
}

interface CreateAPIKeyRequest {
  name: string;
  tier: string;
  permissions: string[];
  expires_in_days?: number;
}

type APIKeyManagerProps = BaseComponentProps;

const APIKeyManager: React.FC<APIKeyManagerProps> = ({) => {
  const { currentUser } = useAuth();
  const [apiKeys, setApiKeys] = useState<APIKey[]>([]);
  const [loading, setLoading] = useState(true);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [creating, setCreating] = useState(false);
  const [newKeyData, setNewKeyData] = useState<string | null>(null);
  const [copied, setCopied] = useState<string | null>(null);
  const [createForm, setCreateForm] = useState({
    name: '',
    tier: 'free',
    expires_in_days: '',
    permissions: ['read', 'write']
  });

  useEffect(() => {
    if (currentUser) {
      loadAPIKeys();
    }
  }, [currentUser]);

  const loadAPIKeys = async () => {
    try {
      setLoading(true);
      const getUserAPIKeys = httpsCallable<voAPIKeyResponse>(functions, 'get_user_api_keys');
      const result = await getUserAPIKeys();
      const data = result.data;

      if (data.success && data.api_keys) {
        setApiKeys(data.api_keys);
      }
    } catch (error) {
      console.error('Error loading API keys:', error);
    } finally {
      setLoading(false);
    }
  };

  // TODO: Add try-catch error handling to this async handler
  const handleCreateAPIKey = async () => {
    if (!createForm.name.trim()) return;

    try {
      setCreating(true);
      const generateAPIKey = httpsCallable<CreateAPIKeyRequest, APIKeyResponse>(functions, 'generate_api_key');

      const requestData: CreateAPIKeyRequest = {
        name: createForm.name,
        tier: createForm.tier,
        permissions: createForm.permissions
      };

      if (createForm.expires_in_days) {
        requestData.expires_in_days = parseInt(createForm.expires_in_days);
      }

      const result = await generateAPIKey(requestData);
      const data = result.data;
      
      if (data.success) {
        setNewKeyData(data.api_key);
        setCreateForm({
          name: '',
          tier: 'free',
          expires_in_days: '',
          permissions: ['read', 'write']
        });
        setShowCreateForm(false);
        loadAPIKeys(); // Reload the list
      } else {
        throw new Error(data.error || 'Failed to create API key');
      }
    } catch (error) {
      console.error('Error creating API key:', error);
      alert('Failed to create API key. Please try again.');
    } finally {
      setCreating(false);
    }
  };

  // TODO: Add try-catch error handling to this async handler
  const handleRevokeAPIKey = async (keyId: string) => {
    if (!confirm('Are you sure you want to revoke this API key? This action cannot be undone.')) {
      return;
    }
    
    try {
      const revokeAPIKey = httpsCallable(functions, 'revoke_api_key');
      const result = await revokeAPIKey({ key_: keyId });
      const data = result.data as any;
      
      if (data.success) {
        loadAPIKeys(); // Reload the list
      } else {
        throw new Error(data.error || 'Failed to revoke API key');
      }
    } catch (error) {
      console.error('Error revoking API key:', error);
      alert('Failed to revoke API key. Please try again.');
    }
  };

  const copyToClipboard = async (text: string, keyId: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopied(keyId);
      setTimeout(() => setCopied(null), 2000);
    } catch (error) {
      console.error('Failed to copy to clipboard:', error);
    }
  };

  const formatDate = (timestamp: TimestampType | null | undefined): string => {
    if (!timestamp) return 'Never';

    let date: Date;
    if (typeof timestamp === 'object' && timestamp !== null && 'toDate' in timestamp) {
      // Firebase Timestamp
      date = timestamp.toDate();
    } else if (timestamp instanceof Date) {
      date = timestamp;
    } else if (typeof timestamp === 'string' || typeof timestamp === 'number') {
      date = new Date(timestamp);
    } else {
      return 'Never';
    }

    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const getTierColor = (tier: string) => {
    const colors = {
      free: 'bg-gray-100 text-gray-800',
      pro: 'bg-blue-100 text-blue-800',
      enterprise: 'bg-purple-100 text-purple-800'
    };
    return colors[tier as keyof typeof colors] || colors.free;
  };

  const getStatusColor = (status: string) => {
    const colors = {
      active: 'bg-green-100 text-green-800',
      revoked: 'bg-red-100 text-red-800',
      expired: 'bg-yellow-100 text-yellow-800',
      suspended: 'bg-orange-100 text-orange-800'
    };
    return colors[status as keyof typeof colors] || colors.active;
  };

  return (
    <div="space-y-6">
      {/* Header */}
      <div="flex items-center justify-between">
        <div>
          <h2="text-2xl font-bold text-gray-900">API Keys</h2>
          <p="text-sm text-gray-500">
            Manage your API keys for programmatic access to the RAG Prompt Library
          </p>
        </div>
        <button
          onClick={() => setShowCreateForm(true)}="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          <PlusIcon="h-4 w-4 mr-2" />
          Create API Key
        </button>
      </div>

      {/* New API Key Display */}
      {newKeyData && (
        <div="bg-green-50 border border-green-200 rounded-md p-4">
          <div="flex items-start">
            <CheckIcon="h-5 w-5 text-green-400 mt-0.5" />
            <div="ml-3 flex-1">
              <h3="text-sm font-medium text-green-800">
                API Key Created Successfully
              </h3>
              <div="mt-2">
                <p="text-sm text-green-700 mb-2">
                  Please copy your API key now. You won't be able to see it again!
                </p>
                <div="flex items-center space-x-2">
                  <code="flex-1 bg-white border border-green-300 rounded px-3 py-2 text-sm font-mono text-gray-900 break-all">
                    {newKeyData}
                  </code>
                  <button
                    onClick={() => copyToClipboard(newKeyData, 'new-key')}="inline-flex items-center px-3 py-2 border border-green-300 text-sm font-medium rounded-md text-green-700 bg-green-100 hover:bg-green-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                  >
                    {copied === 'new-key' ? (
                      <>
                        <CheckIcon="h-4 w-4 mr-1" />
                        Copied!
                      </>
                    ) : (
                      <>
                        <ClipboardIcon="h-4 w-4 mr-1" />
                        Copy
                      </>
                    )}
                  </button>
                </div>
              </div>
              <button
                onClick={() => setNewKeyData(null)}="mt-3 text-sm text-green-600 hover:text-green-800"
              >
                Dismiss
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Create Form */}
      {showCreateForm && (
        <div="bg-white shadow rounded-lg p-6">
          <h3="text-lg font-medium text-gray-900 mb-4">Create New API Key</h3>
          
          <div="space-y-4">
            <div>
              <label htmlFor="key-name"="block text-sm font-medium text-gray-700">
                Name *
              </label>
              <input
                type="text"="key-name"
                value={createForm.name}
                onChange={(e) => setCreateForm({ ...createForm, name: e.target.value })}="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                placeholder="e.g., Production API, Mobile App"
              />
            </div>

            <div>
              <label htmlFor="key-tier"="block text-sm font-medium text-gray-700">
                Tier
              </label>
              <select="key-tier"
                value={createForm.tier}
                onChange={(e) => setCreateForm({ ...createForm, tier: e.target.value })}="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="free">Free (60 req/min)</option>
                <option value="pro">Pro (300 req/min)</option>
                <option value="enterprise">Enterprise (1000 req/min)</option>
              </select>
            </div>

            <div>
              <label htmlFor="expires-in"="block text-sm font-medium text-gray-700">
                Expires in (days, optional)
              </label>
              <input
                type="number"="expires-in"
                value={createForm.expires_in_days}
                onChange={(e) => setCreateForm({ ...createForm, expires_in_days: e.target.value })}="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                placeholder="Leave empty for no expiration"
                min="1"
                max="365"
              />
            </div>

            <div>
              <label="block text-sm font-medium text-gray-700 mb-2">
                Permissions
              </label>
              <div="space-y-2">
                {['read', 'write', 'delete'].map((permission) => (
                  <label key={permission}="flex items-center">
                    <input
                      type="checkbox"
                      checked={createForm.permissions.includes(permission)}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setCreateForm({
                            ...createForm,
                            permissions: [...createForm.permissions, permission]
                          });
                        } else {
                          setCreateForm({
                            ...createForm,
                            permissions: createForm.permissions.filter(p => p !== permission)
                          });
                        }
                      }}="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <span="ml-2 text-sm text-gray-700 capitalize">{permission}</span>
                  </label>
                ))}
              </div>
            </div>
          </div>

          <div="mt-6 flex justify-end space-x-3">
            <button
              onClick={() => setShowCreateForm(false)}
              disabled={creating}="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
            >
              Cancel
            </button>
            <button
              onClick={handleCreateAPIKey}
              disabled={!createForm.name.trim() || creating}="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {creating ? (
                <>
                  <div="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2 inline-block"></div>
                  Creating...
                </>
              ) : (
                'Create API Key'
              )}
            </button>
          </div>
        </div>
      )}

      {/* API Keys List */}
      <div="bg-white shadow rounded-lg">
        <div="px-6 py-4 border-b border-gray-200">
          <h3="text-lg font-medium text-gray-900">Your API Keys</h3>
        </div>
        
        {loading ? (
          <div="p-6 text-center">
            <div="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p="mt-2 text-sm text-gray-500">Loading API keys...</p>
          </div>
        ) : apiKeys.length === 0 ? (
          <div="p-6 text-center">
            <KeyIcon="mx-auto h-12 w-12 text-gray-400" />
            <h3="mt-2 text-sm font-medium text-gray-900">No API keys</h3>
            <p="mt-1 text-sm text-gray-500">
              Create your first API key to get started with programmatic access.
            </p>
          </div>
        ) : (
          <div="dive-y dive-gray-200">
            {apiKeys.map((apiKey) => (
              <div key={apiKey.}="p-6">
                <div="flex items-center justify-between">
                  <div="flex-1">
                    <div="flex items-center space-x-3">
                      <h4="text-lg font-medium text-gray-900">{apiKey.name}</h4>
                      <span={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getTierColor(apiKey.tier)}`}>
                        {apiKey.tier}
                      </span>
                      <span={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(apiKey.status)}`}>
                        {apiKey.status}
                      </span>
                    </div>
                    
                    <div="mt-2 grgr-cols-1 md:gr-cols-3 gap-4 text-sm text-gray-500">
                      <div>
                        <span="font-medium">Created:</span> {formatDate(apiKey.created_at)}
                      </div>
                      <div>
                        <span="font-medium">Last used:</span> {formatDate(apiKey.last_used)}
                      </div>
                      <div>
                        <span="font-medium">Requests:</span> {apiKey.usage_stats?.total_requests || 0} total
                      </div>
                    </div>
                    
                    {apiKey.expires_at && (
                      <div="mt-1 text-sm text-yellow-600">
                        <ExclamationTriangleIcon="h-4 w-4 inline mr-1" />
                        Expires: {formatDate(apiKey.expires_at)}
                      </div>
                    )}
                    
                    <div="mt-2">
                      <span="text-sm text-gray-500">Permissions: </span>
                      <span="text-sm text-gray-700">
                        {apiKey.permissions?.join(', ') || 'read, write'}
                      </span>
                    </div>
                  </div>
                  
                  <div="flex items-center space-x-2">
                    {apiKey.status === 'active' && (
                      <button
                        onClick={() => handleRevokeAPIKey(apiKey.)}="inline-flex items-center px-3 py-1 border border-red-300 text-sm font-medium rounded-md text-red-700 bg-red-50 hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                      >
                        <TrashIcon="h-4 w-4 mr-1" />
                        Revoke
                      </button>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default APIKeyManager;
