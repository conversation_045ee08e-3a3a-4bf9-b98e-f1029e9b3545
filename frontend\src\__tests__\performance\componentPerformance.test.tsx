/**
 * Component Performance Tests
 * 
 * Tests component rendering performance, memory usage, and optimization effectiveness.
 * Validates that performance optimizations (React.memo, useMemo, useCallback) are working correctly.
 * 
 * @module ComponentPerformanceTests
 * @version 1.0.0
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { render, screen, fireEvent, cleanup } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import React from 'react';

// Performance monitoring utilities
import { PerformanceProfiler } from '@/utils/performanceProfiler';

// Mock components for testing
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });

  return (
    <QueryClientProvider client={queryClient}>
      <BrowserRouter>
        {children}
      </BrowserRouter>
    </QueryClientProvider>
  );
};

// Mock heavy component for performance testing
const HeavyComponent: React.FC<{ items: number; onRender?: () => void }> = React.memo(({ items, onRender }) => {
  React.useEffect(() => {
    onRender?.();
  });

  const expensiveCalculation = React.useMemo(() => {
    // Simulate expensive calculation
    let result = 0;
    for (let i = 0; i < items * 1000; i++) {
      result += Math.random();
    }
    return result;
  }, [items]);

  return (
    <div data-testid="heavy-component">
      <h2>Heavy Component</h2>
      <p>Items: {items}</p>
      <p>Calculation Result: {expensiveCalculation.toFixed(2)}</p>
      <div>
        {Array.from({ length: items }, (_, i) => (
          <div key={i} data-testid={`item-${i}`}>
            Item {i}
          </div>
        ))}
      </div>
    </div>
  );
});

// Mock list component for virtualization testing
const VirtualizedList: React.FC<{ items: string[]; onRender?: () => void }> = React.memo(({ items, onRender }) => {
  const [visibleRange, setVisibleRange] = React.useState({ start: 0, end: 10 });

  React.useEffect(() => {
    onRender?.();
  });

  const visibleItems = React.useMemo(() => {
    return items.slice(visibleRange.start, visibleRange.end);
  }, [items, visibleRange]);

  const handleScroll = React.useCallback((event: React.UIEvent<HTMLDivElement>) => {
    const scrollTop = event.currentTarget.scrollTop;
    const itemHeight = 50;
    const containerHeight = 500;
    
    const start = Math.floor(scrollTop / itemHeight);
    const end = Math.min(start + Math.ceil(containerHeight / itemHeight), items.length);
    
    setVisibleRange({ start, end });
  }, [items.length]);

  return (
    <div 
      data-testid="virtualized-list"
      style={{ height: '500px', overflow: 'auto' }}
      onScroll={handleScroll}
    >
      <div style={{ height: items.length * 50 }}>
        <div style={{ transform: `translateY(${visibleRange.start * 50}px)` }}>
          {visibleItems.map((item, index) => (
            <div 
              key={visibleRange.start + index} 
              data-testid={`list-item-${visibleRange.start + index}`}
              style={{ height: '50px', padding: '10px' }}
            >
              {item}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
});

describe('Component Performance Tests', () => {
  let performanceProfiler: PerformanceProfiler;

  beforeEach(() => {
    performanceProfiler = new PerformanceProfiler();
    vi.clearAllMocks();
  });

  afterEach(() => {
    cleanup();
  });

  describe('React.memo Optimization', () => {
    it('should prevent unnecessary re-renders with React.memo', async () => {
      let renderCount = 0;
      const onRender = () => renderCount++;

      const { rerender } = render(
        <TestWrapper>
          <HeavyComponent items={10} onRender={onRender} />
        </TestWrapper>
      );

      // Initial render
      expect(renderCount).toBe(1);

      // Re-render with same props (should not trigger re-render due to React.memo)
      rerender(
        <TestWrapper>
          <HeavyComponent items={10} onRender={onRender} />
        </TestWrapper>
      );

      expect(renderCount).toBe(1); // Should still be 1

      // Re-render with different props (should trigger re-render)
      rerender(
        <TestWrapper>
          <HeavyComponent items={20} onRender={onRender} />
        </TestWrapper>
      );

      expect(renderCount).toBe(2); // Should now be 2
    });

    it('should measure render time performance', async () => {
      const startTime = performance.now();

      render(
        <TestWrapper>
          <HeavyComponent items={100} />
        </TestWrapper>
      );

      const endTime = performance.now();
      const renderTime = endTime - startTime;

      // Render should complete within reasonable time (adjust threshold as needed)
      expect(renderTime).toBeLessThan(100); // 100ms threshold

      // Verify component rendered correctly
      expect(screen.getByTestId('heavy-component')).toBeInTheDocument();
      expect(screen.getByText('Items: 100')).toBeInTheDocument();
    });
  });

  describe('useMemo Optimization', () => {
    it('should cache expensive calculations with useMemo', async () => {
      const { rerender } = render(
        <TestWrapper>
          <HeavyComponent items={50} />
        </TestWrapper>
      );

      const firstResult = screen.getByText(/Calculation Result:/);
      const firstValue = firstResult.textContent;

      // Re-render with same items (useMemo should return cached result)
      rerender(
        <TestWrapper>
          <HeavyComponent items={50} />
        </TestWrapper>
      );

      const secondResult = screen.getByText(/Calculation Result:/);
      const secondValue = secondResult.textContent;

      // Values should be identical due to memoization
      expect(firstValue).toBe(secondValue);
    });

    it('should recalculate when dependencies change', async () => {
      const { rerender } = render(
        <TestWrapper>
          <HeavyComponent items={25} />
        </TestWrapper>
      );

      const firstResult = screen.getByText(/Calculation Result:/);

      // Re-render with different items (should trigger recalculation)
      rerender(
        <TestWrapper>
          <HeavyComponent items={75} />
        </TestWrapper>
      );

      const secondResult = screen.getByText(/Calculation Result:/);

      // Results should be different due to different input
      expect(firstResult.textContent).not.toBe(secondResult.textContent);
    });
  });

  describe('Virtualization Performance', () => {
    it('should handle large lists efficiently with virtualization', async () => {
      const largeItemList = Array.from({ length: 10000 }, (_, i) => `Item ${i}`);
      
      const startTime = performance.now();

      render(
        <TestWrapper>
          <VirtualizedList items={largeItemList} />
        </TestWrapper>
      );

      const endTime = performance.now();
      const renderTime = endTime - startTime;

      // Should render quickly even with large dataset
      expect(renderTime).toBeLessThan(200); // 200ms threshold for 10k items

      // Should only render visible items initially
      const visibleItems = screen.getAllByTestId(/list-item-/);
      expect(visibleItems.length).toBeLessThanOrEqual(15); // Should be around 10-15 visible items

      // Verify virtualized list is present
      expect(screen.getByTestId('virtualized-list')).toBeInTheDocument();
    });

    it('should update visible items on scroll', async () => {
      const items = Array.from({ length: 1000 }, (_, i) => `Item ${i}`);

      render(
        <TestWrapper>
          <VirtualizedList items={items} />
        </TestWrapper>
      );

      const listContainer = screen.getByTestId('virtualized-list');

      // Initial state - should show items 0-9
      expect(screen.getByTestId('list-item-0')).toBeInTheDocument();

      // Simulate scroll
      fireEvent.scroll(listContainer, { target: { scrollTop: 500 } });

      // After scroll, should show different items
      // Note: This is a simplified test - in real implementation, 
      // you'd need to wait for the scroll handler to update the visible range
      expect(listContainer).toBeInTheDocument();
    });
  });

  describe('Memory Usage Monitoring', () => {
    it('should monitor memory usage during component lifecycle', async () => {
      const initialMemory = (performance as any).memory?.usedJSHeapSize || 0;

      const { unmount } = render(
        <TestWrapper>
          <HeavyComponent items={200} />
        </TestWrapper>
      );

      const afterRenderMemory = (performance as any).memory?.usedJSHeapSize || 0;

      // Unmount component
      unmount();

      // Force garbage collection if available (Chrome DevTools)
      if ((window as any).gc) {
        (window as any).gc();
      }

      const afterUnmountMemory = (performance as any).memory?.usedJSHeapSize || 0;

      // Memory should not increase dramatically
      const memoryIncrease = afterRenderMemory - initialMemory;
      expect(memoryIncrease).toBeLessThan(10 * 1024 * 1024); // Less than 10MB increase

      // Memory should be released after unmount (with some tolerance)
      if (afterUnmountMemory > 0) {
        const memoryRetained = afterUnmountMemory - initialMemory;
        expect(memoryRetained).toBeLessThan(5 * 1024 * 1024); // Less than 5MB retained
      }
    });
  });

  describe('Performance Profiling Integration', () => {
    it('should integrate with performance profiler', async () => {
      const componentName = 'HeavyComponent';
      
      performanceProfiler.startProfiling(componentName);

      render(
        <TestWrapper>
          <HeavyComponent items={50} />
        </TestWrapper>
      );

      const metrics = performanceProfiler.stopProfiling(componentName);

      expect(metrics).toBeDefined();
      expect(metrics.componentName).toBe(componentName);
      expect(metrics.renderTime).toBeGreaterThan(0);
      expect(metrics.renderTime).toBeLessThan(1000); // Should complete within 1 second
    });

    it('should track multiple component renders', async () => {
      const componentName = 'MultipleRenders';
      
      performanceProfiler.startProfiling(componentName);

      // Render multiple times
      const { rerender } = render(
        <TestWrapper>
          <HeavyComponent items={10} />
        </TestWrapper>
      );

      rerender(
        <TestWrapper>
          <HeavyComponent items={20} />
        </TestWrapper>
      );

      rerender(
        <TestWrapper>
          <HeavyComponent items={30} />
        </TestWrapper>
      );

      const metrics = performanceProfiler.stopProfiling(componentName);

      expect(metrics).toBeDefined();
      expect(metrics.renderCount).toBeGreaterThanOrEqual(3);
    });
  });

  describe('Bundle Size Impact', () => {
    it('should verify component tree-shaking effectiveness', async () => {
      // This test would typically be run as part of build analysis
      // Here we simulate checking that unused components are not included
      
      render(
        <TestWrapper>
          <HeavyComponent items={10} />
        </TestWrapper>
      );

      // Verify only necessary components are rendered
      expect(screen.getByTestId('heavy-component')).toBeInTheDocument();
      
      // Verify no unexpected components are present
      expect(screen.queryByTestId('unused-component')).not.toBeInTheDocument();
    });
  });
});
