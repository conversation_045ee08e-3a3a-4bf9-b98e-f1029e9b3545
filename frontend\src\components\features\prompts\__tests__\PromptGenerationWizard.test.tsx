import { describe, it, expect, vi, beforeEach } from 'vitest';
import { screencleanup } from '@testing-library/re';
import { renderWithProviders } from '../../../test/test-utils';
import userEvent from '@testing-library/user-event';
import { PromptGenerationWizard } from '../PromptGenerationWizard';
import type {} from '@/types';

afterEach(() => {
    cleanup();
  });

  describe('PromptGenerationWizard', () => {
  const mockOnGenerate = vi.fn();
  const mockOnCancel = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  const renderWizard = (props = {}) => {
    return renderWithProviders(
      <PromptGenerationWizard
        onGenerate={mockOnGenerate}
        onCancel={mockOnCancel}
        loading={false}
        {...props}
      />
    );
  };

  afterEach(() => {
    cleanup();
  });

  describe('Basic Info Step', () => {
    it('should render the basic info step initially', () => {
      renderWizard();

      expect(screen.getByText('Tell us about your prompt')).toBeInTheDocument();
      expect(screen.getByLabelText(/What is the purpose of your prompt/)).toBeInTheDocument();
      expect(screen.getByLabelText(/Industry/)).toBeInTheDocument();
      expect(screen.getByLabelText(/Use Case/)).toBeInTheDocument();
    });

    it('should show validation errors for required fields', async () => {
      const user = userEvent.setup();
      renderWizard();

      const nextButton = screen.getAllByRole('button', { name: /next/i })[0];
      await user.click(nextButton);

      expect(screen.getByText('Purpose is required')).toBeInTheDocument();
      expect(screen.getByText('Industry is required')).toBeInTheDocument();
      expect(screen.getByText('Use case is required')).toBeInTheDocument();
    });

    it('should proceed to next step when valid data is entered', async () => {
      const user = userEvent.setup();
      renderWizard();

      // Fill in required fields
      await user.type(
        screen.getByLabelText(/What is the purpose of your prompt/),
        'Generate customer support responses'
      );
      await user.selectOptions(screen.getByLabelText(/Industry/), 'Technology');
      await user.selectOptions(screen.getByLabelText(/Use Case/), 'Customer Support');

      const nextButton = screen.getAllByRole('button', { name: /next/i })[0];
      await user.click(nextButton);

      expect(screen.getByText('Define Input Variables')).toBeInTheDocument();
    });

    it('should call onCancel when cancel button is clicked', async () => {
      const user = userEvent.setup();
      renderWizard();

      const cancelButton = screen.getByRole('button', { name: /cancel/i });
      await user.click(cancelButton);

      expect(mockOnCancel).toHaveBeenCalled();
    });
  });

  afterEach(() => {
    cleanup();
  });

  describe('Variables Step', () => {
    beforeEach(async () => {
      const user = userEvent.setup();
      renderWizard();

      // Navigate to variables step
      await user.type(
        screen.getByLabelText(/What is the purpose of your prompt/),
        'Test purpose'
      );
      await user.selectOptions(screen.getByLabelText(/Industry/), 'Technology');
      await user.selectOptions(screen.getByLabelText(/Use Case/), 'Other');
      await user.click(screen.getAllByRole('button', { name: /next/i })[0]);
    });

    it('should show empty state when no variables are defined', () => {
      expect(screen.getByText('No variables defined yet')).toBeInTheDocument();
      expect(screen.getByText(/Variables make your prompts flexible/)).toBeInTheDocument();
    });

    it('should add a new variable when Add Variable button is clicked', async () => {
      const user = userEvent.setup();

      const addButton = screen.getByRole('button', { name: /add variable/i });
      await user.click(addButton);

      expect(screen.getByText('Variable 1')).toBeInTheDocument();
      expect(screen.getByLabelText(/Variable Name/)).toBeInTheDocument();
      expect(screen.getByLabelText(/Description/)).toBeInTheDocument();
    });

    it('should remove a variable when delete button is clicked', async () => {
      const user = userEvent.setup();

      // Add a variable
      const addButton = screen.getByRole('button', { name: /add variable/i });
      await user.click(addButton);

      expect(screen.getByText('Variable 1')).toBeInTheDocument();

      // Remove the variable
      const deleteButton = screen.getByRole('button', { name: '' }); // Trash icon button
      await user.click(deleteButton);

      expect(screen.queryByText('Variable 1')).not.toBeInTheDocument();
      expect(screen.getByText('No variables defined yet')).toBeInTheDocument();
    });

    it('should update variable properties', async () => {
      const user = userEvent.setup();

      // Add a variable
      const addButton = screen.getByRole('button', { name: /add variable/i });
      await user.click(addButton);

      // Update variable name
      const nameInput = screen.getByLabelText(/Variable Name/);
      await user.type(nameInput, 'customer_name');

      // Update description
      const descriptionInput = screen.getByLabelText(/Description/);
      await user.type(descriptionInput, 'Name of the customer');

      // Update type
      const typeSelect = screen.getByDisplayValue('Text');
      await user.selectOptions(typeSelect, 'string');

      expect(nameInput).toHaveValue('customer_name');
      expect(descriptionInput).toHaveValue('Name of the customer');
    });
  });

  afterEach(() => {
    cleanup();
  });

  describe('Preferences Step', () => {
    beforeEach(async () => {
      const user = userEvent.setup();
      renderWizard();

      // Navigate to preferences step
      await user.type(
        screen.getByLabelText(/What is the purpose of your prompt/),
        'Test purpose'
      );
      await user.selectOptions(screen.getByLabelText(/Industry/), 'Technology');
      await user.selectOptions(screen.getByLabelText(/Use Case/), 'Other');
      await user.click(screen.getAllByRole('button', { name: /next/i })[0]);
      await user.click(screen.getAllByRole('button', { name: /next/i })[0]);
    });

    it('should render preferences options', () => {
      expect(screen.getByText('Customize Your Prompt')).toBeInTheDocument();
      expect(screen.getByText('Output Format')).toBeInTheDocument();
      expect(screen.getByText('Tone & Style')).toBeInTheDocument();
      expect(screen.getByText('Response Length')).toBeInTheDocument();
    });

    it('should select output format', async () => {
      const user = userEvent.setup();

      const bulletPointsOption = screen.getByLabelText(/Bullet Points/);
      await user.click(bulletPointsOption);

      expect(bulletPointsOption).toBeChecked();
    });

    it('should select tone', async () => {
      const user = userEvent.setup();

      const casualTone = screen.getByLabelText(/Casual/);
      await user.click(casualTone);

      expect(casualTone).toBeChecked();
    });

    it('should toggle RAG support', async () => {
      const user = userEvent.setup();

      const ragCheckbox = screen.getByLabelText(/Include RAG/);
      await user.click(ragCheckbox);

      expect(ragCheckbox).toBeChecked();
    });
  });

  afterEach(() => {
    cleanup();
  });

  describe('Review Step', () => {
    beforeEach(async () => {
      const user = userEvent.setup();
      renderWizard();

      // Navigate to review step
      await user.type(
        screen.getByLabelText(/What is the purpose of your prompt/),
        'Generate customer support responses'
      );
      await user.selectOptions(screen.getByLabelText(/Industry/), 'Technology');
      await user.selectOptions(screen.getByLabelText(/Use Case/), 'Customer Support');
      await user.click(screen.getAllByRole('button', { name: /next/i })[0]);
      await user.click(screen.getAllByRole('button', { name: /next/i })[0]);
      await user.click(screen.getAllByRole('button', { name: /next/i })[0]);
    });

    it('should display review information', () => {
      expect(screen.getByText('Review Your Requirements')).toBeInTheDocument();
      expect(screen.getByText('Generate customer support responses')).toBeInTheDocument();
      expect(screen.getByText('Technology')).toBeInTheDocument();
      expect(screen.getByText('Customer Support')).toBeInTheDocument();
    });

    it('should show generate button on review step', () => {
      const generateButton = screen.getByRole('button', { name: /generate prompt/i });
      expect(generateButton).toBeInTheDocument();
    });

    it('should call onGenerate with correct data when generate button is clicked', async () => {
      const user = userEvent.setup();

      const generateButton = screen.getByRole('button', { name: /generate prompt/i });
      await user.click(generateButton);

      expect(mockOnGenerate).toHaveBeenCalledWith(
        expect.objectContaining({
          purpose: 'Generate customer support responses',
          industry: 'Technology',
          useCase: 'Customer Support',
          outputFormat: 'paragraph',
          tone: 'professional',
          length: 'medium',
          includeRAG: false
        })
      );
    });
  });

  afterEach(() => {
    cleanup();
  });

  describe('Navigation', () => {
    it('should navigate back to previous step', async () => {
      const user = userEvent.setup();
      renderWizard();

      // Go to variables step
      await user.type(
        screen.getByLabelText(/What is the purpose of your prompt/),
        'Test purpose'
      );
      await user.selectOptions(screen.getByLabelText(/Industry/), 'Technology');
      await user.selectOptions(screen.getByLabelText(/Use Case/), 'Other');
      await user.click(screen.getAllByRole('button', { name: /next/i })[0]);

      expect(screen.getByText('Define Input Variables')).toBeInTheDocument();

      // Go back
      const previousButton = screen.getByRole('button', { name: /previous/i });
      await user.click(previousButton);

      expect(screen.getByText('Tell us about your prompt')).toBeInTheDocument();
    });

    it('should show loading state when generating', async () => {
      const user = userEvent.setup();
      renderWizard({ loading: true });

      // Navigate to review step by filling out the form
      await user.type(
        screen.getByLabelText(/What is the purpose of your prompt/),
        'Test purpose'
      );
      await user.selectOptions(screen.getByLabelText(/Industry/), 'Technology');
      await user.selectOptions(screen.getByLabelText(/Use Case/), 'Other');

      // Go to variables step
      await user.click(screen.getAllByRole('button', { name: /next/i })[0]);

      // Go to preferences step
      await user.click(screen.getAllByRole('button', { name: /next/i })[0]);

      // Go to review step
      await user.click(screen.getAllByRole('button', { name: /next/i })[0]);

      // Should show loading state on the generate button
      expect(screen.getByText(/generating/i)).toBeInTheDocument();
    });
  });

  afterEach(() => {
    cleanup();
  });

  describe('Step Indicator', () => {
    it('should show current step as active', () => {
      renderWizard();

      const stepIndicators = screen.getAllByRole('generic');
      // The first step should be active (this would need more specific selectors in real implementation)
      expect(stepIndicators.length).toBeGreaterThan(0);
    });
  });

  afterEach(() => {
    cleanup();
  });

  describe('Form Validation', () => {
    it('should prevent navigation with invalid data', async () => {
      const user = userEvent.setup();
      renderWizard();

      // Try to proceed without filling required fields
      const nextButton = screen.getAllByRole('button', { name: /next/i })[0];
      await user.click(nextButton);

      // Should still be on the first step
      expect(screen.getByText('Tell us about your prompt')).toBeInTheDocument();
      expect(screen.getByText('Purpose is required')).toBeInTheDocument();
    });

    it('should validate variable names', async () => {
      const user = userEvent.setup();
      renderWizard();

      // Navigate to variables step
      await user.type(
        screen.getByLabelText(/What is the purpose of your prompt/),
        'Test purpose'
      );
      await user.selectOptions(screen.getByLabelText(/Industry/), 'Technology');
      await user.selectOptions(screen.getByLabelText(/Use Case/), 'Other');
      await user.click(screen.getAllByRole('button', { name: /next/i })[0]);

      // Add a variable with invalid name
      const addButton = screen.getByRole('button', { name: /add variable/i });
      await user.click(addButton);

      const nameInput = screen.getByLabelText(/Variable Name/);
      await user.type(nameInput, '123invalid');

      // The validation would be handled by the parent component or service
      // This test verifies the UI allows the input
      expect(nameInput).toHaveValue('123invalid');
    });
  });
});
