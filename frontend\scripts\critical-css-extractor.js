/**
 * Critical CSS Extraction and Inlining Script
 * Extracts critical above-the-fold CSS and inlines it in HTML
 * for faster LCP (Largest Contentful Paint) performance
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class CriticalCSSExtractor {
  constructor() {
    this.projectRoot = path.resolve(__dirname, '..');
    this.srcDir = path.join(this.projectRoot, 'src');
    this.distDir = path.join(this.projectRoot, 'dist');
    this.criticalCSSPath = path.join(this.srcDir, 'styles', 'critical.css');
  }

  /**
   * Extract and inline critical CSS in built HTML files
   */
  async extractAndInline() {
    console.log('🎯 Extracting and inlining critical CSS...');

    // Check if critical CSS file exists
    if (!fs.existsSync(this.criticalCSSPath)) {
      console.log('⚠️ Critical CSS file not found at:', this.criticalCSSPath);
      console.log('Creating basic critical CSS...');
      await this.createBasicCriticalCSS();
    }

    // Read critical CSS
    const criticalCSS = fs.readFileSync(this.criticalCSSPath, 'utf8');
    const minifiedCriticalCSS = this.minifyCSS(criticalCSS);

    // Find HTML files in dist directory
    const htmlFiles = this.findHTMLFiles(this.distDir);

    if (htmlFiles.length === 0) {
      console.log('⚠️ No HTML files found in dist directory. Run build first.');
      return;
    }

    // Process each HTML file
    for (const htmlFile of htmlFiles) {
      await this.inlineCriticalCSS(htmlFile, minifiedCriticalCSS);
    }

    console.log('✅ Critical CSS extraction and inlining completed');
  }

  /**
   * Create basic critical CSS if it doesn't exist
   */
  async createBasicCriticalCSS() {
    const basicCriticalCSS = `/* Critical CSS - Above the fold styles */
/* This CSS should be inlined in the HTML head for fastest rendering */

/* Reset and base styles */
*,
*::before,
*::after {
  box-sizing: border-box;
}

html {
  line-height: 1.15;
  -webkit-text-size-adjust: 100%;
}

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #ffffff;
  color: #1f2937;
}

/* Loading spinner for initial page load */
.loading-spinner {
  display: inline-block;
  width: 40px;
  height: 40px;
  border: 3px solid #f3f4f6;
  border-radius: 50%;
  border-top-color: #3b82f6;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Critical layout styles */
.app-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

#root {
  min-height: 100vh;
}

/* Hide non-critical content initially */
.non-critical {
  display: none;
}`;

    // Ensure styles directory exists
    const stylesDir = path.dirname(this.criticalCSSPath);
    if (!fs.existsSync(stylesDir)) {
      fs.mkdirSync(stylesDir, { recursive: true });
    }

    fs.writeFileSync(this.criticalCSSPath, basicCriticalCSS);
    console.log('✅ Created basic critical CSS file');
  }

  /**
   * Inline critical CSS in HTML file
   */
  async inlineCriticalCSS(htmlFile, criticalCSS) {
    try {
      let html = fs.readFileSync(htmlFile, 'utf8');
      
      // Create critical CSS style tag
      const criticalStyleTag = `<style id="critical-css">${criticalCSS}</style>`;
      
      // Check if critical CSS is already inlined
      if (html.includes('id="critical-css"')) {
        console.log(`⚠️ Critical CSS already inlined in ${path.basename(htmlFile)}`);
        return;
      }
      
      // Insert critical CSS before the closing </head> tag
      if (html.includes('</head>')) {
        html = html.replace('</head>', `    ${criticalStyleTag}\n</head>`);
        fs.writeFileSync(htmlFile, html);
        console.log(`✅ Inlined critical CSS in ${path.basename(htmlFile)}`);
      } else {
        console.log(`⚠️ No </head> tag found in ${path.basename(htmlFile)}`);
      }
    } catch (error) {
      console.error(`❌ Error processing ${htmlFile}:`, error.message);
    }
  }

  /**
   * Minify CSS for inlining
   */
  minifyCSS(css) {
    return css
      // Remove comments
      .replace(/\/\*[\s\S]*?\*\//g, '')
      // Remove extra whitespace
      .replace(/\s+/g, ' ')
      // Remove whitespace around specific characters
      .replace(/\s*([{}:;,>+~])\s*/g, '$1')
      // Remove trailing semicolons
      .replace(/;}/g, '}')
      // Remove leading/trailing whitespace
      .trim();
  }

  /**
   * Find all HTML files in a directory
   */
  findHTMLFiles(dir) {
    const htmlFiles = [];
    
    if (!fs.existsSync(dir)) {
      return htmlFiles;
    }
    
    const files = fs.readdirSync(dir);
    
    for (const file of files) {
      const filePath = path.join(dir, file);
      const stat = fs.statSync(filePath);
      
      if (stat.isDirectory()) {
        htmlFiles.push(...this.findHTMLFiles(filePath));
      } else if (file.endsWith('.html')) {
        htmlFiles.push(filePath);
      }
    }
    
    return htmlFiles;
  }
}

// Run the extractor if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const extractor = new CriticalCSSExtractor();
  extractor.extractAndInline().catch(console.error);
}

export { CriticalCSSExtractor };
