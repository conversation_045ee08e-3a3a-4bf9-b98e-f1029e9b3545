/**
 * Unified Service Layer Architecture
 * Provides standardized interfaces, error handling, caching, and request patterns for all services
 */

import {} from '@/utils/requestBatcher';
import type { StandardError, ErrorCategory, ErrorSeverity } from '@/utils/errorHandler';
import { withRetry } from '@/utils/errorHandler';

// Core service interfaces
export interface ServiceConfig {
  enableCaching?: boolean;
  defaultTTL?: number;
  enableRetry?: boolean;
  maxRetries?: number;
  enableMetrics?: boolean;
  enableLogging?: boolean;
}

export interface ServiceMetadata {
  timestamp: Date;
  requestId: string;
  processingTime: number;
  cacheHit?: boolean;
  retryCount?: number;
  correlationId: string;
}

export interface ServiceResponse<T> {
  data: T;
  success: boolean;
  error?: StandardError;
  metadata: ServiceMetadata;
}

export interface ServiceOperation<T> {
  execute(): Promise<T>;
  validate?(): boolean;
  getCacheKey?(): string;
  getContext(): string;
}

// Service dependency injection interfaces
export interface ServiceDependencies {
  cache?: CacheService;
  logger?: LoggerService;
  metrics?: MetricsService;
  auth?: AuthService;
}

export interface CacheService {
  get<T>(key: string): Promise<T | null>;
  set<T>(key: string, value: T, ttl?: number): Promise<void>;
  delete(key: string): Promise<void>;
  clear(): Promise<void>;
  has(key: string): Promise<boolean>;
}

export interface LoggerService {
  debug(message: string, context?: any): void;
  info(message: string, context?: any): void;
  warn(message: string, context?: any): void;
  error(message: string, error?: Error, context?: any): void;
}

export interface MetricsService {
  recordOperation(operation: string, duration: number, success: boolean): void;
  recordError(operation: string, error: StandardError): void;
  recordCacheHit(operation: string): void;
  recordCacheMiss(operation: string): void;
}

export interface AuthService {
  getCurrentUser(): Promise<any>;
  getAuthToken(): Promise<string | null>;
  isAuthenticated(): boolean;
}

// Unified base service class
export abstract class BaseService {
  protected static readonly DEFAULT_TTL = 300000; // 5 minutes
  protected static readonly DEFAULT_MAX_RETRIES = 3;
  protected static readonly cache = new Map<string, { data: any; expires: number }>();

  protected config: ServiceConfig;
  protected dependencies: ServiceDependencies;
  protected serviceName: string;

  constructor(serviceName: string, config: ServiceConfig = {}, dependencies: ServiceDependencies = {}) {
    this.serviceName = serviceName;
    this.config = {
      enableCaching: true,
      defaultTTL: BaseService.DEFAULT_TTL,
      enableRetry: true,
      maxRetries: BaseService.DEFAULT_MAX_RETRIES,
      enableMetrics: true,
      enableLogging: true,
      ...config
    };
    this.dependencies = dependencies;
  }

  /**
   * Execute a service operation with full error handling, caching, and metrics
   */
  protected async executeOperation<T>(operation: ServiceOperation<T>): Promise<ServiceResponse<T>> {
    const startTime = Date.now();
    const requestId = this.generateRequestId();
    const correlationId = this.generateCorrelationId();
    let cacheHit = false;
    let retryCount = 0;

    try {
      // Validate operation if validation is provided
      if (operation.validate && !operation.validate()) {
        throw new StandardError('Operation validation failed', {
          code: 'VALIDATION_ERROR',
          category: ErrorCategory.VALIDATION,
          severity: ErrorSeverity.LOW,
          context: operation.getContext()
        });
      }

      // Try cache first if enabled and cache key is provided
      let result: T;
      const cacheKey = operation.getCacheKey?.();

      if (this.config.enableCaching && cacheKey) {
        const cachedResult = await this.getFromCache<T>(cacheKey);
        if (cachedResult !== null) {
          result = cachedResult;
          cacheHit = true;
          this.recordCacheHit(operation.getContext());
        }
      }

      // Execute operation if not cached
      if (!cacheHit) {
        if (this.config.enableRetry) {
          result = await withRetry(
            operation.execute,
            {
              maxAttempts: this.config.maxRetries,
              onRetry: (attempt) => {
                retryCount = attempt;
                this.log('warn', `Retrying operation ${operation.getContext()}, attempt ${attempt}`);
              }
            }
          );
        } else {
          result = await operation.execute();
        }

        // Cache result if caching is enabled
        if (this.config.enableCaching && cacheKey && result !== null) {
          await this.setCache(cacheKey, result);
        }

        this.recordCacheMiss(operation.getContext());
      }

      const processingTime = Date.now() - startTime;

      // Record metrics
      this.recordMetrics(operation.getContext(), processingTime, true);

      return {
        data: result,
        success: true,
        metadata: {
          timestamp: new Date(),
          requestId,
          processingTime,
          cacheHit,
          retryCount,
          correlationId
        }
      };

    } catch (error) {
      const processingTime = Date.now() - startTime;
      const standardError = this.mapError(error, operation.getContext(), correlationId);

      // Record error metrics
      this.recordMetrics(operation.getContext(), processingTime, false);
      this.recordError(operation.getContext(), standardError);

      return {
        data: null as any,
        success: false,
        error: standardError,
        metadata: {
          timestamp: new Date(),
          requestId,
          processingTime,
          cacheHit,
          retryCount,
          correlationId
        }
      };
    }
  }

  /**
   * Cache management methods
   */
  protected async getFromCache<T>(key: string): Promise<T | null> {
    if (this.dependencies.cache) {
      return await this.dependencies.cache.get<T>(key);
    }

    // Fallback to in-memory cache
    const entry = BaseService.cache.get(key);
    if (!entry) return null;

    if (Date.now() > entry.expires) {
      BaseService.cache.delete(key);
      return null;
    }

    return entry.data;
  }

  protected async setCache<T>(key: string, data: T, ttl?: number): Promise<void> {
    const cacheTime = ttl || this.config.defaultTTL || BaseService.DEFAULT_TTL;

    if (this.dependencies.cache) {
      await this.dependencies.cache.set(key, data, cacheTime);
    } else {
      // Fallback to in-memory cache
      BaseService.cache.set(key, {
        data,
        expires: Date.now() + cacheTime
      });
    }
  }

  protected async clearCache(pattern?: string): Promise<void> {
    if (this.dependencies.cache) {
      if (pattern) {
        // Implementation depends on cache service capabilities
        this.log('warn', 'Pattern-based cache clearing not implemented for external cache service');
      } else {
        await this.dependencies.cache.clear();
      }
    } else {
      if (pattern) {
        // Clear matching keys from in-memory cache
        for (const key of BaseService.cache.keys()) {
          if (key.includes(pattern)) {
            BaseService.cache.delete(key);
          }
        }
      } else {
        BaseService.cache.clear();
      }
    }
  }

  /**
   * Set cache entry
   */
  protected static setCache<T>(key: string, data: T, ttl: number = this.DEFAULT_TTL): void {
    this.cache.set(key, {
      data,
      expires: Date.now() + ttl
    });
  }

  /**
   * Get cache entry
   */
  protected static getCache<T>(key: string): T | null {
    const entry = this.cache.get(key);
    if (!entry) return null;

    if (Date.now() > entry.expires) {
      this.cache.delete(key);
      return null;
    }

    return entry.data;
  }

  /**
   * Clear cache entries by pattern
   */
  protected static clearCache(pattern?: string): void {
    if (!pattern) {
      this.cache.clear();
      return;
    }

    for (const key of this.cache.keys()) {
      if (key.includes(pattern)) {
        this.cache.delete(key);
      }
    }
  }

  /**
   * Logging methods
   */
  protected log(level: 'debug' | 'info' | 'warn' | 'error', message: string, context?: any): void {
    if (!this.config.enableLogging) return;

    const logMessage = `[${this.serviceName}] ${message}`;

    if (this.dependencies.logger) {
      this.dependencies.logger[level](logMessage, context);
    } else {
      // Fallback to console logging
      switch (level) {
        case 'debug':
          console.debug(logMessage, context);
          break;
        case 'info':
          console.info(logMessage, context);
          break;
        case 'warn':
          console.warn(logMessage, context);
          break;
        case 'error':
          console.error(logMessage, context);
          break;
      }
    }
  }

  /**
   * Metrics recording methods
   */
  protected recordMetrics(operation: string, duration: number, success: boolean): void {
    if (!this.config.enableMetrics) return;

    if (this.dependencies.metrics) {
      this.dependencies.metrics.recordOperation(operation, duration, success);
    } else {
      // Fallback logging
      this.log('debug', `Operation ${operation} completed in ${duration}ms, success: ${success}`);
    }
  }

  protected recordError(operation: string, error: StandardError): void {
    if (!this.config.enableMetrics) return;

    if (this.dependencies.metrics) {
      this.dependencies.metrics.recordError(operation, error);
    } else {
      this.log('error', `Operation ${operation} failed`, { error: error.message, code: error.code });
    }
  }

  protected recordCacheHit(operation: string): void {
    if (!this.config.enableMetrics) return;

    if (this.dependencies.metrics) {
      this.dependencies.metrics.recordCacheHit(operation);
    } else {
      this.log('debug', `Cache hit for operation ${operation}`);
    }
  }

  protected recordCacheMiss(operation: string): void {
    if (!this.config.enableMetrics) return;

    if (this.dependencies.metrics) {
      this.dependencies.metrics.recordCacheMiss(operation);
    } else {
      this.log('debug', `Cache miss for operation ${operation}`);
    }
  }

  /**
   * Utility methods
   */
  protected generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  protected generateCorrelationId(): string {
    return `corr_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Map error to standardized format
   */
  protected mapError(
    error: unknown,
    context: string,
    correlationId?: string
  ): StandardError {
    const id = correlationId || this.generateCorrelationId();

    if (error instanceof Error) {
      return new StandardError(error.message, {
        code: this.getErrorCode(error),
        userMessage: this.getUserFriendlyMessage(error),
        retryable: this.isRetryable(error),
        context,
        correlationId: id,
        category: this.getErrorCategory(error),
        severity: this.getErrorSeverity(error)
      });
    }

    return new StandardError(String(error), {
      code: 'UNKNOWN_ERROR',
      userMessage: 'An unexpected error occurred. Please try again.',
      retryable: true,
      context,
      correlationId: id,
      category: ErrorCategory.UNKNOWN,
      severity: ErrorSeverity.MEDIUM
    });
  }

  /**
   * Get error code from error
   */
  protected static getErrorCode(error: Error): string {
    // Firebase errors
    if ('code' in error) {
      return (error as any).code;
    }

    // Network errors
    if (error.message.includes('fetch')) {
      return 'NETWORK_ERROR';
    }

    // Permission errors
    if (error.message.includes('permission')) {
      return 'PERMISSION_DENIED';
    }

    return 'UNKNOWN_ERROR';
  }

  /**
   * Get user-friendly error message
   */
  protected static getUserFriendlyMessage(error: Error): string {
    const code = this.getErrorCode(error);

    const messageMap: Record<string, string> = {
      'permission-denied': 'You do not have permission to perform this action.',
      'unauthenticated': 'Please sign in to continue.',
      'unavailable': 'Service is temporarily unavailable. Please try again later.',
      'not-found': 'The requested resource was not found.',
      'already-exists': 'This resource already exists.',
      'invalid-argument': 'Invalid input provided. Please check your data.',
      'deadline-exceeded': 'Request timed out. Please try again.',
      'resource-exhausted': 'Rate limit exceeded. Please try again later.',
      'NETWORK_ERROR': 'Network connection error. Please check your internet connection.',
      'UNKNOWN_ERROR': 'An unexpected error occurred. Please try again.'
    };

    return messageMap[code] || messageMap['UNKNOWN_ERROR'];
  }

  /**
   * Check if error is retryable
   */
  protected static isRetryable(error: Error): boolean {
    const code = this.getErrorCode(error);

    const retryableCodes = [
      'unavailable',
      'deadline-exceeded',
      'resource-exhausted',
      'internal',
      'NETWORK_ERROR'
    ];

    return retryableCodes.includes(code);
  }

  /**
   * Get error category
   */
  protected static getErrorCategory(error: Error): ErrorCategory {
    const code = this.getErrorCode(error);

    if (code.includes('permission') || code === 'unauthenticated') {
      return ErrorCategory.AUTHENTICATION;
    }
    if (code === 'permission-denied') {
      return ErrorCategory.AUTHORIZATION;
    }
    if (code === 'not-found') {
      return ErrorCategory.NOT_FOUND;
    }
    if (code.includes('network') || error.message.includes('fetch')) {
      return ErrorCategory.NETWORK;
    }
    if (code === 'invalid-argument' || code === 'failed-precondition') {
      return ErrorCategory.VALIDATION;
    }
    if (code.includes('unavailable') || code.includes('deadline-exceeded')) {
      return ErrorCategory.SERVER;
    }

    return ErrorCategory.CLIENT;
  }

  /**
   * Get error severity
   */
  protected static getErrorSeverity(error: Error): ErrorSeverity {
    const code = this.getErrorCode(error);

    if (code === 'data-loss' || code === 'internal') {
      return ErrorSeverity.CRITICAL;
    }
    if (code.includes('unavailable') || code.includes('deadline-exceeded')) {
      return ErrorSeverity.HIGH;
    }
    if (code === 'permission-denied' || code === 'unauthenticated') {
      return ErrorSeverity.MEDIUM;
    }

    return ErrorSeverity.LOW;
  }

  /**
   * Static methods for backward compatibility
   */
  protected static generateCorrelationId(): string {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Static method for simple operations without full service setup
   */
  protected static async executeWithErrorHandling<T>(
    operation: () => Promise<T>,
    context: string,
    correlationId?: string
  ): Promise<T> {
    try {
      return await operation();
    } catch (error) {
      const standardError = this.mapError(error, context, correlationId);
      throw standardError;
    }
  }

  protected static mapError(
    error: unknown,
    context: string,
    correlationId?: string
  ): StandardError {
    const id = correlationId || this.generateCorrelationId();

    if (error instanceof Error) {
      return new StandardError(error.message, {
        code: this.getErrorCode(error),
        userMessage: this.getUserFriendlyMessage(error),
        retryable: this.isRetryable(error),
        context,
        correlationId: id,
        category: this.getErrorCategory(error),
        severity: this.getErrorSeverity(error)
      });
    }

    return new StandardError(String(error), {
      code: 'UNKNOWN_ERROR',
      userMessage: 'An unexpected error occurred. Please try again.',
      retryable: true,
      context,
      correlationId: id,
      category: ErrorCategory.UNKNOWN,
      severity: ErrorSeverity.MEDIUM
    });
  }

  /**
   * Validate required parameters
   */
  protected static validateRequired(params: Record<string, any>): void {
    for (const [key, value] of Object.entries(params)) {
      if (value === undefined || value === null || value === '') {
        throw new Error(`Required parameter '${key}' is missing or empty`);
      }
    }
  }

  /**
   * Create standardized response
   */
  protected static createResponse<T>(
    data: T,
    metadata?: { requestId?: string; processingTime?: number }
  ): ServiceResponse<T> {
    return {
      data,
      success: true,
      metadata: {
        timestamp: new Date(),
        requestId: metadata?.requestId || this.generateCorrelationId(),
        processingTime: metadata?.processingTime || 0
      }
    };
  }

  /**
   * Create error response
   */
  protected static createErrorResponse(error: ServiceError): ServiceResponse<null> {
    return {
      data: null,
      success: false,
      error,
      metadata: {
        timestamp: new Date(),
        requestId: error.correlationId,
        processingTime: 0
      }
    };
  }
}

/**
 * Service operation decorator for automatic error handling
 */
export function withErrorHandling(context: string) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;

    descriptor.value = async function (...args: any[]) {
      try {
        return await method.apply(this, args);
      } catch (error) {
        const standardError = BaseService.prototype.constructor.mapError(error, context);
        throw standardError;
      }
    };
  };
}

/**
 * Service operation decorator for automatic caching
 */
export function withCache(keyGenerator: (...args: any[]) => string, ttl?: number) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;

    descriptor.value = async function (...args: any[]) {
      const cacheKey = keyGenerator(...args);
      return BaseService.prototype.constructor.executeWithCache(
        cacheKey,
        () => method.apply(this, args),
        { ttl }
      );
    };
  };
}
