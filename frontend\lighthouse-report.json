{"lighthouseVersion": "12.8.0", "requestedUrl": "https://rag-prompt-library.web.app/", "mainDocumentUrl": "https://rag-prompt-library.web.app/", "finalDisplayedUrl": "https://rag-prompt-library.web.app/auth", "finalUrl": "https://rag-prompt-library.web.app/", "fetchTime": "2025-07-28T19:21:24.842Z", "gatherMode": "navigation", "runWarnings": [], "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) HeadlessChrome/********* Safari/537.36", "environment": {"networkUserAgent": "Mozilla/5.0 (Linux; Android 11; moto g power (2022)) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "hostUserAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) HeadlessChrome/********* Safari/537.36", "benchmarkIndex": 2512.5, "credits": {"axe-core": "4.10.3"}}, "audits": {"is-on-https": {"id": "is-on-https", "title": "Uses HTTPS", "description": "All sites should be protected with HTTPS, even ones that don't handle sensitive data. This includes avoiding [mixed content](https://developers.google.com/web/fundamentals/security/prevent-mixed-content/what-is-mixed-content), where some resources are loaded over HTTP despite the initial request being served over HTTPS. HTTPS prevents intruders from tampering with or passively listening in on the communications between your app and your users, and is a prerequisite for HTTP/2 and many new web platform APIs. [Learn more about HTTPS](https://developer.chrome.com/docs/lighthouse/pwa/is-on-https/).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [{"key": "url", "valueType": "url", "label": "Insecure URL"}, {"key": "resolution", "valueType": "text", "label": "Request Resolution"}], "items": []}}, "redirects-http": {"id": "redirects-http", "title": "Redirects HTTP traffic to HTTPS", "description": "Make sure that you redirect all HTTP traffic to HTTPS in order to enable secure web features for all your users. [Learn more](https://developer.chrome.com/docs/lighthouse/pwa/redirects-http/).", "score": null, "scoreDisplayMode": "notApplicable"}, "viewport": {"id": "viewport", "title": "Has a `<meta name=\"viewport\">` tag with `width` or `initial-scale`", "description": "A `<meta name=\"viewport\">` not only optimizes your app for mobile screen sizes, but also prevents [a 300 millisecond delay to user input](https://developer.chrome.com/blog/300ms-tap-delay-gone-away/). [Learn more about using the viewport meta tag](https://developer.chrome.com/docs/lighthouse/pwa/viewport/).", "score": 1, "scoreDisplayMode": "metricSavings", "warnings": [], "metricSavings": {"INP": 0}, "details": {"type": "debugdata", "viewportContent": "width=device-width, initial-scale=1.0"}, "guidanceLevel": 3}, "first-contentful-paint": {"id": "first-contentful-paint", "title": "First Contentful Paint", "description": "First Contentful Paint marks the time at which the first text or image is painted. [Learn more about the First Contentful Paint metric](https://developer.chrome.com/docs/lighthouse/performance/first-contentful-paint/).", "score": 0.99, "scoreDisplayMode": "numeric", "numericValue": 1082.559, "numericUnit": "millisecond", "displayValue": "1.1 s", "scoringOptions": {"p10": 1800, "median": 3000}}, "largest-contentful-paint": {"id": "largest-contentful-paint", "title": "Largest Contentful Paint", "description": "Largest Contentful Paint marks the time at which the largest text or image is painted. [Learn more about the Largest Contentful Paint metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)", "score": 0.33, "scoreDisplayMode": "numeric", "numericValue": 4680.45975, "numericUnit": "millisecond", "displayValue": "4.7 s", "scoringOptions": {"p10": 2500, "median": 4000}}, "first-meaningful-paint": {"id": "first-meaningful-paint", "title": "First Meaningful Paint", "description": "First Meaningful Paint measures when the primary content of a page is visible. [Learn more about the First Meaningful Paint metric](https://developer.chrome.com/docs/lighthouse/performance/first-meaningful-paint/).", "score": null, "scoreDisplayMode": "notApplicable"}, "speed-index": {"id": "speed-index", "title": "Speed Index", "description": "Speed Index shows how quickly the contents of a page are visibly populated. [Learn more about the Speed Index metric](https://developer.chrome.com/docs/lighthouse/performance/speed-index/).", "score": 0.37, "scoreDisplayMode": "numeric", "numericValue": 6600.253369201369, "numericUnit": "millisecond", "displayValue": "6.6 s", "scoringOptions": {"p10": 3387, "median": 5800}}, "screenshot-thumbnails": {"id": "screenshot-thumbnails", "title": "Screenshot Thumbnails", "description": "This is what the load of your site looked like.", "score": 1, "scoreDisplayMode": "informative", "details": {"type": "filmstrip", "scale": 3670, "items": [{"timing": 459, "timestamp": 1025178960, "data": "data:image/jpeg;base64,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"}, {"timing": 918, "timestamp": 1025637710, "data": "data:image/jpeg;base64,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"}, {"timing": 1376, "timestamp": 1026096460, "data": "data:image/jpeg;base64,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"}, {"timing": 1835, "timestamp": 1026555210, "data": "data:image/jpeg;base64,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"}, {"timing": 2294, "timestamp": 1027013960, "data": "data:image/jpeg;base64,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"}, {"timing": 2753, "timestamp": 1027472710, "data": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAHyAPoDASIAAhEBAxEB/8QAGQABAQEBAQEAAAAAAAAAAAAAAAQDAgEI/8QAKRABAAIBAgQEBwEAAAAAAAAAAAECAwQREhMxUSEigZEUMkFSYXHB0f/EABQBAQAAAAAAAAAAAAAAAAAAAAD/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwD6pAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGGXUbZOXipzMn1jfaK/uXnDq58ZyYYntwTP8AQUCaM+TFMRqqRFZ8IyVny+vZSAAAAAAAAAAAAAAAAAAAAAAAAAAAz1OTk4MmT7azLRjrqTfSZa168O8A90uLlYoiZ3vPja3eWrnFeMmOt6zvW0bw6B5asWrNbRvExtMMNFM1jJhtMzOK3DEz26woTaXzZtTkj5ZvFY9I/wBBSAAAAAAAAAAAAAAAAAAAAAAAAAAACXhvpbTy6zfBM78Mdaz+O8OvjdP0nJwz2tExPsoAS2y5NR5MFbUrPXJaNvaFGLHXFjrSkbViNodAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAP/2Q=="}, {"timing": 3211, "timestamp": 1027931460, "data": "data:image/jpeg;base64,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"}, {"timing": 3670, "timestamp": 1028390210, "data": "data:image/jpeg;base64,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"}]}}, "final-screenshot": {"id": "final-screenshot", "title": "Final Screenshot", "description": "The last screenshot captured of the pageload.", "score": 1, "scoreDisplayMode": "informative", "details": {"type": "screenshot", "timing": 3671, "timestamp": 1028390937, "data": "data:image/jpeg;base64,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"}}, "total-blocking-time": {"id": "total-blocking-time", "title": "Total Blocking Time", "description": "Sum of all time periods between FCP and Time to Interactive, when task length exceeded 50ms, expressed in milliseconds. [Learn more about the Total Blocking Time metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-total-blocking-time/).", "score": 0.99, "scoreDisplayMode": "numeric", "numericValue": 72.5, "numericUnit": "millisecond", "displayValue": "70 ms", "scoringOptions": {"p10": 200, "median": 600}}, "max-potential-fid": {"id": "max-potential-fid", "title": "Max Potential First Input Delay", "description": "The maximum potential First Input Delay that your users could experience is the duration of the longest task. [Learn more about the Maximum Potential First Input Delay metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-max-potential-fid/).", "score": 0.98, "scoreDisplayMode": "numeric", "numericValue": 87, "numericUnit": "millisecond", "displayValue": "90 ms"}, "cumulative-layout-shift": {"id": "cumulative-layout-shift", "title": "Cumulative Layout Shift", "description": "Cumulative Layout Shift measures the movement of visible elements within the viewport. [Learn more about the Cumulative Layout Shift metric](https://web.dev/articles/cls).", "score": 1, "scoreDisplayMode": "numeric", "numericValue": 0, "numericUnit": "unitless", "displayValue": "0", "scoringOptions": {"p10": 0.1, "median": 0.25}, "details": {"type": "debugdata", "items": [{"cumulativeLayoutShiftMainFrame": 0, "newEngineResult": {"cumulativeLayoutShift": 0, "cumulativeLayoutShiftMainFrame": 0}, "newEngineResultDiffered": false}]}}, "errors-in-console": {"id": "errors-in-console", "title": "No browser errors logged to the console", "description": "Errors logged to the console indicate unresolved problems. They can come from network request failures and other browser concerns. [Learn more about this errors in console diagnostic audit](https://developer.chrome.com/docs/lighthouse/best-practices/errors-in-console/)", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [{"key": "sourceLocation", "valueType": "source-location", "label": "Source"}, {"key": "description", "valueType": "code", "label": "Description"}], "items": []}}, "server-response-time": {"id": "server-response-time", "title": "Initial server response time was short", "description": "Keep the server response time for the main document short because all other requests depend on it. [Learn more about the Time to First Byte metric](https://developer.chrome.com/docs/lighthouse/performance/time-to-first-byte/).", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 171.41099999999994, "numericUnit": "millisecond", "displayValue": "Root document took 170 ms", "metricSavings": {"FCP": 50, "LCP": 50}, "details": {"type": "opportunity", "headings": [{"key": "url", "valueType": "url", "label": "URL"}, {"key": "responseTime", "valueType": "timespanMs", "label": "Time Spent"}], "items": [{"url": "https://rag-prompt-library.web.app/", "responseTime": 171.41099999999994}], "overallSavingsMs": 71.41099999999994}, "guidanceLevel": 1}, "interactive": {"id": "interactive", "title": "Time to Interactive", "description": "Time to Interactive is the amount of time it takes for the page to become fully interactive. [Learn more about the Time to Interactive metric](https://developer.chrome.com/docs/lighthouse/performance/interactive/).", "score": 0.62, "scoreDisplayMode": "numeric", "numericValue": 6185.************, "numericUnit": "millisecond", "displayValue": "6.2 s"}, "user-timings": {"id": "user-timings", "title": "User Timing marks and measures", "description": "Consider instrumenting your app with the User Timing API to measure your app's real-world performance during key user experiences. [Learn more about User Timing marks](https://developer.chrome.com/docs/lighthouse/performance/user-timings/).", "score": null, "scoreDisplayMode": "notApplicable", "details": {"type": "table", "headings": [{"key": "name", "valueType": "text", "label": "Name"}, {"key": "timingType", "valueType": "text", "label": "Type"}, {"key": "startTime", "valueType": "ms", "granularity": 0.01, "label": "Start Time"}, {"key": "duration", "valueType": "ms", "granularity": 0.01, "label": "Duration"}], "items": []}, "guidanceLevel": 2}, "critical-request-chains": {"id": "critical-request-chains", "title": "Avoid chaining critical requests", "description": "The Critical Request Chains below show you what resources are loaded with a high priority. Consider reducing the length of chains, reducing the download size of resources, or deferring the download of unnecessary resources to improve page load. [Learn how to avoid chaining critical requests](https://developer.chrome.com/docs/lighthouse/performance/critical-request-chains/).", "score": 1, "scoreDisplayMode": "informative", "displayValue": "3 chains found", "details": {"type": "criticalrequestchain", "chains": {"6C69CF588F9957C8EAAACC3AA07E5C6C": {"request": {"url": "https://rag-prompt-library.web.app/", "startTime": 1024.723882, "endTime": 1025.255085, "responseReceivedTime": 1025.25448, "transferSize": 1978}, "children": {"6556.3": {"request": {"url": "https://rag-prompt-library.web.app/assets/index-U1UX038B.css", "startTime": 1025.282805, "endTime": 1025.580789, "responseReceivedTime": 1025.563906, "transferSize": 10366}}, "6556.2": {"request": {"url": "https://rag-prompt-library.web.app/assets/js/index-CrS6G9IP.js", "startTime": 1025.282418, "endTime": 1025.682034, "responseReceivedTime": 1025.679248, "transferSize": 2610}, "children": {"6556.6": {"request": {"url": "https://rag-prompt-library.web.app/assets/js/vendor-react-core-DBAjAgoh.js", "startTime": 1025.684487, "endTime": 1027.042554, "responseReceivedTime": 1025.983032, "transferSize": 180719}}, "6556.7": {"request": {"url": "https://rag-prompt-library.web.app/assets/js/components-common-Bz06cWsP.js", "startTime": 1025.684737, "endTime": 1027.280862, "responseReceivedTime": 1025.9841250000002, "transferSize": 67648}}}}}}}, "longestChain": {"duration": 2556.************, "length": 3, "transferSize": 67648}}, "guidanceLevel": 1}, "redirects": {"id": "redirects", "title": "Avoid multiple page redirects", "description": "Redirects introduce additional delays before the page can be loaded. [Learn how to avoid page redirects](https://developer.chrome.com/docs/lighthouse/performance/redirects/).", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 0, "numericUnit": "millisecond", "displayValue": "", "metricSavings": {"LCP": 0, "FCP": 0}, "details": {"type": "opportunity", "headings": [], "items": [], "overallSavingsMs": 0}, "guidanceLevel": 2}, "image-aspect-ratio": {"id": "image-aspect-ratio", "title": "Displays images with correct aspect ratio", "description": "Image display dimensions should match natural aspect ratio. [Learn more about image aspect ratio](https://developer.chrome.com/docs/lighthouse/best-practices/image-aspect-ratio/).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [{"key": "node", "valueType": "node", "label": ""}, {"key": "url", "valueType": "url", "label": "URL"}, {"key": "displayedAspectRatio", "valueType": "text", "label": "Aspect Ratio (Displayed)"}, {"key": "actualAspectRatio", "valueType": "text", "label": "Aspect Ratio (Actual)"}], "items": []}}, "image-size-responsive": {"id": "image-size-responsive", "title": "Serves images with appropriate resolution", "description": "Image natural dimensions should be proportional to the display size and the pixel ratio to maximize image clarity. [Learn how to provide responsive images](https://web.dev/articles/serve-responsive-images).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [{"key": "node", "valueType": "node", "label": ""}, {"key": "url", "valueType": "url", "label": "URL"}, {"key": "displayedSize", "valueType": "text", "label": "Displayed size"}, {"key": "actualSize", "valueType": "text", "label": "Actual size"}, {"key": "expectedSize", "valueType": "text", "label": "Expected size"}], "items": []}}, "deprecations": {"id": "deprecations", "title": "Avoids deprecated APIs", "description": "Deprecated APIs will eventually be removed from the browser. [Learn more about deprecated APIs](https://developer.chrome.com/docs/lighthouse/best-practices/deprecations/).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [{"key": "value", "valueType": "text", "label": "Deprecation / Warning"}, {"key": "source", "valueType": "source-location", "label": "Source"}], "items": []}}, "third-party-cookies": {"id": "third-party-cookies", "title": "Avoids third-party cookies", "description": "Third-party cookies may be blocked in some contexts. [Learn more about preparing for third-party cookie restrictions](https://privacysandbox.google.com/cookies/prepare/overview).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [{"key": "name", "valueType": "text", "label": "Name"}, {"key": "url", "valueType": "url", "label": "URL"}], "items": []}}, "mainthread-work-breakdown": {"id": "mainthread-work-breakdown", "title": "Minimizes main-thread work", "description": "Consider reducing the time spent parsing, compiling and executing JS. You may find delivering smaller JS payloads helps with this. [Learn how to minimize main-thread work](https://developer.chrome.com/docs/lighthouse/performance/mainthread-work-breakdown/)", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 898.6600000000009, "numericUnit": "millisecond", "displayValue": "0.9 s", "metricSavings": {"TBT": 50}, "details": {"type": "table", "headings": [{"key": "groupLabel", "valueType": "text", "label": "Category"}, {"key": "duration", "valueType": "ms", "granularity": 1, "label": "Time Spent"}], "items": [{"group": "other", "groupLabel": "Other", "duration": 364.6160000000005}, {"group": "scriptEvaluation", "groupLabel": "Script Evaluation", "duration": 297.7400000000004}, {"group": "styleLayout", "groupLabel": "Style & Layout", "duration": 178.39199999999997}, {"group": "paintCompositeRender", "groupLabel": "Rendering", "duration": 15.603999999999996}, {"group": "garbageCollection", "groupLabel": "Garbage Collection", "duration": 15.491999999999994}, {"group": "scriptParseCompile", "groupLabel": "Script Parsing & Compilation", "duration": 13.552}, {"group": "parseHTML", "groupLabel": "Parse HTML & CSS", "duration": 13.264}], "sortedBy": ["duration"]}, "guidanceLevel": 1}, "bootup-time": {"id": "bootup-time", "title": "JavaScript execution time", "description": "Consider reducing the time spent parsing, compiling, and executing JS. You may find delivering smaller JS payloads helps with this. [Learn how to reduce Javascript execution time](https://developer.chrome.com/docs/lighthouse/performance/bootup-time/).", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 285.67200000000014, "numericUnit": "millisecond", "displayValue": "0.3 s", "metricSavings": {"TBT": 50}, "details": {"type": "table", "headings": [{"key": "url", "valueType": "url", "label": "URL"}, {"key": "total", "granularity": 1, "valueType": "ms", "label": "Total CPU Time"}, {"key": "scripting", "granularity": 1, "valueType": "ms", "label": "Script Evaluation"}, {"key": "scriptParseCompile", "granularity": 1, "valueType": "ms", "label": "<PERSON><PERSON><PERSON> Parse"}], "items": [{"url": "Unattributable", "total": 332.4680000000005, "scripting": 11.239999999999991, "scriptParseCompile": 0}, {"url": "https://rag-prompt-library.web.app/", "total": 303.0480000000002, "scripting": 74.58000000000017, "scriptParseCompile": 0.596}, {"url": "https://rag-prompt-library.web.app/assets/js/vendor-react-core-DBAjAgoh.js", "total": 145.89600000000002, "scripting": 126.10000000000002, "scriptParseCompile": 2.964}, {"url": "https://apis.google.com/_/scs/abc-static/_/js/k=gapi.lb.en.iFs_Bhrqdio.O/m=gapi_iframes/rt=j/sv=1/d=1/ed=1/rs=AHpOoo-IvlQWaLHZdUKQFbafXd_3OEvU9Q/cb=gapi.loaded_0?le=scs", "total": 73.54799999999996, "scripting": 63.44399999999997, "scriptParseCompile": 6.748}], "summary": {"wastedMs": 285.67200000000014}, "sortedBy": ["total"]}, "guidanceLevel": 1}, "uses-rel-preconnect": {"id": "uses-rel-preconnect", "title": "Preconnect to required origins", "description": "Consider adding `preconnect` or `dns-prefetch` resource hints to establish early connections to important third-party origins. [Learn how to preconnect to required origins](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/).", "score": 0, "scoreDisplayMode": "metricSavings", "numericValue": 705.816, "numericUnit": "millisecond", "displayValue": "Est savings of 710 ms", "warnings": [], "metricSavings": {"LCP": 700, "FCP": 0}, "details": {"type": "opportunity", "headings": [{"key": "url", "valueType": "url", "label": "URL"}, {"key": "wastedMs", "valueType": "timespanMs", "label": "Est Savings"}], "items": [{"url": "https://rag-prompt-library.firebaseapp.com", "wastedMs": 705.816}, {"url": "https://apis.google.com", "wastedMs": 383.576}], "overallSavingsMs": 705.816, "sortedBy": ["wastedMs"]}, "guidanceLevel": 3}, "font-display": {"id": "font-display", "title": "All text remains visible during webfont loads", "description": "Leverage the `font-display` CSS feature to ensure text is user-visible while webfonts are loading. [Learn more about `font-display`](https://developer.chrome.com/docs/lighthouse/performance/font-display/).", "score": 1, "scoreDisplayMode": "metricSavings", "warnings": [], "details": {"type": "table", "headings": [{"key": "url", "valueType": "url", "label": "URL"}, {"key": "wastedMs", "valueType": "ms", "label": "Est Savings"}], "items": []}, "guidanceLevel": 3}, "diagnostics": {"id": "diagnostics", "title": "Diagnostics", "description": "Collection of useful page vitals.", "score": 1, "scoreDisplayMode": "informative", "details": {"type": "debugdata", "items": [{"numRequests": 13, "numScripts": 6, "numStylesheets": 1, "numFonts": 0, "numTasks": 592, "numTasksOver10ms": 6, "numTasksOver25ms": 1, "numTasksOver50ms": 0, "numTasksOver100ms": 0, "numTasksOver500ms": 0, "rtt": 0, "throughput": 1952786.923800906, "maxRtt": 202.90800000000002, "maxServerLatency": 537.1755, "totalByteWeight": 398556, "totalTaskTime": 224.6649999999999, "mainDocumentTransferSize": 1978}]}}, "network-requests": {"id": "network-requests", "title": "Network Requests", "description": "Lists the network requests that were made during page load.", "score": 1, "scoreDisplayMode": "informative", "details": {"type": "table", "headings": [{"key": "url", "valueType": "url", "label": "URL"}, {"key": "protocol", "valueType": "text", "label": "Protocol"}, {"key": "networkRequestTime", "valueType": "ms", "granularity": 1, "label": "Network Request Time"}, {"key": "networkEndTime", "valueType": "ms", "granularity": 1, "label": "Network End Time"}, {"key": "transferSize", "valueType": "bytes", "displayUnit": "kb", "granularity": 1, "label": "Transfer Size"}, {"key": "resourceSize", "valueType": "bytes", "displayUnit": "kb", "granularity": 1, "label": "Resource Size"}, {"key": "statusCode", "valueType": "text", "label": "Status Code"}, {"key": "mimeType", "valueType": "text", "label": "MIME Type"}, {"key": "resourceType", "valueType": "text", "label": "Resource Type"}], "items": [{"url": "https://rag-prompt-library.web.app/", "sessionTargetType": "page", "protocol": "h2", "rendererStartTime": 0, "networkRequestTime": 1.7089999999152496, "networkEndTime": 532.9119999998948, "finished": true, "transferSize": 1978, "resourceSize": 2500, "statusCode": 200, "mimeType": "text/html", "resourceType": "Document", "priority": "VeryHigh", "experimentalFromMainFrame": true, "entity": "web.app"}, {"url": "https://rag-prompt-library.web.app/assets/js/index-CrS6G9IP.js", "sessionTargetType": "page", "protocol": "h2", "rendererStartTime": 559.3090000000084, "networkRequestTime": 560.2449999999953, "networkEndTime": 959.8609999999171, "finished": true, "transferSize": 2610, "resourceSize": 12670, "statusCode": 200, "mimeType": "text/javascript", "resourceType": "<PERSON><PERSON><PERSON>", "priority": "High", "experimentalFromMainFrame": true, "entity": "web.app"}, {"url": "https://rag-prompt-library.web.app/assets/index-U1UX038B.css", "sessionTargetType": "page", "protocol": "h2", "rendererStartTime": 559.683999999892, "networkRequestTime": 560.6319999999832, "networkEndTime": 858.6160000000382, "finished": true, "transferSize": 10366, "resourceSize": 86467, "statusCode": 200, "mimeType": "text/css", "resourceType": "Stylesheet", "priority": "VeryHigh", "experimentalFromMainFrame": true, "entity": "web.app"}, {"url": "https://rag-prompt-library.web.app/assets/js/vendor-react-core-DBAjAgoh.js", "sessionTargetType": "page", "protocol": "h3", "rendererStartTime": 961.8559999999125, "networkRequestTime": 962.3139999998966, "networkEndTime": 2320.381000000052, "finished": true, "transferSize": 180719, "resourceSize": 842850, "statusCode": 200, "mimeType": "text/javascript", "resourceType": "<PERSON><PERSON><PERSON>", "priority": "High", "experimentalFromMainFrame": true, "entity": "web.app"}, {"url": "https://rag-prompt-library.web.app/assets/js/components-common-Bz06cWsP.js", "sessionTargetType": "page", "protocol": "h3", "rendererStartTime": 962.012999999919, "networkRequestTime": 962.564000000013, "networkEndTime": 2558.689000000013, "finished": true, "transferSize": 67648, "resourceSize": 465832, "statusCode": 200, "mimeType": "text/javascript", "resourceType": "<PERSON><PERSON><PERSON>", "priority": "High", "experimentalFromMainFrame": true, "entity": "web.app"}, {"url": "https://rag-prompt-library.web.app/vite.svg", "sessionTargetType": "page", "protocol": "h3", "rendererStartTime": 2621.9509999998845, "networkRequestTime": 2622.572999999975, "networkEndTime": 3025.67499999993, "finished": true, "transferSize": 1794, "resourceSize": 1497, "statusCode": 200, "mimeType": "image/svg+xml", "resourceType": "Other", "priority": "High", "experimentalFromMainFrame": true, "entity": "web.app"}, {"url": "https://apis.google.com/js/api.js?onload=__iframefcb151178", "sessionTargetType": "page", "protocol": "h2", "rendererStartTime": 2666.8829999999143, "networkRequestTime": 2667.6559999998426, "networkEndTime": 2817.2399999998743, "finished": true, "transferSize": 6341, "resourceSize": 14503, "statusCode": 200, "mimeType": "text/javascript", "resourceType": "<PERSON><PERSON><PERSON>", "priority": "Low", "experimentalFromMainFrame": true, "entity": "Other Google APIs/SDKs"}, {"url": "https://apis.google.com/_/scs/abc-static/_/js/k=gapi.lb.en.iFs_Bhrqdio.O/m=gapi_iframes/rt=j/sv=1/d=1/ed=1/rs=AHpOoo-IvlQWaLHZdUKQFbafXd_3OEvU9Q/cb=gapi.loaded_0?le=scs", "sessionTargetType": "page", "protocol": "h2", "rendererStartTime": 2820.7339999999385, "networkRequestTime": 2821.4949999999953, "networkEndTime": 2890.915999999852, "finished": true, "transferSize": 34384, "resourceSize": 103605, "statusCode": 200, "mimeType": "text/javascript", "resourceType": "<PERSON><PERSON><PERSON>", "priority": "Low", "experimentalFromMainFrame": true, "entity": "Other Google APIs/SDKs"}, {"url": "https://rag-prompt-library.firebaseapp.com/__/auth/iframe?apiKey=AIzaSyDJWjw2e8FayU3CvIWyGXXFAqDCTFN5CJs&appName=%5BDEFAULT%5D&v=11.10.0&eid=p&usegapi=1&jsh=m%3B%2F_%2Fscs%2Fabc-static%2F_%2Fjs%2Fk%3Dgapi.lb.en.iFs_Bhrqdio.O%2Fd%3D1%2Frs%3DAHpOoo-IvlQWaLHZdUKQFbafXd_3OEvU9Q%2Fm%3D__features__", "sessionTargetType": "iframe", "protocol": "h2", "rendererStartTime": 2910.144999999902, "networkRequestTime": 2911.625, "networkEndTime": 3955.010999999824, "finished": true, "transferSize": 534, "resourceSize": 364, "statusCode": 200, "mimeType": "text/html", "resourceType": "Document", "priority": "VeryHigh", "entity": "firebaseapp.com"}, {"url": "https://rag-prompt-library.web.app/vite.svg", "sessionTargetType": "page", "protocol": "h3", "rendererStartTime": 2922.19299999997, "networkRequestTime": 2923.055999999866, "networkEndTime": 3026.3980000000447, "finished": true, "transferSize": 0, "resourceSize": 1497, "statusCode": 200, "mimeType": "image/svg+xml", "resourceType": "Other", "priority": "High", "experimentalFromMainFrame": true, "entity": "web.app"}, {"url": "https://rag-prompt-library.firebaseapp.com/__/auth/iframe.js", "sessionTargetType": "iframe", "protocol": "h2", "rendererStartTime": 3973.881999999867, "networkRequestTime": 3975.1500000000233, "networkEndTime": 5161.603999999934, "finished": true, "transferSize": 92182, "resourceSize": 289083, "statusCode": 200, "mimeType": "text/javascript", "resourceType": "<PERSON><PERSON><PERSON>", "priority": "High", "entity": "firebaseapp.com"}, {"url": "https://www.googleapis.com/identitytoolkit/v3/relyingparty/getProjectConfig?key=AIzaSyDJWjw2e8FayU3CvIWyGXXFAqDCTFN5CJs&cb=1753730490214", "sessionTargetType": "iframe", "protocol": "", "rendererStartTime": 5214.726000000024, "networkRequestTime": 5214.726000000024, "finished": false, "transferSize": 0, "resourceSize": 0, "statusCode": -1, "mimeType": "", "resourceType": "XHR", "priority": "High", "entity": "Other Google APIs/SDKs"}, {"url": "https://www.googleapis.com/identitytoolkit/v3/relyingparty/getProjectConfig?key=AIzaSyDJWjw2e8FayU3CvIWyGXXFAqDCTFN5CJs&cb=1753730490214", "sessionTargetType": "iframe", "protocol": "", "rendererStartTime": 5217.138000000035, "networkRequestTime": 5217.138000000035, "finished": false, "transferSize": 0, "resourceSize": 0, "statusCode": -1, "mimeType": "", "resourceType": "Other", "priority": "High", "entity": "Other Google APIs/SDKs"}], "debugData": {"type": "debugdata", "networkStartTimeTs": 1024722173.0000001}}}, "network-rtt": {"id": "network-rtt", "title": "Network Round Trip Times", "description": "Network round trip times (RTT) have a large impact on performance. If the RTT to an origin is high, it's an indication that servers closer to the user could improve performance. [Learn more about the Round Trip Time](https://hpbn.co/primer-on-latency-and-bandwidth/).", "score": 1, "scoreDisplayMode": "informative", "numericValue": 202.90800000000002, "numericUnit": "millisecond", "displayValue": "200 ms", "details": {"type": "table", "headings": [{"key": "origin", "valueType": "text", "label": "URL"}, {"key": "rtt", "valueType": "ms", "granularity": 1, "label": "Time Spent"}], "items": [{"origin": "https://rag-prompt-library.firebaseapp.com", "rtt": 202.90800000000002}, {"origin": "https://apis.google.com", "rtt": 41.788}, {"origin": "https://rag-prompt-library.web.app", "rtt": 0}], "sortedBy": ["rtt"]}}, "network-server-latency": {"id": "network-server-latency", "title": "Server Backend Latencies", "description": "Server latencies can impact web performance. If the server latency of an origin is high, it's an indication the server is overloaded or has poor backend performance. [Learn more about server response time](https://hpbn.co/primer-on-web-performance/#analyzing-the-resource-waterfall).", "score": 1, "scoreDisplayMode": "informative", "numericValue": 537.1755, "numericUnit": "millisecond", "displayValue": "540 ms", "details": {"type": "table", "headings": [{"key": "origin", "valueType": "text", "label": "URL"}, {"key": "serverResponseTime", "valueType": "ms", "granularity": 1, "label": "Time Spent"}], "items": [{"origin": "https://rag-prompt-library.firebaseapp.com", "serverResponseTime": 537.1755}, {"origin": "https://rag-prompt-library.web.app", "serverResponseTime": 297.559}, {"origin": "https://apis.google.com", "serverResponseTime": 2.7989999999999995}], "sortedBy": ["serverResponseTime"]}}, "main-thread-tasks": {"id": "main-thread-tasks", "title": "Tasks", "description": "Lists the toplevel main thread tasks that executed during page load.", "score": 1, "scoreDisplayMode": "informative", "details": {"type": "table", "headings": [{"key": "startTime", "valueType": "ms", "granularity": 1, "label": "Start Time"}, {"key": "duration", "valueType": "ms", "granularity": 1, "label": "End Time"}], "items": [{"duration": 13.992, "startTime": 548.154}, {"duration": 9.201, "startTime": 563.715}, {"duration": 12.527, "startTime": 863.228}, {"duration": 21.693, "startTime": 2568.805}, {"duration": 18.134, "startTime": 2590.519}, {"duration": 9.539, "startTime": 2609.686}, {"duration": 14.236, "startTime": 2896.162}, {"duration": 5.145, "startTime": 2912.236}, {"duration": 30.552, "startTime": 2925.763}, {"duration": 5.765, "startTime": 3029.175}]}}, "metrics": {"id": "metrics", "title": "Metrics", "description": "Collects all available metrics.", "score": 1, "scoreDisplayMode": "informative", "numericValue": 6186, "numericUnit": "millisecond", "details": {"type": "debugdata", "items": [{"firstContentfulPaint": 1083, "largestContentfulPaint": 4680, "interactive": 6186, "speedIndex": 6600, "totalBlockingTime": 73, "maxPotentialFID": 87, "cumulativeLayoutShift": 0, "cumulativeLayoutShiftMainFrame": 0, "timeToFirstByte": 898, "observedTimeOrigin": 0, "observedTimeOriginTs": 1024720210, "observedNavigationStart": 0, "observedNavigationStartTs": 1024720210, "observedFirstPaint": 947, "observedFirstPaintTs": 1025667036, "observedFirstContentfulPaint": 947, "observedFirstContentfulPaintTs": 1025667036, "observedFirstContentfulPaintAllFrames": 947, "observedFirstContentfulPaintAllFramesTs": 1025667036, "observedLargestContentfulPaint": 3099, "observedLargestContentfulPaintTs": 1027819256, "observedLargestContentfulPaintAllFrames": 3099, "observedLargestContentfulPaintAllFramesTs": 1027819256, "observedTraceEnd": 5383, "observedTraceEndTs": 1030103285, "observedLoad": 2608, "observedLoadTs": 1027328659, "observedDomContentLoaded": 2608, "observedDomContentLoadedTs": 1027328320, "observedCumulativeLayoutShift": 0, "observedCumulativeLayoutShiftMainFrame": 0, "observedFirstVisualChange": 1767, "observedFirstVisualChangeTs": 1026487210, "observedLastVisualChange": 3670, "observedLastVisualChangeTs": 1028390210, "observedSpeedIndex": 3638, "observedSpeedIndexTs": 1028358635}, {"lcpInvalidated": false}]}}, "resource-summary": {"id": "resource-summary", "title": "Resources Summary", "description": "Aggregates all network requests and groups them by type", "score": 1, "scoreDisplayMode": "informative", "details": {"type": "table", "headings": [{"key": "label", "valueType": "text", "label": "Resource Type"}, {"key": "requestCount", "valueType": "numeric", "label": "Requests"}, {"key": "transferSize", "valueType": "bytes", "label": "Transfer Size"}], "items": [{"resourceType": "total", "label": "Total", "requestCount": 13, "transferSize": 398556}, {"resourceType": "script", "label": "<PERSON><PERSON><PERSON>", "requestCount": 6, "transferSize": 383884}, {"resourceType": "stylesheet", "label": "Stylesheet", "requestCount": 1, "transferSize": 10366}, {"resourceType": "document", "label": "Document", "requestCount": 2, "transferSize": 2512}, {"resourceType": "other", "label": "Other", "requestCount": 4, "transferSize": 1794}, {"resourceType": "image", "label": "Image", "requestCount": 0, "transferSize": 0}, {"resourceType": "media", "label": "Media", "requestCount": 0, "transferSize": 0}, {"resourceType": "font", "label": "Font", "requestCount": 0, "transferSize": 0}, {"resourceType": "third-party", "label": "Third-party", "requestCount": 6, "transferSize": 133441}]}}, "third-party-summary": {"id": "third-party-summary", "title": "Minimize third-party usage", "description": "Third-party code can significantly impact load performance. Limit the number of redundant third-party providers and try to load third-party code after your page has primarily finished loading. [Learn how to minimize third-party impact](https://developers.google.com/web/fundamentals/performance/optimizing-content-efficiency/loading-third-party-javascript/).", "score": 1, "scoreDisplayMode": "informative", "displayValue": "Third-party code blocked the main thread for 10 ms", "metricSavings": {"TBT": 0}, "details": {"type": "table", "headings": [{"key": "entity", "valueType": "text", "label": "Third-Party", "subItemsHeading": {"key": "url", "valueType": "url"}}, {"key": "transferSize", "granularity": 1, "valueType": "bytes", "label": "Transfer Size", "subItemsHeading": {"key": "transferSize"}}, {"key": "blockingTime", "granularity": 1, "valueType": "ms", "label": "Main-Thread Blocking Time", "subItemsHeading": {"key": "blockingTime"}}], "items": [{"mainThreadTime": 85.36400000000005, "blockingTime": 6.999999999999999, "transferSize": 40725, "tbtImpact": 6.999999999999999, "entity": "Other Google APIs/SDKs", "subItems": {"type": "subitems", "items": [{"url": "https://apis.google.com/_/scs/abc-static/_/js/k=gapi.lb.en.iFs_Bhrqdio.O/m=gapi_iframes/rt=j/sv=1/d=1/ed=1/rs=AHpOoo-IvlQWaLHZdUKQFbafXd_3OEvU9Q/cb=gapi.loaded_0?le=scs", "mainThreadTime": 73.54800000000004, "blockingTime": 6.999999999999999, "transferSize": 34384, "tbtImpact": 6.999999999999999}, {"url": "https://apis.google.com/js/api.js?onload=__iframefcb151178", "mainThreadTime": 11.815999999999997, "blockingTime": 0, "transferSize": 6341, "tbtImpact": 0}, {"url": "https://www.googleapis.com/identitytoolkit/v3/relyingparty/getProjectConfig?key=AIzaSyDJWjw2e8FayU3CvIWyGXXFAqDCTFN5CJs&cb=1753730490214", "mainThreadTime": 0, "blockingTime": 0, "transferSize": 0, "tbtImpact": 0}]}}, {"mainThreadTime": 1.2719999999999998, "blockingTime": 0.11449332285937106, "transferSize": 92716, "tbtImpact": 0.11449332285937106, "entity": "firebaseapp.com", "subItems": {"type": "subitems", "items": [{"url": "https://rag-prompt-library.firebaseapp.com/__/auth/iframe?apiKey=AIzaSyDJWjw2e8FayU3CvIWyGXXFAqDCTFN5CJs&appName=%5BDEFAULT%5D&v=11.10.0&eid=p&usegapi=1&jsh=m%3B%2F_%2Fscs%2Fabc-static%2F_%2Fjs%2Fk%3Dgapi.lb.en.iFs_Bhrqdio.O%2Fd%3D1%2Frs%3DAHpOoo-IvlQWaLHZdUKQFbafXd_3OEvU9Q%2Fm%3D__features__", "mainThreadTime": 1.2719999999999998, "blockingTime": 0.11449332285937106, "transferSize": 534, "tbtImpact": 0.11449332285937106}, {"url": "https://rag-prompt-library.firebaseapp.com/__/auth/iframe.js", "mainThreadTime": 0, "blockingTime": 0, "transferSize": 92182, "tbtImpact": 0}]}}], "summary": {"wastedBytes": 133441, "wastedMs": 7.11449332285937}, "isEntityGrouped": true}, "guidanceLevel": 1}, "third-party-facades": {"id": "third-party-facades", "title": "Lazy load third-party resources with facades", "description": "Some third-party embeds can be lazy loaded. Consider replacing them with a facade until they are required. [Learn how to defer third-parties with a facade](https://developer.chrome.com/docs/lighthouse/performance/third-party-facades/).", "score": null, "scoreDisplayMode": "notApplicable", "metricSavings": {"TBT": 0}, "guidanceLevel": 3}, "largest-contentful-paint-element": {"id": "largest-contentful-paint-element", "title": "Largest Contentful Paint element", "description": "This is the largest contentful element painted within the viewport. [Learn more about the Largest Contentful Paint element](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)", "score": 0, "scoreDisplayMode": "metricSavings", "displayValue": "4,680 ms", "metricSavings": {"LCP": 2200}, "details": {"type": "list", "items": [{"type": "table", "headings": [{"key": "node", "valueType": "node", "label": "Element"}], "items": [{"node": {"type": "node", "lhId": "page-0-P", "path": "1,HTML,1,BODY,0,DIV,0,DIV,0,DIV,0,DIV,0,DIV,2,P", "selector": "div.min-h-screen > div.w-full > div.text-center > p.text-xl", "boundingRect": {"top": 232, "bottom": 344, "left": 16, "right": 396, "width": 380, "height": 112}, "snippet": "<p class=\"text-xl text-gray-200 mb-8\">", "nodeLabel": "Create, manage, and execute AI prompts with powerful retrieval-augmented genera…"}}]}, {"type": "table", "headings": [{"key": "phase", "valueType": "text", "label": "Phase"}, {"key": "percent", "valueType": "text", "label": "% of LCP"}, {"key": "timing", "valueType": "ms", "label": "Timing"}], "items": [{"phase": "TTFB", "timing": 897.559, "percent": "19%"}, {"phase": "<PERSON>ad <PERSON>", "timing": 0, "percent": "0%"}, {"phase": "Load Time", "timing": 0, "percent": "0%"}, {"phase": "Render Delay", "timing": 3782.90075, "percent": "81%"}]}]}, "guidanceLevel": 1}, "lcp-lazy-loaded": {"id": "lcp-lazy-loaded", "title": "Largest Contentful Paint image was not lazily loaded", "description": "Above-the-fold images that are lazily loaded render later in the page lifecycle, which can delay the largest contentful paint. [Learn more about optimal lazy loading](https://web.dev/articles/lcp-lazy-loading).", "score": null, "scoreDisplayMode": "notApplicable", "metricSavings": {"LCP": 0}, "guidanceLevel": 3}, "layout-shifts": {"id": "layout-shifts", "title": "Avoid large layout shifts", "description": "These are the largest layout shifts observed on the page. Each table item represents a single layout shift, and shows the element that shifted the most. Below each item are possible root causes that led to the layout shift. Some of these layout shifts may not be included in the CLS metric value due to [windowing](https://web.dev/articles/cls#what_is_cls). [Learn how to improve CLS](https://web.dev/articles/optimize-cls)", "score": null, "scoreDisplayMode": "notApplicable", "metricSavings": {"CLS": 0}, "details": {"type": "table", "headings": [{"key": "node", "valueType": "node", "subItemsHeading": {"key": "extra"}, "label": "Element"}, {"key": "score", "valueType": "numeric", "subItemsHeading": {"key": "cause", "valueType": "text"}, "granularity": 0.001, "label": "Layout shift score"}], "items": []}, "guidanceLevel": 2}, "long-tasks": {"id": "long-tasks", "title": "Avoid long main-thread tasks", "description": "Lists the longest tasks on the main thread, useful for identifying worst contributors to input delay. [Learn how to avoid long main-thread tasks](https://web.dev/articles/optimize-long-tasks)", "score": 1, "scoreDisplayMode": "informative", "displayValue": "5 long tasks found", "metricSavings": {"TBT": 50}, "details": {"type": "table", "headings": [{"key": "url", "valueType": "url", "label": "URL"}, {"key": "startTime", "valueType": "ms", "granularity": 1, "label": "Start Time"}, {"key": "duration", "valueType": "ms", "granularity": 1, "label": "Duration"}], "items": [{"url": "Unattributable", "duration": 87, "startTime": 1138.559}, {"url": "https://rag-prompt-library.web.app/", "duration": 73, "startTime": 3053.559}, {"url": "https://rag-prompt-library.web.app/", "duration": 61, "startTime": 6211.1975}, {"url": "https://apis.google.com/_/scs/abc-static/_/js/k=gapi.lb.en.iFs_Bhrqdio.O/m=gapi_iframes/rt=j/sv=1/d=1/ed=1/rs=AHpOoo-IvlQWaLHZdUKQFbafXd_3OEvU9Q/cb=gapi.loaded_0?le=scs", "duration": 57, "startTime": 4315.298000000001}, {"url": "Unattributable", "duration": 56, "startTime": 1047.559}], "sortedBy": ["duration"], "skipSumming": ["startTime"], "debugData": {"type": "debugdata", "urls": ["Unattributable", "https://rag-prompt-library.web.app/", "https://apis.google.com/_/scs/abc-static/_/js/k=gapi.lb.en.iFs_Bhrqdio.O/m=gapi_iframes/rt=j/sv=1/d=1/ed=1/rs=AHpOoo-IvlQWaLHZdUKQFbafXd_3OEvU9Q/cb=gapi.loaded_0?le=scs"], "tasks": [{"urlIndex": 0, "startTime": 1138.6, "duration": 87, "other": 87}, {"urlIndex": 1, "startTime": 3053.6, "duration": 73, "other": 73, "scriptEvaluation": 0, "styleLayout": 0}, {"urlIndex": 1, "startTime": 6211.2, "duration": 61, "other": 61, "paintCompositeRender": 0, "scriptEvaluation": 0, "styleLayout": 0}, {"urlIndex": 2, "startTime": 4315.3, "duration": 57, "other": 57, "scriptEvaluation": 0}, {"urlIndex": 0, "startTime": 1047.6, "duration": 56, "other": 56, "scriptEvaluation": 0}]}}, "guidanceLevel": 1}, "non-composited-animations": {"id": "non-composited-animations", "title": "Avoid non-composited animations", "description": "Animations which are not composited can be janky and increase CLS. [Learn how to avoid non-composited animations](https://developer.chrome.com/docs/lighthouse/performance/non-composited-animations/)", "score": null, "scoreDisplayMode": "notApplicable", "metricSavings": {"CLS": 0}, "details": {"type": "table", "headings": [{"key": "node", "valueType": "node", "subItemsHeading": {"key": "failureReason", "valueType": "text"}, "label": "Element"}], "items": []}, "guidanceLevel": 2}, "unsized-images": {"id": "unsized-images", "title": "Image elements have explicit `width` and `height`", "description": "Set an explicit width and height on image elements to reduce layout shifts and improve CLS. [Learn how to set image dimensions](https://web.dev/articles/optimize-cls#images_without_dimensions)", "score": 1, "scoreDisplayMode": "metricSavings", "metricSavings": {"CLS": 0}, "details": {"type": "table", "headings": [{"key": "node", "valueType": "node", "label": ""}, {"key": "url", "valueType": "url", "label": "URL"}], "items": []}, "guidanceLevel": 4}, "valid-source-maps": {"id": "valid-source-maps", "title": "Missing source maps for large first-party JavaScript", "description": "Source maps translate minified code to the original source code. This helps developers debug in production. In addition, Lighthouse is able to provide further insights. Consider deploying source maps to take advantage of these benefits. [Learn more about source maps](https://developer.chrome.com/docs/devtools/javascript/source-maps/).", "score": 0, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [{"key": "scriptUrl", "valueType": "url", "subItemsHeading": {"key": "error"}, "label": "URL"}, {"key": "sourceMapUrl", "valueType": "url", "label": "Map URL"}], "items": [{"scriptUrl": "https://rag-prompt-library.web.app/assets/js/vendor-react-core-DBAjAgoh.js", "subItems": {"type": "subitems", "items": [{"error": "Large JavaScript file is missing a source map"}]}}]}}, "prioritize-lcp-image": {"id": "prioritize-lcp-image", "title": "Preload Largest Contentful Paint image", "description": "If the LCP element is dynamically added to the page, you should preload the image in order to improve LCP. [Learn more about preloading LCP elements](https://web.dev/articles/optimize-lcp#optimize_when_the_resource_is_discovered).", "score": null, "scoreDisplayMode": "notApplicable", "metricSavings": {"LCP": 0}, "guidanceLevel": 4}, "csp-xss": {"id": "csp-xss", "title": "Ensure CSP is effective against XSS attacks", "description": "A strong Content Security Policy (CSP) significantly reduces the risk of cross-site scripting (XSS) attacks. [Learn how to use a CSP to prevent XSS](https://developer.chrome.com/docs/lighthouse/best-practices/csp-xss/)", "score": 1, "scoreDisplayMode": "informative", "details": {"type": "table", "headings": [{"key": "description", "valueType": "text", "subItemsHeading": {"key": "description"}, "label": "Description"}, {"key": "directive", "valueType": "code", "subItemsHeading": {"key": "directive"}, "label": "Directive"}, {"key": "severity", "valueType": "text", "subItemsHeading": {"key": "severity"}, "label": "Severity"}], "items": [{"directive": "script-src", "description": "Host allowlists can frequently be bypassed. Consider using CSP nonces or hashes instead, along with `'strict-dynamic'` if necessary.", "severity": "High"}, {"directive": "script-src", "description": "`'unsafe-inline'` allows the execution of unsafe in-page scripts and event handlers. Consider using CSP nonces or hashes to allow scripts individually.", "severity": "High"}, {"directive": "script-src", "description": "Avoid using plain URL schemes (data:) in this directive. Plain URL schemes allow scripts to be sourced from an unsafe domain.", "severity": "High"}, {"directive": "script-src", "description": "Avoid using plain URL schemes (https:) in this directive. Plain URL schemes allow scripts to be sourced from an unsafe domain.", "severity": "High"}]}}, "has-hsts": {"id": "has-hsts", "title": "Use a strong HSTS policy", "description": "Deployment of the HSTS header significantly reduces the risk of downgrading HTTP connections and eavesdropping attacks. A rollout in stages, starting with a low max-age is recommended. [Learn more about using a strong HSTS policy.](https://developer.chrome.com/docs/lighthouse/best-practices/has-hsts)", "score": null, "scoreDisplayMode": "notApplicable", "details": {"type": "table", "headings": [{"key": "description", "valueType": "text", "subItemsHeading": {"key": "description"}, "label": "Description"}, {"key": "directive", "valueType": "code", "subItemsHeading": {"key": "directive"}, "label": "Directive"}, {"key": "severity", "valueType": "text", "subItemsHeading": {"key": "severity"}, "label": "Severity"}], "items": []}}, "origin-isolation": {"id": "origin-isolation", "title": "Ensure proper origin isolation with COOP", "description": "The Cross-Origin-Opener-Policy (COOP) can be used to isolate the top-level window from other documents such as pop-ups. [Learn more about deploying the COOP header.](https://web.dev/articles/why-coop-coep#coop)", "score": null, "scoreDisplayMode": "notApplicable", "details": {"type": "table", "headings": [{"key": "description", "valueType": "text", "subItemsHeading": {"key": "description"}, "label": "Description"}, {"key": "directive", "valueType": "code", "subItemsHeading": {"key": "directive"}, "label": "Directive"}, {"key": "severity", "valueType": "text", "subItemsHeading": {"key": "severity"}, "label": "Severity"}], "items": []}}, "clickjacking-mitigation": {"id": "clickjacking-mitigation", "title": "Mitigate clickjacking with XFO or CSP", "description": "The `X-Frame-Options` (XFO) header or the `frame-ancestors` directive in the `Content-Security-Policy` (CSP) header control where a page can be embedded. These can mitigate clickjacking attacks by blocking some or all sites from embedding the page. [Learn more about mitigating clickjacking](https://developer.chrome.com/docs/lighthouse/best-practices/clickjacking-mitigation).", "score": null, "scoreDisplayMode": "notApplicable", "details": {"type": "table", "headings": [{"key": "description", "valueType": "text", "subItemsHeading": {"key": "description"}, "label": "Description"}, {"key": "severity", "valueType": "text", "subItemsHeading": {"key": "severity"}, "label": "Severity"}], "items": []}}, "trusted-types-xss": {"id": "trusted-types-xss", "title": "Mitigate DOM-based XSS with Trusted Types", "description": "The `require-trusted-types-for` directive in the `Content-Security-Policy` (CSP) header instructs user agents to control the data passed to DOM XSS sink functions. [Learn more about mitigating DOM-based XSS with Trusted Types](https://developer.chrome.com/docs/lighthouse/best-practices/trusted-types-xss).", "score": 1, "scoreDisplayMode": "informative", "details": {"type": "table", "headings": [{"key": "description", "valueType": "text", "subItemsHeading": {"key": "description"}, "label": "Description"}, {"key": "severity", "valueType": "text", "subItemsHeading": {"key": "severity"}, "label": "Severity"}], "items": [{"severity": "High", "description": "No `Content-Security-Policy` header with Trusted Types directive found"}]}}, "script-treemap-data": {"id": "script-treemap-data", "title": "Script Treemap Data", "description": "Used for treemap app", "score": 1, "scoreDisplayMode": "informative", "details": {"type": "treemap-data", "nodes": [{"name": "https://rag-prompt-library.web.app/assets/js/index-CrS6G9IP.js", "resourceBytes": 12666, "encodedBytes": 2454, "unusedBytes": 2317}, {"name": "https://rag-prompt-library.web.app/assets/js/vendor-react-core-DBAjAgoh.js", "resourceBytes": 842846, "encodedBytes": 179586, "unusedBytes": 465808}, {"name": "https://rag-prompt-library.web.app/assets/js/components-common-Bz06cWsP.js", "resourceBytes": 465706, "encodedBytes": 66516, "unusedBytes": 390762}, {"name": "https://apis.google.com/js/api.js?onload=__iframefcb151178", "resourceBytes": 14503, "encodedBytes": 250, "unusedBytes": 1262}, {"name": "https://apis.google.com/_/scs/abc-static/_/js/k=gapi.lb.en.iFs_Bhrqdio.O/m=gapi_iframes/rt=j/sv=1/d=1/ed=1/rs=AHpOoo-IvlQWaLHZdUKQFbafXd_3OEvU9Q/cb=gapi.loaded_0?le=scs", "resourceBytes": 103605, "encodedBytes": 31191, "unusedBytes": 46252}]}}, "accesskeys": {"id": "accesskeys", "title": "`[accesskey]` values are unique", "description": "Access keys let users quickly focus a part of the page. For proper navigation, each access key must be unique. [Learn more about access keys](https://dequeuniversity.com/rules/axe/4.10/accesskeys).", "score": null, "scoreDisplayMode": "notApplicable"}, "aria-allowed-attr": {"id": "aria-allowed-attr", "title": "`[aria-*]` attributes match their roles", "description": "Each ARIA `role` supports a specific subset of `aria-*` attributes. Mismatching these invalidates the `aria-*` attributes. [Learn how to match ARIA attributes to their roles](https://dequeuniversity.com/rules/axe/4.10/aria-allowed-attr).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [{"key": "node", "valueType": "node", "subItemsHeading": {"key": "relatedNode", "valueType": "node"}, "label": "Failing Elements"}], "items": []}}, "aria-allowed-role": {"id": "aria-allowed-role", "title": "Uses ARIA roles only on compatible elements", "description": "Many HTML elements can only be assigned certain ARIA roles. Using ARIA roles where they are not allowed can interfere with the accessibility of the web page. [Learn more about ARIA roles](https://dequeuniversity.com/rules/axe/4.10/aria-allowed-role).", "score": null, "scoreDisplayMode": "notApplicable"}, "aria-command-name": {"id": "aria-command-name", "title": "`button`, `link`, and `menuitem` elements have accessible names", "description": "When an element doesn't have an accessible name, screen readers announce it with a generic name, making it unusable for users who rely on screen readers. [Learn how to make command elements more accessible](https://dequeuniversity.com/rules/axe/4.10/aria-command-name).", "score": null, "scoreDisplayMode": "notApplicable"}, "aria-conditional-attr": {"id": "aria-conditional-attr", "title": "ARIA attributes are used as specified for the element's role", "description": "Some ARIA attributes are only allowed on an element under certain conditions. [Learn more about conditional ARIA attributes](https://dequeuniversity.com/rules/axe/4.10/aria-conditional-attr).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [{"key": "node", "valueType": "node", "subItemsHeading": {"key": "relatedNode", "valueType": "node"}, "label": "Failing Elements"}], "items": []}}, "aria-deprecated-role": {"id": "aria-deprecated-role", "title": "Deprecated ARIA roles were not used", "description": "Deprecated ARIA roles may not be processed correctly by assistive technology. [Learn more about deprecated ARIA roles](https://dequeuniversity.com/rules/axe/4.10/aria-deprecated-role).", "score": null, "scoreDisplayMode": "notApplicable"}, "aria-dialog-name": {"id": "aria-dialog-name", "title": "Elements with `role=\"dialog\"` or `role=\"alertdialog\"` have accessible names.", "description": "ARIA dialog elements without accessible names may prevent screen readers users from discerning the purpose of these elements. [Learn how to make ARIA dialog elements more accessible](https://dequeuniversity.com/rules/axe/4.10/aria-dialog-name).", "score": null, "scoreDisplayMode": "notApplicable"}, "aria-hidden-body": {"id": "aria-hidden-body", "title": "`[aria-hidden=\"true\"]` is not present on the document `<body>`", "description": "Assistive technologies, like screen readers, work inconsistently when `aria-hidden=\"true\"` is set on the document `<body>`. [Learn how `aria-hidden` affects the document body](https://dequeuniversity.com/rules/axe/4.10/aria-hidden-body).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [{"key": "node", "valueType": "node", "subItemsHeading": {"key": "relatedNode", "valueType": "node"}, "label": "Failing Elements"}], "items": []}}, "aria-hidden-focus": {"id": "aria-hidden-focus", "title": "`[aria-hidden=\"true\"]` elements do not contain focusable descendents", "description": "Focusable descendents within an `[aria-hidden=\"true\"]` element prevent those interactive elements from being available to users of assistive technologies like screen readers. [Learn how `aria-hidden` affects focusable elements](https://dequeuniversity.com/rules/axe/4.10/aria-hidden-focus).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [{"key": "node", "valueType": "node", "subItemsHeading": {"key": "relatedNode", "valueType": "node"}, "label": "Failing Elements"}], "items": []}}, "aria-input-field-name": {"id": "aria-input-field-name", "title": "ARIA input fields have accessible names", "description": "When an input field doesn't have an accessible name, screen readers announce it with a generic name, making it unusable for users who rely on screen readers. [Learn more about input field labels](https://dequeuniversity.com/rules/axe/4.10/aria-input-field-name).", "score": null, "scoreDisplayMode": "notApplicable"}, "aria-meter-name": {"id": "aria-meter-name", "title": "ARIA `meter` elements have accessible names", "description": "When a meter element doesn't have an accessible name, screen readers announce it with a generic name, making it unusable for users who rely on screen readers. [Learn how to name `meter` elements](https://dequeuniversity.com/rules/axe/4.10/aria-meter-name).", "score": null, "scoreDisplayMode": "notApplicable"}, "aria-progressbar-name": {"id": "aria-progressbar-name", "title": "ARIA `progressbar` elements have accessible names", "description": "When a `progressbar` element doesn't have an accessible name, screen readers announce it with a generic name, making it unusable for users who rely on screen readers. [Learn how to label `progressbar` elements](https://dequeuniversity.com/rules/axe/4.10/aria-progressbar-name).", "score": null, "scoreDisplayMode": "notApplicable"}, "aria-prohibited-attr": {"id": "aria-prohibited-attr", "title": "Elements use only permitted ARIA attributes", "description": "Using ARIA attributes in roles where they are prohibited can mean that important information is not communicated to users of assistive technologies. [Learn more about prohibited ARIA roles](https://dequeuniversity.com/rules/axe/4.10/aria-prohibited-attr).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [{"key": "node", "valueType": "node", "subItemsHeading": {"key": "relatedNode", "valueType": "node"}, "label": "Failing Elements"}], "items": []}}, "aria-required-attr": {"id": "aria-required-attr", "title": "`[role]`s have all required `[aria-*]` attributes", "description": "Some ARIA roles have required attributes that describe the state of the element to screen readers. [Learn more about roles and required attributes](https://dequeuniversity.com/rules/axe/4.10/aria-required-attr).", "score": null, "scoreDisplayMode": "notApplicable"}, "aria-required-children": {"id": "aria-required-children", "title": "Elements with an ARIA `[role]` that require children to contain a specific `[role]` have all required children.", "description": "Some ARIA parent roles must contain specific child roles to perform their intended accessibility functions. [Learn more about roles and required children elements](https://dequeuniversity.com/rules/axe/4.10/aria-required-children).", "score": null, "scoreDisplayMode": "notApplicable"}, "aria-required-parent": {"id": "aria-required-parent", "title": "`[role]`s are contained by their required parent element", "description": "Some ARIA child roles must be contained by specific parent roles to properly perform their intended accessibility functions. [Learn more about ARIA roles and required parent element](https://dequeuniversity.com/rules/axe/4.10/aria-required-parent).", "score": null, "scoreDisplayMode": "notApplicable"}, "aria-roles": {"id": "aria-roles", "title": "`[role]` values are valid", "description": "ARIA roles must have valid values in order to perform their intended accessibility functions. [Learn more about valid ARIA roles](https://dequeuniversity.com/rules/axe/4.10/aria-roles).", "score": null, "scoreDisplayMode": "notApplicable"}, "aria-text": {"id": "aria-text", "title": "Elements with the `role=text` attribute do not have focusable descendents.", "description": "Adding `role=text` around a text node split by markup enables VoiceOver to treat it as one phrase, but the element's focusable descendents will not be announced. [Learn more about the `role=text` attribute](https://dequeuniversity.com/rules/axe/4.10/aria-text).", "score": null, "scoreDisplayMode": "notApplicable"}, "aria-toggle-field-name": {"id": "aria-toggle-field-name", "title": "ARIA toggle fields have accessible names", "description": "When a toggle field doesn't have an accessible name, screen readers announce it with a generic name, making it unusable for users who rely on screen readers. [Learn more about toggle fields](https://dequeuniversity.com/rules/axe/4.10/aria-toggle-field-name).", "score": null, "scoreDisplayMode": "notApplicable"}, "aria-tooltip-name": {"id": "aria-tooltip-name", "title": "ARIA `tooltip` elements have accessible names", "description": "When a tooltip element doesn't have an accessible name, screen readers announce it with a generic name, making it unusable for users who rely on screen readers. [Learn how to name `tooltip` elements](https://dequeuniversity.com/rules/axe/4.10/aria-tooltip-name).", "score": null, "scoreDisplayMode": "notApplicable"}, "aria-treeitem-name": {"id": "aria-treeitem-name", "title": "ARIA `treeitem` elements have accessible names", "description": "When a `treeitem` element doesn't have an accessible name, screen readers announce it with a generic name, making it unusable for users who rely on screen readers. [Learn more about labeling `treeitem` elements](https://dequeuniversity.com/rules/axe/4.10/aria-treeitem-name).", "score": null, "scoreDisplayMode": "notApplicable"}, "aria-valid-attr-value": {"id": "aria-valid-attr-value", "title": "`[aria-*]` attributes have valid values", "description": "Assistive technologies, like screen readers, can't interpret ARIA attributes with invalid values. [Learn more about valid values for ARIA attributes](https://dequeuniversity.com/rules/axe/4.10/aria-valid-attr-value).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [{"key": "node", "valueType": "node", "subItemsHeading": {"key": "relatedNode", "valueType": "node"}, "label": "Failing Elements"}], "items": []}}, "aria-valid-attr": {"id": "aria-valid-attr", "title": "`[aria-*]` attributes are valid and not misspelled", "description": "Assistive technologies, like screen readers, can't interpret ARIA attributes with invalid names. [Learn more about valid ARIA attributes](https://dequeuniversity.com/rules/axe/4.10/aria-valid-attr).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [{"key": "node", "valueType": "node", "subItemsHeading": {"key": "relatedNode", "valueType": "node"}, "label": "Failing Elements"}], "items": []}}, "button-name": {"id": "button-name", "title": "Buttons do not have an accessible name", "description": "When a button doesn't have an accessible name, screen readers announce it as \"button\", making it unusable for users who rely on screen readers. [Learn how to make buttons more accessible](https://dequeuniversity.com/rules/axe/4.10/button-name).", "score": 0, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [{"key": "node", "valueType": "node", "subItemsHeading": {"key": "relatedNode", "valueType": "node"}, "label": "Failing Elements"}], "items": [{"node": {"type": "node", "lhId": "1-0-BUTTON", "path": "1,H<PERSON>L,1,BODY,0,DIV,1,DIV,1,DIV,2,BUTTON", "selector": "div#root > div.fixed > div.absolute > button.absolute", "boundingRect": {"top": 8, "bottom": 28, "left": 356, "right": 376, "width": 20, "height": 20}, "snippet": "<button class=\"absolute top-2 right-2 text-gray-400 hover:text-gray-600\">", "nodeLabel": "div#root > div.fixed > div.absolute > button.absolute", "explanation": "Fix any of the following:\n  Element does not have inner text that is visible to screen readers\n  aria-label attribute does not exist or is empty\n  aria-labelledby attribute does not exist, references elements that do not exist or references elements that are empty\n  Element has no title attribute\n  Element does not have an implicit (wrapped) <label>\n  Element does not have an explicit <label>\n  Element's default semantics were not overridden with role=\"none\" or role=\"presentation\""}}], "debugData": {"type": "debugdata", "impact": "critical", "tags": ["cat.name-role-value", "wcag2a", "wcag412", "section508", "section508.22.a", "TTv5", "TT6.a", "EN-301-549", "EN-*******", "ACT"]}}}, "bypass": {"id": "bypass", "title": "The page contains a heading, skip link, or landmark region", "description": "Adding ways to bypass repetitive content lets keyboard users navigate the page more efficiently. [Learn more about bypass blocks](https://dequeuniversity.com/rules/axe/4.10/bypass).", "score": null, "scoreDisplayMode": "notApplicable"}, "color-contrast": {"id": "color-contrast", "title": "Background and foreground colors have a sufficient contrast ratio", "description": "Low-contrast text is difficult or impossible for many users to read. [Learn how to provide sufficient color contrast](https://dequeuniversity.com/rules/axe/4.10/color-contrast).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [{"key": "node", "valueType": "node", "subItemsHeading": {"key": "relatedNode", "valueType": "node"}, "label": "Failing Elements"}], "items": []}}, "definition-list": {"id": "definition-list", "title": "`<dl>`'s contain only properly-ordered `<dt>` and `<dd>` groups, `<script>`, `<template>` or `<div>` elements.", "description": "When definition lists are not properly marked up, screen readers may produce confusing or inaccurate output. [Learn how to structure definition lists correctly](https://dequeuniversity.com/rules/axe/4.10/definition-list).", "score": null, "scoreDisplayMode": "notApplicable"}, "dlitem": {"id": "dlitem", "title": "Definition list items are wrapped in `<dl>` elements", "description": "Definition list items (`<dt>` and `<dd>`) must be wrapped in a parent `<dl>` element to ensure that screen readers can properly announce them. [Learn how to structure definition lists correctly](https://dequeuniversity.com/rules/axe/4.10/dlitem).", "score": null, "scoreDisplayMode": "notApplicable"}, "document-title": {"id": "document-title", "title": "Document has a `<title>` element", "description": "The title gives screen reader users an overview of the page, and search engine users rely on it heavily to determine if a page is relevant to their search. [Learn more about document titles](https://dequeuniversity.com/rules/axe/4.10/document-title).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [{"key": "node", "valueType": "node", "subItemsHeading": {"key": "relatedNode", "valueType": "node"}, "label": "Failing Elements"}], "items": []}}, "duplicate-id-aria": {"id": "duplicate-id-aria", "title": "ARIA IDs are unique", "description": "The value of an ARIA ID must be unique to prevent other instances from being overlooked by assistive technologies. [Learn how to fix duplicate ARIA IDs](https://dequeuniversity.com/rules/axe/4.10/duplicate-id-aria).", "score": null, "scoreDisplayMode": "notApplicable"}, "empty-heading": {"id": "empty-heading", "title": "All heading elements contain content.", "description": "A heading with no content or inaccessible text prevent screen reader users from accessing information on the page's structure. [Learn more about headings](https://dequeuniversity.com/rules/axe/4.10/empty-heading).", "score": null, "scoreDisplayMode": "notApplicable"}, "form-field-multiple-labels": {"id": "form-field-multiple-labels", "title": "No form fields have multiple labels", "description": "Form fields with multiple labels can be confusingly announced by assistive technologies like screen readers which use either the first, the last, or all of the labels. [Learn how to use form labels](https://dequeuniversity.com/rules/axe/4.10/form-field-multiple-labels).", "score": null, "scoreDisplayMode": "notApplicable"}, "frame-title": {"id": "frame-title", "title": "`<frame>` or `<iframe>` elements have a title", "description": "Screen reader users rely on frame titles to describe the contents of frames. [Learn more about frame titles](https://dequeuniversity.com/rules/axe/4.10/frame-title).", "score": null, "scoreDisplayMode": "notApplicable"}, "heading-order": {"id": "heading-order", "title": "Heading elements appear in a sequentially-descending order", "description": "Properly ordered headings that do not skip levels convey the semantic structure of the page, making it easier to navigate and understand when using assistive technologies. [Learn more about heading order](https://dequeuniversity.com/rules/axe/4.10/heading-order).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [{"key": "node", "valueType": "node", "subItemsHeading": {"key": "relatedNode", "valueType": "node"}, "label": "Failing Elements"}], "items": []}}, "html-has-lang": {"id": "html-has-lang", "title": "`<html>` element has a `[lang]` attribute", "description": "If a page doesn't specify a `lang` attribute, a screen reader assumes that the page is in the default language that the user chose when setting up the screen reader. If the page isn't actually in the default language, then the screen reader might not announce the page's text correctly. [Learn more about the `lang` attribute](https://dequeuniversity.com/rules/axe/4.10/html-has-lang).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [{"key": "node", "valueType": "node", "subItemsHeading": {"key": "relatedNode", "valueType": "node"}, "label": "Failing Elements"}], "items": []}}, "html-lang-valid": {"id": "html-lang-valid", "title": "`<html>` element has a valid value for its `[lang]` attribute", "description": "Specifying a valid [BCP 47 language](https://www.w3.org/International/questions/qa-choosing-language-tags#question) helps screen readers announce text properly. [Learn how to use the `lang` attribute](https://dequeuniversity.com/rules/axe/4.10/html-lang-valid).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [{"key": "node", "valueType": "node", "subItemsHeading": {"key": "relatedNode", "valueType": "node"}, "label": "Failing Elements"}], "items": []}}, "html-xml-lang-mismatch": {"id": "html-xml-lang-mismatch", "title": "`<html>` element has an `[xml:lang]` attribute with the same base language as the `[lang]` attribute.", "description": "If the webpage does not specify a consistent language, then the screen reader might not announce the page's text correctly. [Learn more about the `lang` attribute](https://dequeuniversity.com/rules/axe/4.10/html-xml-lang-mismatch).", "score": null, "scoreDisplayMode": "notApplicable"}, "identical-links-same-purpose": {"id": "identical-links-same-purpose", "title": "Identical links have the same purpose.", "description": "Links with the same destination should have the same description, to help users understand the link's purpose and decide whether to follow it. [Learn more about identical links](https://dequeuniversity.com/rules/axe/4.10/identical-links-same-purpose).", "score": null, "scoreDisplayMode": "notApplicable"}, "image-alt": {"id": "image-alt", "title": "Image elements have `[alt]` attributes", "description": "Informative elements should aim for short, descriptive alternate text. Decorative elements can be ignored with an empty alt attribute. [Learn more about the `alt` attribute](https://dequeuniversity.com/rules/axe/4.10/image-alt).", "score": null, "scoreDisplayMode": "notApplicable"}, "image-redundant-alt": {"id": "image-redundant-alt", "title": "Image elements do not have `[alt]` attributes that are redundant text.", "description": "Informative elements should aim for short, descriptive alternative text. Alternative text that is exactly the same as the text adjacent to the link or image is potentially confusing for screen reader users, because the text will be read twice. [Learn more about the `alt` attribute](https://dequeuniversity.com/rules/axe/4.10/image-redundant-alt).", "score": null, "scoreDisplayMode": "notApplicable"}, "input-button-name": {"id": "input-button-name", "title": "Input buttons have discernible text.", "description": "Adding discernable and accessible text to input buttons may help screen reader users understand the purpose of the input button. [Learn more about input buttons](https://dequeuniversity.com/rules/axe/4.10/input-button-name).", "score": null, "scoreDisplayMode": "notApplicable"}, "input-image-alt": {"id": "input-image-alt", "title": "`<input type=\"image\">` elements have `[alt]` text", "description": "When an image is being used as an `<input>` button, providing alternative text can help screen reader users understand the purpose of the button. [Learn about input image alt text](https://dequeuniversity.com/rules/axe/4.10/input-image-alt).", "score": null, "scoreDisplayMode": "notApplicable"}, "label-content-name-mismatch": {"id": "label-content-name-mismatch", "title": "Elements with visible text labels have matching accessible names.", "description": "Visible text labels that do not match the accessible name can result in a confusing experience for screen reader users. [Learn more about accessible names](https://dequeuniversity.com/rules/axe/4.10/label-content-name-mismatch).", "score": null, "scoreDisplayMode": "notApplicable"}, "label": {"id": "label", "title": "Form elements have associated labels", "description": "Labels ensure that form controls are announced properly by assistive technologies, like screen readers. [Learn more about form element labels](https://dequeuniversity.com/rules/axe/4.10/label).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [{"key": "node", "valueType": "node", "subItemsHeading": {"key": "relatedNode", "valueType": "node"}, "label": "Failing Elements"}], "items": []}}, "landmark-one-main": {"id": "landmark-one-main", "title": "Document has a main landmark.", "description": "One main landmark helps screen reader users navigate a web page. [Learn more about landmarks](https://dequeuniversity.com/rules/axe/4.10/landmark-one-main).", "score": null, "scoreDisplayMode": "notApplicable"}, "link-name": {"id": "link-name", "title": "Links have a discernible name", "description": "Link text (and alternate text for images, when used as links) that is discernible, unique, and focusable improves the navigation experience for screen reader users. [Learn how to make links accessible](https://dequeuniversity.com/rules/axe/4.10/link-name).", "score": null, "scoreDisplayMode": "notApplicable"}, "link-in-text-block": {"id": "link-in-text-block", "title": "Links are distinguishable without relying on color.", "description": "Low-contrast text is difficult or impossible for many users to read. Link text that is discernible improves the experience for users with low vision. [Learn how to make links distinguishable](https://dequeuniversity.com/rules/axe/4.10/link-in-text-block).", "score": null, "scoreDisplayMode": "notApplicable"}, "list": {"id": "list", "title": "Lists contain only `<li>` elements and script supporting elements (`<script>` and `<template>`).", "description": "Screen readers have a specific way of announcing lists. Ensuring proper list structure aids screen reader output. [Learn more about proper list structure](https://dequeuniversity.com/rules/axe/4.10/list).", "score": null, "scoreDisplayMode": "notApplicable"}, "listitem": {"id": "listitem", "title": "List items (`<li>`) are contained within `<ul>`, `<ol>` or `<menu>` parent elements", "description": "Screen readers require list items (`<li>`) to be contained within a parent `<ul>`, `<ol>` or `<menu>` to be announced properly. [Learn more about proper list structure](https://dequeuniversity.com/rules/axe/4.10/listitem).", "score": null, "scoreDisplayMode": "notApplicable"}, "meta-refresh": {"id": "meta-refresh", "title": "The document does not use `<meta http-equiv=\"refresh\">`", "description": "Users do not expect a page to refresh automatically, and doing so will move focus back to the top of the page. This may create a frustrating or confusing experience. [Learn more about the refresh meta tag](https://dequeuniversity.com/rules/axe/4.10/meta-refresh).", "score": null, "scoreDisplayMode": "notApplicable"}, "meta-viewport": {"id": "meta-viewport", "title": "`[user-scalable=\"no\"]` is not used in the `<meta name=\"viewport\">` element and the `[maximum-scale]` attribute is not less than 5.", "description": "Disabling zooming is problematic for users with low vision who rely on screen magnification to properly see the contents of a web page. [Learn more about the viewport meta tag](https://dequeuniversity.com/rules/axe/4.10/meta-viewport).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [{"key": "node", "valueType": "node", "subItemsHeading": {"key": "relatedNode", "valueType": "node"}, "label": "Failing Elements"}], "items": []}}, "object-alt": {"id": "object-alt", "title": "`<object>` elements have alternate text", "description": "Screen readers cannot translate non-text content. Adding alternate text to `<object>` elements helps screen readers convey meaning to users. [Learn more about alt text for `object` elements](https://dequeuniversity.com/rules/axe/4.10/object-alt).", "score": null, "scoreDisplayMode": "notApplicable"}, "select-name": {"id": "select-name", "title": "Select elements have associated label elements.", "description": "Form elements without effective labels can create frustrating experiences for screen reader users. [Learn more about the `select` element](https://dequeuniversity.com/rules/axe/4.10/select-name).", "score": null, "scoreDisplayMode": "notApplicable"}, "skip-link": {"id": "skip-link", "title": "Skip links are focusable.", "description": "Including a skip link can help users skip to the main content to save time. [Learn more about skip links](https://dequeuniversity.com/rules/axe/4.10/skip-link).", "score": null, "scoreDisplayMode": "notApplicable"}, "tabindex": {"id": "tabindex", "title": "No element has a `[tabindex]` value greater than 0", "description": "A value greater than 0 implies an explicit navigation ordering. Although technically valid, this often creates frustrating experiences for users who rely on assistive technologies. [Learn more about the `tabindex` attribute](https://dequeuniversity.com/rules/axe/4.10/tabindex).", "score": null, "scoreDisplayMode": "notApplicable"}, "table-duplicate-name": {"id": "table-duplicate-name", "title": "Tables have different content in the summary attribute and `<caption>`.", "description": "The summary attribute should describe the table structure, while `<caption>` should have the onscreen title. Accurate table mark-up helps users of screen readers. [Learn more about summary and caption](https://dequeuniversity.com/rules/axe/4.10/table-duplicate-name).", "score": null, "scoreDisplayMode": "notApplicable"}, "table-fake-caption": {"id": "table-fake-caption", "title": "Tables use `<caption>` instead of cells with the `[colspan]` attribute to indicate a caption.", "description": "Screen readers have features to make navigating tables easier. Ensuring that tables use the actual caption element instead of cells with the `[colspan]` attribute may improve the experience for screen reader users. [Learn more about captions](https://dequeuniversity.com/rules/axe/4.10/table-fake-caption).", "score": null, "scoreDisplayMode": "notApplicable"}, "target-size": {"id": "target-size", "title": "Touch targets do not have sufficient size or spacing.", "description": "Touch targets with sufficient size and spacing help users who may have difficulty targeting small controls to activate the targets. [Learn more about touch targets](https://dequeuniversity.com/rules/axe/4.10/target-size).", "score": 0, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [{"key": "node", "valueType": "node", "subItemsHeading": {"key": "relatedNode", "valueType": "node"}, "label": "Failing Elements"}], "items": [{"node": {"type": "node", "lhId": "1-1-BUTTON", "path": "1,HTML,1,BODY,0,DIV,0,DIV,0,DIV,0,DIV,1,DIV,0,DIV,0,DIV,1,FORM,1,DIV,1,DIV,2,BUTTON", "selector": "form.space-y-4 > div > div.relative > button.absolute", "boundingRect": {"top": 829, "bottom": 849, "left": 340, "right": 360, "width": 20, "height": 20}, "snippet": "<button type=\"button\" class=\"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:te…\" aria-label=\"Toggle password visibility\">", "nodeLabel": "Toggle password visibility", "explanation": "Fix any of the following:\n  Target has insufficient size (20px by 20px, should be at least 24px by 24px)\n  Target has insufficient space to its closest neighbors. Safe clickable space has a diameter of 20px instead of at least 24px."}, "subItems": {"type": "subitems", "items": [{"relatedNode": {"type": "node", "lhId": "1-2-INPUT", "path": "1,HTML,1,BODY,0,DIV,0,DIV,0,DIV,0,DIV,1,DIV,0,DIV,0,DIV,1,FORM,1,DIV,1,DIV,1,INPUT", "selector": "form.space-y-4 > div > div.relative > input#password", "boundingRect": {"top": 818, "bottom": 860, "left": 40, "right": 372, "width": 332, "height": 42}, "snippet": "<input id=\"password\" type=\"password\" class=\"w-full pl-10 pr-10 py-2 border rounded-md focus:outline-none focus:ring-2 …\" placeholder=\"Enter your password\" autocomplete=\"current-password\" required=\"\" value=\"\">", "nodeLabel": "form.space-y-4 > div > div.relative > input#password"}}]}}], "debugData": {"type": "debugdata", "impact": "serious", "tags": ["cat.sensory-and-visual-cues", "wcag22aa", "wcag258"]}}}, "td-has-header": {"id": "td-has-header", "title": "`<td>` elements in a large `<table>` have one or more table headers.", "description": "Screen readers have features to make navigating tables easier. Ensuring that `<td>` elements in a large table (3 or more cells in width and height) have an associated table header may improve the experience for screen reader users. [Learn more about table headers](https://dequeuniversity.com/rules/axe/4.10/td-has-header).", "score": null, "scoreDisplayMode": "notApplicable"}, "td-headers-attr": {"id": "td-headers-attr", "title": "Cells in a `<table>` element that use the `[headers]` attribute refer to table cells within the same table.", "description": "Screen readers have features to make navigating tables easier. Ensuring `<td>` cells using the `[headers]` attribute only refer to other cells in the same table may improve the experience for screen reader users. [Learn more about the `headers` attribute](https://dequeuniversity.com/rules/axe/4.10/td-headers-attr).", "score": null, "scoreDisplayMode": "notApplicable"}, "th-has-data-cells": {"id": "th-has-data-cells", "title": "`<th>` elements and elements with `[role=\"columnheader\"/\"rowheader\"]` have data cells they describe.", "description": "Screen readers have features to make navigating tables easier. Ensuring table headers always refer to some set of cells may improve the experience for screen reader users. [Learn more about table headers](https://dequeuniversity.com/rules/axe/4.10/th-has-data-cells).", "score": null, "scoreDisplayMode": "notApplicable"}, "valid-lang": {"id": "valid-lang", "title": "`[lang]` attributes have a valid value", "description": "Specifying a valid [BCP 47 language](https://www.w3.org/International/questions/qa-choosing-language-tags#question) on elements helps ensure that text is pronounced correctly by a screen reader. [Learn how to use the `lang` attribute](https://dequeuniversity.com/rules/axe/4.10/valid-lang).", "score": null, "scoreDisplayMode": "notApplicable"}, "video-caption": {"id": "video-caption", "title": "`<video>` elements contain a `<track>` element with `[kind=\"captions\"]`", "description": "When a video provides a caption it is easier for deaf and hearing impaired users to access its information. [Learn more about video captions](https://dequeuniversity.com/rules/axe/4.10/video-caption).", "score": null, "scoreDisplayMode": "notApplicable"}, "custom-controls-labels": {"id": "custom-controls-labels", "title": "Custom controls have associated labels", "description": "Custom interactive controls have associated labels, provided by aria-label or aria-labelledby. [Learn more about custom controls and labels](https://developer.chrome.com/docs/lighthouse/accessibility/custom-controls-labels/).", "score": null, "scoreDisplayMode": "manual"}, "custom-controls-roles": {"id": "custom-controls-roles", "title": "Custom controls have ARIA roles", "description": "Custom interactive controls have appropriate ARIA roles. [Learn how to add roles to custom controls](https://developer.chrome.com/docs/lighthouse/accessibility/custom-control-roles/).", "score": null, "scoreDisplayMode": "manual"}, "focus-traps": {"id": "focus-traps", "title": "User focus is not accidentally trapped in a region", "description": "A user can tab into and out of any control or region without accidentally trapping their focus. [Learn how to avoid focus traps](https://developer.chrome.com/docs/lighthouse/accessibility/focus-traps/).", "score": null, "scoreDisplayMode": "manual"}, "focusable-controls": {"id": "focusable-controls", "title": "Interactive controls are keyboard focusable", "description": "Custom interactive controls are keyboard focusable and display a focus indicator. [Learn how to make custom controls focusable](https://developer.chrome.com/docs/lighthouse/accessibility/focusable-controls/).", "score": null, "scoreDisplayMode": "manual"}, "interactive-element-affordance": {"id": "interactive-element-affordance", "title": "Interactive elements indicate their purpose and state", "description": "Interactive elements, such as links and buttons, should indicate their state and be distinguishable from non-interactive elements. [Learn how to decorate interactive elements with affordance hints](https://developer.chrome.com/docs/lighthouse/accessibility/interactive-element-affordance/).", "score": null, "scoreDisplayMode": "manual"}, "logical-tab-order": {"id": "logical-tab-order", "title": "The page has a logical tab order", "description": "Tabbing through the page follows the visual layout. Users cannot focus elements that are offscreen. [Learn more about logical tab ordering](https://developer.chrome.com/docs/lighthouse/accessibility/logical-tab-order/).", "score": null, "scoreDisplayMode": "manual"}, "managed-focus": {"id": "managed-focus", "title": "The user's focus is directed to new content added to the page", "description": "If new content, such as a dialog, is added to the page, the user's focus is directed to it. [Learn how to direct focus to new content](https://developer.chrome.com/docs/lighthouse/accessibility/managed-focus/).", "score": null, "scoreDisplayMode": "manual"}, "offscreen-content-hidden": {"id": "offscreen-content-hidden", "title": "Offscreen content is hidden from assistive technology", "description": "Offscreen content is hidden with display: none or aria-hidden=true. [Learn how to properly hide offscreen content](https://developer.chrome.com/docs/lighthouse/accessibility/offscreen-content-hidden/).", "score": null, "scoreDisplayMode": "manual"}, "use-landmarks": {"id": "use-landmarks", "title": "HTML5 landmark elements are used to improve navigation", "description": "Landmark elements (`<main>`, `<nav>`, etc.) are used to improve the keyboard navigation of the page for assistive technology. [Learn more about landmark elements](https://developer.chrome.com/docs/lighthouse/accessibility/use-landmarks/).", "score": null, "scoreDisplayMode": "manual"}, "visual-order-follows-dom": {"id": "visual-order-follows-dom", "title": "Visual order on the page follows DOM order", "description": "DOM order matches the visual order, improving navigation for assistive technology. [Learn more about DOM and visual ordering](https://developer.chrome.com/docs/lighthouse/accessibility/visual-order-follows-dom/).", "score": null, "scoreDisplayMode": "manual"}, "uses-long-cache-ttl": {"id": "uses-long-cache-ttl", "title": "Serve static assets with an efficient cache policy", "description": "A long cache lifetime can speed up repeat visits to your page. [Learn more about efficient cache policies](https://developer.chrome.com/docs/lighthouse/performance/uses-long-cache-ttl/).", "score": 0.5, "scoreDisplayMode": "metricSavings", "numericValue": 79506.975, "numericUnit": "byte", "displayValue": "1 resource found", "details": {"type": "table", "headings": [{"key": "url", "valueType": "url", "label": "URL"}, {"key": "cacheLifetimeMs", "valueType": "ms", "label": "<PERSON><PERSON>", "displayUnit": "duration"}, {"key": "totalBytes", "valueType": "bytes", "label": "Transfer Size", "displayUnit": "kb", "granularity": 1}], "items": [{"url": "https://rag-prompt-library.firebaseapp.com/__/auth/iframe.js", "debugData": {"type": "debugdata", "max-age": 1800}, "cacheLifetimeMs": 1800000, "cacheHitProbability": 0.1375, "totalBytes": 92182, "wastedBytes": 79506.975}], "summary": {"wastedBytes": 79506.975}, "sortedBy": ["totalBytes"], "skipSumming": ["cacheLifetimeMs"]}, "guidanceLevel": 3}, "total-byte-weight": {"id": "total-byte-weight", "title": "Avoids enormous network payloads", "description": "Large network payloads cost users real money and are highly correlated with long load times. [Learn how to reduce payload sizes](https://developer.chrome.com/docs/lighthouse/performance/total-byte-weight/).", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 398556, "numericUnit": "byte", "displayValue": "Total size was 389 KiB", "details": {"type": "table", "headings": [{"key": "url", "valueType": "url", "label": "URL"}, {"key": "totalBytes", "valueType": "bytes", "label": "Transfer Size"}], "items": [{"url": "https://rag-prompt-library.web.app/assets/js/vendor-react-core-DBAjAgoh.js", "totalBytes": 180719}, {"url": "https://rag-prompt-library.firebaseapp.com/__/auth/iframe.js", "totalBytes": 92182}, {"url": "https://rag-prompt-library.web.app/assets/js/components-common-Bz06cWsP.js", "totalBytes": 67648}, {"url": "https://apis.google.com/_/scs/abc-static/_/js/k=gapi.lb.en.iFs_Bhrqdio.O/m=gapi_iframes/rt=j/sv=1/d=1/ed=1/rs=AHpOoo-IvlQWaLHZdUKQFbafXd_3OEvU9Q/cb=gapi.loaded_0?le=scs", "totalBytes": 34384}, {"url": "https://rag-prompt-library.web.app/assets/index-U1UX038B.css", "totalBytes": 10366}, {"url": "https://apis.google.com/js/api.js?onload=__iframefcb151178", "totalBytes": 6341}, {"url": "https://rag-prompt-library.web.app/assets/js/index-CrS6G9IP.js", "totalBytes": 2610}, {"url": "https://rag-prompt-library.web.app/", "totalBytes": 1978}, {"url": "https://rag-prompt-library.web.app/vite.svg", "totalBytes": 1794}, {"url": "https://rag-prompt-library.firebaseapp.com/__/auth/iframe?apiKey=AIzaSyDJWjw2e8FayU3CvIWyGXXFAqDCTFN5CJs&appName=%5BDEFAULT%5D&v=11.10.0&eid=p&usegapi=1&jsh=m%3B%2F_%2Fscs%2Fabc-static%2F_%2Fjs%2Fk%3Dgapi.lb.en.iFs_Bhrqdio.O%2Fd%3D1%2Frs%3DAHpOoo-IvlQWaLHZdUKQFbafXd_3OEvU9Q%2Fm%3D__features__", "totalBytes": 534}], "sortedBy": ["totalBytes"]}, "guidanceLevel": 1}, "offscreen-images": {"id": "offscreen-images", "title": "Defer offscreen images", "description": "Consider lazy-loading offscreen and hidden images after all critical resources have finished loading to lower time to interactive. [Learn how to defer offscreen images](https://developer.chrome.com/docs/lighthouse/performance/offscreen-images/).", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 0, "numericUnit": "millisecond", "displayValue": "", "warnings": [], "metricSavings": {"FCP": 0, "LCP": 0}, "details": {"type": "opportunity", "headings": [], "items": [], "overallSavingsMs": 0, "overallSavingsBytes": 0, "sortedBy": ["wastedBytes"], "debugData": {"type": "debugdata", "metricSavings": {"FCP": 0, "LCP": 0}}}, "guidanceLevel": 2}, "render-blocking-resources": {"id": "render-blocking-resources", "title": "Eliminate render-blocking resources", "description": "Resources are blocking the first paint of your page. Consider delivering critical JS/CSS inline and deferring all non-critical JS/styles. [Learn how to eliminate render-blocking resources](https://developer.chrome.com/docs/lighthouse/performance/render-blocking-resources/).", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 0, "numericUnit": "millisecond", "metricSavings": {"FCP": 0, "LCP": 0}, "details": {"type": "opportunity", "headings": [], "items": [], "overallSavingsMs": 0}, "guidanceLevel": 2}, "unminified-css": {"id": "unminified-css", "title": "Minify CSS", "description": "Minifying CSS files can reduce network payload sizes. [Learn how to minify CSS](https://developer.chrome.com/docs/lighthouse/performance/unminified-css/).", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 0, "numericUnit": "millisecond", "displayValue": "", "metricSavings": {"FCP": 0, "LCP": 0}, "details": {"type": "opportunity", "headings": [], "items": [], "overallSavingsMs": 0, "overallSavingsBytes": 0, "sortedBy": ["wastedBytes"], "debugData": {"type": "debugdata", "metricSavings": {"FCP": 0, "LCP": 0}}}, "guidanceLevel": 3}, "unminified-javascript": {"id": "unminified-javascript", "title": "Minify JavaScript", "description": "Minifying JavaScript files can reduce payload sizes and script parse time. [Learn how to minify JavaScript](https://developer.chrome.com/docs/lighthouse/performance/unminified-javascript/).", "score": 0, "scoreDisplayMode": "metricSavings", "numericValue": 300, "numericUnit": "millisecond", "displayValue": "Est savings of 39 KiB", "warnings": [], "metricSavings": {"FCP": 0, "LCP": 300}, "details": {"type": "opportunity", "headings": [{"key": "url", "valueType": "url", "label": "URL"}, {"key": "totalBytes", "valueType": "bytes", "label": "Transfer Size"}, {"key": "wastedBytes", "valueType": "bytes", "label": "Est Savings"}], "items": [{"url": "https://rag-prompt-library.web.app/assets/js/vendor-react-core-DBAjAgoh.js", "totalBytes": 179586, "wastedBytes": 39487, "wastedPercent": 21.987527970708765}], "overallSavingsMs": 300, "overallSavingsBytes": 39487, "sortedBy": ["wastedBytes"], "debugData": {"type": "debugdata", "metricSavings": {"FCP": 0, "LCP": 300}}}, "guidanceLevel": 3}, "unused-css-rules": {"id": "unused-css-rules", "title": "Reduce unused CSS", "description": "Reduce unused rules from stylesheets and defer CSS not used for above-the-fold content to decrease bytes consumed by network activity. [Learn how to reduce unused CSS](https://developer.chrome.com/docs/lighthouse/performance/unused-css-rules/).", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 0, "numericUnit": "millisecond", "displayValue": "", "metricSavings": {"FCP": 0, "LCP": 0}, "details": {"type": "opportunity", "headings": [], "items": [], "overallSavingsMs": 0, "overallSavingsBytes": 0, "sortedBy": ["wastedBytes"], "debugData": {"type": "debugdata", "metricSavings": {"FCP": 0, "LCP": 0}}}, "guidanceLevel": 1}, "unused-javascript": {"id": "unused-javascript", "title": "Reduce unused JavaScript", "description": "Reduce unused JavaScript and defer loading scripts until they are required to decrease bytes consumed by network activity. [Learn how to reduce unused JavaScript](https://developer.chrome.com/docs/lighthouse/performance/unused-javascript/).", "score": 0, "scoreDisplayMode": "metricSavings", "numericValue": 900, "numericUnit": "millisecond", "displayValue": "Est savings of 151 KiB", "metricSavings": {"FCP": 0, "LCP": 900}, "details": {"type": "opportunity", "headings": [{"key": "url", "valueType": "url", "subItemsHeading": {"key": "source", "valueType": "code"}, "label": "URL"}, {"key": "totalBytes", "valueType": "bytes", "subItemsHeading": {"key": "sourceBytes"}, "label": "Transfer Size"}, {"key": "wastedBytes", "valueType": "bytes", "subItemsHeading": {"key": "sourceWastedBytes"}, "label": "Est Savings"}], "items": [{"url": "https://rag-prompt-library.web.app/assets/js/vendor-react-core-DBAjAgoh.js", "totalBytes": 179585, "wastedBytes": 99250, "wastedPercent": 55.26608656860209}, {"url": "https://rag-prompt-library.web.app/assets/js/components-common-Bz06cWsP.js", "totalBytes": 66498, "wastedBytes": 55797, "wastedPercent": 83.90744375206675}], "overallSavingsMs": 900, "overallSavingsBytes": 155047, "sortedBy": ["wastedBytes"], "debugData": {"type": "debugdata", "metricSavings": {"FCP": 0, "LCP": 900}}}, "guidanceLevel": 1}, "modern-image-formats": {"id": "modern-image-formats", "title": "Serve images in next-gen formats", "description": "Image formats like WebP and AVIF often provide better compression than PNG or JPEG, which means faster downloads and less data consumption. [Learn more about modern image formats](https://developer.chrome.com/docs/lighthouse/performance/uses-webp-images/).", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 0, "numericUnit": "millisecond", "displayValue": "", "warnings": [], "metricSavings": {"FCP": 0, "LCP": 0}, "details": {"type": "opportunity", "headings": [], "items": [], "overallSavingsMs": 0, "overallSavingsBytes": 0, "sortedBy": ["wastedBytes"], "debugData": {"type": "debugdata", "metricSavings": {"FCP": 0, "LCP": 0}}}, "guidanceLevel": 3}, "uses-optimized-images": {"id": "uses-optimized-images", "title": "Efficiently encode images", "description": "Optimized images load faster and consume less cellular data. [Learn how to efficiently encode images](https://developer.chrome.com/docs/lighthouse/performance/uses-optimized-images/).", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 0, "numericUnit": "millisecond", "displayValue": "", "warnings": [], "metricSavings": {"FCP": 0, "LCP": 0}, "details": {"type": "opportunity", "headings": [], "items": [], "overallSavingsMs": 0, "overallSavingsBytes": 0, "sortedBy": ["wastedBytes"], "debugData": {"type": "debugdata", "metricSavings": {"FCP": 0, "LCP": 0}}}, "guidanceLevel": 2}, "uses-text-compression": {"id": "uses-text-compression", "title": "Enable text compression", "description": "Text-based resources should be served with compression (gzip, deflate or brotli) to minimize total network bytes. [Learn more about text compression](https://developer.chrome.com/docs/lighthouse/performance/uses-text-compression/).", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 0, "numericUnit": "millisecond", "displayValue": "", "metricSavings": {"FCP": 0, "LCP": 0}, "details": {"type": "opportunity", "headings": [], "items": [], "overallSavingsMs": 0, "overallSavingsBytes": 0, "sortedBy": ["wastedBytes"], "debugData": {"type": "debugdata", "metricSavings": {"FCP": 0, "LCP": 0}}}, "guidanceLevel": 3}, "uses-responsive-images": {"id": "uses-responsive-images", "title": "Properly size images", "description": "Serve images that are appropriately-sized to save cellular data and improve load time. [Learn how to size images](https://developer.chrome.com/docs/lighthouse/performance/uses-responsive-images/).", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 0, "numericUnit": "millisecond", "displayValue": "", "metricSavings": {"FCP": 0, "LCP": 0}, "details": {"type": "opportunity", "headings": [], "items": [], "overallSavingsMs": 0, "overallSavingsBytes": 0, "sortedBy": ["wastedBytes"], "debugData": {"type": "debugdata", "metricSavings": {"FCP": 0, "LCP": 0}}}, "guidanceLevel": 2}, "efficient-animated-content": {"id": "efficient-animated-content", "title": "Use video formats for animated content", "description": "Large GIFs are inefficient for delivering animated content. Consider using MPEG4/WebM videos for animations and PNG/WebP for static images instead of GIF to save network bytes. [Learn more about efficient video formats](https://developer.chrome.com/docs/lighthouse/performance/efficient-animated-content/)", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 0, "numericUnit": "millisecond", "displayValue": "", "metricSavings": {"FCP": 0, "LCP": 0}, "details": {"type": "opportunity", "headings": [], "items": [], "overallSavingsMs": 0, "overallSavingsBytes": 0, "sortedBy": ["wastedBytes"], "debugData": {"type": "debugdata", "metricSavings": {"FCP": 0, "LCP": 0}}}, "guidanceLevel": 3}, "duplicated-javascript": {"id": "duplicated-javascript", "title": "Remove duplicate modules in JavaScript bundles", "description": "Remove large, duplicate JavaScript modules from bundles to reduce unnecessary bytes consumed by network activity. ", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 0, "numericUnit": "millisecond", "displayValue": "", "metricSavings": {"FCP": 0, "LCP": 0}, "details": {"type": "opportunity", "headings": [], "items": [], "overallSavingsMs": 0, "overallSavingsBytes": 0, "sortedBy": ["wastedBytes"], "debugData": {"type": "debugdata", "metricSavings": {"FCP": 0, "LCP": 0}}}, "guidanceLevel": 2}, "legacy-javascript": {"id": "legacy-javascript", "title": "Avoid serving legacy JavaScript to modern browsers", "description": "Polyfills and transforms enable legacy browsers to use new JavaScript features. However, many aren't necessary for modern browsers. Consider modifying your JavaScript build process to not transpile [Baseline](https://web.dev/baseline) features, unless you know you must support legacy browsers. [Learn why most sites can deploy ES6+ code without transpiling](https://philipwalton.com/articles/the-state-of-es5-on-the-web/)", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 0, "numericUnit": "millisecond", "displayValue": "", "warnings": [], "metricSavings": {"FCP": 0, "LCP": 0}, "details": {"type": "opportunity", "headings": [], "items": [], "overallSavingsMs": 0, "overallSavingsBytes": 0, "sortedBy": ["wastedBytes"], "debugData": {"type": "debugdata", "metricSavings": {"FCP": 0, "LCP": 0}}}, "guidanceLevel": 2}, "doctype": {"id": "doctype", "title": "<PERSON> has the HTML doctype", "description": "Specifying a doctype prevents the browser from switching to quirks-mode. [Learn more about the doctype declaration](https://developer.chrome.com/docs/lighthouse/best-practices/doctype/).", "score": 1, "scoreDisplayMode": "binary"}, "charset": {"id": "charset", "title": "<PERSON><PERSON><PERSON> defines charset", "description": "A character encoding declaration is required. It can be done with a `<meta>` tag in the first 1024 bytes of the HTML or in the Content-Type HTTP response header. [Learn more about declaring the character encoding](https://developer.chrome.com/docs/lighthouse/best-practices/charset/).", "score": 1, "scoreDisplayMode": "binary"}, "dom-size": {"id": "dom-size", "title": "Avoids an excessive DOM size", "description": "A large DOM will increase memory usage, cause longer [style calculations](https://developers.google.com/web/fundamentals/performance/rendering/reduce-the-scope-and-complexity-of-style-calculations), and produce costly [layout reflows](https://developers.google.com/speed/articles/reflow). [Learn how to avoid an excessive DOM size](https://developer.chrome.com/docs/lighthouse/performance/dom-size/).", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 95, "numericUnit": "element", "displayValue": "95 elements", "metricSavings": {"TBT": 0}, "details": {"type": "table", "headings": [{"key": "statistic", "valueType": "text", "label": "Statistic"}, {"key": "node", "valueType": "node", "label": "Element"}, {"key": "value", "valueType": "numeric", "label": "Value"}], "items": [{"statistic": "Total DOM Elements", "value": {"type": "numeric", "granularity": 1, "value": 95}}, {"node": {"type": "node", "lhId": "1-5-path", "path": "1,HTML,1,BODY,0,DIV,0,DIV,0,DIV,0,DIV,1,DIV,0,DIV,0,DIV,1,FORM,1,DIV,1,DIV,2,BUTTON,0,svg,0,path", "selector": "div.relative > button.absolute > svg.lucide > path", "boundingRect": {"top": 833, "bottom": 845, "left": 342, "right": 358, "width": 17, "height": 12}, "snippet": "<path d=\"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696…\">", "nodeLabel": "div.relative > button.absolute > svg.lucide > path"}, "statistic": "Maximum DOM Depth", "value": {"type": "numeric", "granularity": 1, "value": 14}}, {"node": {"type": "node", "lhId": "1-6-svg", "path": "1,HTML,1,BODY,0,DIV,0,DIV,0,DIV,0,DIV,0,DIV,0,DIV,0,svg", "selector": "div.w-full > div.text-center > div.flex > svg.lucide", "boundingRect": {"top": 16, "bottom": 64, "left": 73, "right": 121, "width": 48, "height": 48}, "snippet": "<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\" class=\"lucide lucide-brain w-12 h-12 text-blue-400 mr-3\" aria-hidden=\"true\">", "nodeLabel": "div.w-full > div.text-center > div.flex > svg.lucide"}, "statistic": "Maximum Child Elements", "value": {"type": "numeric", "granularity": 1, "value": 9}}]}, "guidanceLevel": 1}, "geolocation-on-start": {"id": "geolocation-on-start", "title": "Avoids requesting the geolocation permission on page load", "description": "Users are mistrustful of or confused by sites that request their location without context. Consider tying the request to a user action instead. [Learn more about the geolocation permission](https://developer.chrome.com/docs/lighthouse/best-practices/geolocation-on-start/).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [{"key": "source", "valueType": "source-location", "label": "Source"}], "items": []}}, "inspector-issues": {"id": "inspector-issues", "title": "No issues in the `Issues` panel in Chrome Devtools", "description": "Issues logged to the `Issues` panel in Chrome Devtools indicate unresolved problems. They can come from network request failures, insufficient security controls, and other browser concerns. Open up the Issues panel in Chrome DevTools for more details on each issue.", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [{"key": "issueType", "valueType": "text", "subItemsHeading": {"key": "url", "valueType": "url"}, "label": "Issue type"}], "items": []}}, "no-document-write": {"id": "no-document-write", "title": "Avoids `document.write()`", "description": "For users on slow connections, external scripts dynamically injected via `document.write()` can delay page load by tens of seconds. [Learn how to avoid document.write()](https://developer.chrome.com/docs/lighthouse/best-practices/no-document-write/).", "score": 1, "scoreDisplayMode": "metricSavings", "details": {"type": "table", "headings": [{"key": "source", "valueType": "source-location", "label": "Source"}], "items": []}, "guidanceLevel": 2}, "js-libraries": {"id": "js-libraries", "title": "Detected JavaScript libraries", "description": "All front-end JavaScript libraries detected on the page. [Learn more about this JavaScript library detection diagnostic audit](https://developer.chrome.com/docs/lighthouse/best-practices/js-libraries/).", "score": null, "scoreDisplayMode": "notApplicable"}, "notification-on-start": {"id": "notification-on-start", "title": "Avoids requesting the notification permission on page load", "description": "Users are mistrustful of or confused by sites that request to send notifications without context. Consider tying the request to user gestures instead. [Learn more about responsibly getting permission for notifications](https://developer.chrome.com/docs/lighthouse/best-practices/notification-on-start/).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [{"key": "source", "valueType": "source-location", "label": "Source"}], "items": []}}, "paste-preventing-inputs": {"id": "paste-preventing-inputs", "title": "Allows users to paste into input fields", "description": "Preventing input pasting is a bad practice for the UX, and weakens security by blocking password managers.[Learn more about user-friendly input fields](https://developer.chrome.com/docs/lighthouse/best-practices/paste-preventing-inputs/).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [{"key": "node", "valueType": "node", "label": "Failing Elements"}], "items": []}}, "uses-http2": {"id": "uses-http2", "title": "Use HTTP/2", "description": "HTTP/2 offers many benefits over HTTP/1.1, including binary headers and multiplexing. [Learn more about HTTP/2](https://developer.chrome.com/docs/lighthouse/best-practices/uses-http2/).", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 0, "numericUnit": "millisecond", "metricSavings": {"LCP": 0, "FCP": 0}, "details": {"type": "opportunity", "headings": [], "items": [], "overallSavingsMs": 0}, "guidanceLevel": 3}, "uses-passive-event-listeners": {"id": "uses-passive-event-listeners", "title": "Uses passive listeners to improve scrolling performance", "description": "Consider marking your touch and wheel event listeners as `passive` to improve your page's scroll performance. [Learn more about adopting passive event listeners](https://developer.chrome.com/docs/lighthouse/best-practices/uses-passive-event-listeners/).", "score": 1, "scoreDisplayMode": "metricSavings", "details": {"type": "table", "headings": [{"key": "source", "valueType": "source-location", "label": "Source"}], "items": []}, "guidanceLevel": 3}, "meta-description": {"id": "meta-description", "title": "Document has a meta description", "description": "Meta descriptions may be included in search results to concisely summarize page content. [Learn more about the meta description](https://developer.chrome.com/docs/lighthouse/seo/meta-description/).", "score": 1, "scoreDisplayMode": "binary"}, "http-status-code": {"id": "http-status-code", "title": "Page has successful HTTP status code", "description": "Pages with unsuccessful HTTP status codes may not be indexed properly. [Learn more about HTTP status codes](https://developer.chrome.com/docs/lighthouse/seo/http-status-code/).", "score": 1, "scoreDisplayMode": "binary"}, "font-size": {"id": "font-size", "title": "Document uses legible font sizes", "description": "Font sizes less than 12px are too small to be legible and require mobile visitors to “pinch to zoom” in order to read. Strive to have >60% of page text ≥12px. [Learn more about legible font sizes](https://developer.chrome.com/docs/lighthouse/seo/font-size/).", "score": 1, "scoreDisplayMode": "binary", "displayValue": "100% legible text", "details": {"type": "table", "headings": [{"key": "source", "valueType": "source-location", "label": "Source"}, {"key": "selector", "valueType": "code", "label": "Selector"}, {"key": "coverage", "valueType": "text", "label": "% of Page Text"}, {"key": "fontSize", "valueType": "text", "label": "Font Size"}], "items": [{"source": {"type": "code", "value": "Legible text"}, "selector": "", "coverage": "100.00%", "fontSize": "≥ 12px"}]}}, "link-text": {"id": "link-text", "title": "Links have descriptive text", "description": "Descriptive link text helps search engines understand your content. [Learn how to make links more accessible](https://developer.chrome.com/docs/lighthouse/seo/link-text/).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [{"key": "href", "valueType": "url", "label": "Link destination"}, {"key": "text", "valueType": "text", "label": "Link Text"}], "items": []}}, "crawlable-anchors": {"id": "crawlable-anchors", "title": "Links are crawlable", "description": "Search engines may use `href` attributes on links to crawl websites. Ensure that the `href` attribute of anchor elements links to an appropriate destination, so more pages of the site can be discovered. [Learn how to make links crawlable](https://support.google.com/webmasters/answer/9112205)", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [{"key": "node", "valueType": "node", "label": "Uncrawlable Link"}], "items": []}}, "is-crawlable": {"id": "is-crawlable", "title": "Page isn’t blocked from indexing", "description": "Search engines are unable to include your pages in search results if they don't have permission to crawl them. [Learn more about crawler directives](https://developer.chrome.com/docs/lighthouse/seo/is-crawlable/).", "score": 1, "scoreDisplayMode": "binary", "warnings": [], "details": {"type": "table", "headings": [{"key": "source", "valueType": "code", "label": "Blocking Directive Source"}], "items": []}}, "robots-txt": {"id": "robots-txt", "title": "robots.txt is not valid", "description": "If your robots.txt file is malformed, crawlers may not be able to understand how you want your website to be crawled or indexed. [Learn more about robots.txt](https://developer.chrome.com/docs/lighthouse/seo/invalid-robots-txt/).", "score": 0, "scoreDisplayMode": "binary", "displayValue": "47 errors found", "details": {"type": "table", "headings": [{"key": "index", "valueType": "text", "label": "Line #"}, {"key": "line", "valueType": "code", "label": "Content"}, {"key": "message", "valueType": "code", "label": "Error"}], "items": [{"index": "1", "line": "<!doctype html>", "message": "Syntax not understood"}, {"index": "2", "line": "<html lang=\"en\">", "message": "Syntax not understood"}, {"index": "3", "line": "  <head>", "message": "Syntax not understood"}, {"index": "4", "line": "    <meta charset=\"UTF-8\" />", "message": "Syntax not understood"}, {"index": "5", "line": "    <link rel=\"icon\" type=\"image/svg+xml\" href=\"/vite.svg\" />", "message": "Syntax not understood"}, {"index": "6", "line": "    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\" />", "message": "Syntax not understood"}, {"index": "7", "line": "    <meta name=\"description\" content=\"RAG Prompt Library - Smart, modular, RAG-enabled prompt management system for AI workflows\" />", "message": "Syntax not understood"}, {"index": "8", "line": "    <meta name=\"keywords\" content=\"AI, prompts, RAG, retrieval augmented generation, prompt library, AI tools\" />", "message": "Syntax not understood"}, {"index": "9", "line": "    <meta name=\"author\" content=\"RAG Prompt Library\" />", "message": "Syntax not understood"}, {"index": "11", "line": "    <!-- Security Headers (Note: CSP and X-Frame-Options are set via HTTP headers in Firebase hosting) -->", "message": "Unknown directive"}, {"index": "12", "line": "    <meta http-equiv=\"X-Content-Type-Options\" content=\"nosniff\" />", "message": "Syntax not understood"}, {"index": "13", "line": "    <meta http-equiv=\"Referrer-Policy\" content=\"strict-origin-when-cross-origin\" />", "message": "Syntax not understood"}, {"index": "14", "line": "    <meta http-equiv=\"Permissions-Policy\" content=\"camera=(), microphone=(), geolocation=()\" />", "message": "Syntax not understood"}, {"index": "16", "line": "    <!-- Performance optimizations -->", "message": "Syntax not understood"}, {"index": "17", "line": "    <link rel=\"dns-prefetch\" href=\"//fonts.googleapis.com\" />", "message": "Syntax not understood"}, {"index": "18", "line": "    <link rel=\"dns-prefetch\" href=\"//firebaseapp.com\" />", "message": "Syntax not understood"}, {"index": "19", "line": "    <link rel=\"dns-prefetch\" href=\"//openrouter.ai\" />", "message": "Syntax not understood"}, {"index": "21", "line": "    <!-- Preload critical resources will be handled by Vite build -->", "message": "Syntax not understood"}, {"index": "23", "line": "    <!-- Theme and styling -->", "message": "Syntax not understood"}, {"index": "24", "line": "    <meta name=\"theme-color\" content=\"#3b82f6\" />", "message": "Syntax not understood"}, {"index": "25", "line": "    <meta name=\"color-scheme\" content=\"light dark\" />", "message": "Syntax not understood"}, {"index": "27", "line": "    <title>RAG Prompt Library - Smart Prompt Management</title>", "message": "Syntax not understood"}, {"index": "28", "line": "    <script type=\"module\" crossorigin src=\"/assets/js/index-CrS6G9IP.js\"></script>", "message": "Syntax not understood"}, {"index": "29", "line": "    <link rel=\"stylesheet\" crossorigin href=\"/assets/index-U1UX038B.css\">", "message": "Syntax not understood"}, {"index": "30", "line": "  </head>", "message": "Syntax not understood"}, {"index": "31", "line": "  <body>", "message": "Syntax not understood"}, {"index": "32", "line": "    <div id=\"root\">", "message": "Syntax not understood"}, {"index": "33", "line": "      <!-- Loading fallback -->", "message": "Syntax not understood"}, {"index": "34", "line": "      <div style=\"display: flex; align-items: center; justify-content: center; height: 100vh; font-family: system-ui, sans-serif;\">", "message": "Unknown directive"}, {"index": "35", "line": "        <div style=\"text-align: center;\">", "message": "Unknown directive"}, {"index": "36", "line": "          <div style=\"width: 40px; height: 40px; border: 4px solid #f3f4f6; border-top: 4px solid #3b82f6; border-radius: 50%; animation: spin 1s linear infinite; margin: 0 auto 16px;\"></div>", "message": "Unknown directive"}, {"index": "37", "line": "          <p style=\"color: #6b7280; margin: 0;\">Loading RAG Prompt Library...</p>", "message": "Unknown directive"}, {"index": "38", "line": "        </div>", "message": "Syntax not understood"}, {"index": "39", "line": "      </div>", "message": "Syntax not understood"}, {"index": "40", "line": "      <style>", "message": "Syntax not understood"}, {"index": "41", "line": "        @-webkit-keyframes spin {", "message": "Syntax not understood"}, {"index": "42", "line": "          0% { -webkit-transform: rotate(0deg); transform: rotate(0deg); }", "message": "Unknown directive"}, {"index": "43", "line": "          100% { -webkit-transform: rotate(360deg); transform: rotate(360deg); }", "message": "Unknown directive"}, {"index": "44", "line": "        }", "message": "Syntax not understood"}, {"index": "45", "line": "@keyframes spin {", "message": "Syntax not understood"}, {"index": "46", "line": "          0% { -webkit-transform: rotate(0deg); transform: rotate(0deg); }", "message": "Unknown directive"}, {"index": "47", "line": "          100% { -webkit-transform: rotate(360deg); transform: rotate(360deg); }", "message": "Unknown directive"}, {"index": "48", "line": "        }", "message": "Syntax not understood"}, {"index": "49", "line": "      </style>", "message": "Syntax not understood"}, {"index": "50", "line": "    </div>", "message": "Syntax not understood"}, {"index": "51", "line": "  </body>", "message": "Syntax not understood"}, {"index": "52", "line": "</html>", "message": "Syntax not understood"}]}}, "hreflang": {"id": "hreflang", "title": "Document has a valid `hreflang`", "description": "hreflang links tell search engines what version of a page they should list in search results for a given language or region. [Learn more about `hreflang`](https://developer.chrome.com/docs/lighthouse/seo/hreflang/).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [{"key": "source", "valueType": "code", "subItemsHeading": {"key": "reason", "valueType": "text"}, "label": ""}], "items": []}}, "canonical": {"id": "canonical", "title": "Document has a valid `rel=canonical`", "description": "Canonical links suggest which URL to show in search results. [Learn more about canonical links](https://developer.chrome.com/docs/lighthouse/seo/canonical/).", "score": null, "scoreDisplayMode": "notApplicable"}, "structured-data": {"id": "structured-data", "title": "Structured data is valid", "description": "Run the [Structured Data Testing Tool](https://search.google.com/structured-data/testing-tool/) and the [Structured Data Linter](http://linter.structured-data.org/) to validate structured data. [Learn more about Structured Data](https://developer.chrome.com/docs/lighthouse/seo/structured-data/).", "score": null, "scoreDisplayMode": "manual"}, "bf-cache": {"id": "bf-cache", "title": "Page didn't prevent back/forward cache restoration", "description": "Many navigations are performed by going back to a previous page, or forwards again. The back/forward cache (bfcache) can speed up these return navigations. [Learn more about the bfcache](https://developer.chrome.com/docs/lighthouse/performance/bf-cache/)", "score": 1, "scoreDisplayMode": "binary", "guidanceLevel": 4}, "cache-insight": {"id": "cache-insight", "title": "Use efficient cache lifetimes", "description": "A long cache lifetime can speed up repeat visits to your page. [Learn more](https://web.dev/uses-long-cache-ttl/).", "score": 0.5, "scoreDisplayMode": "metricSavings", "displayValue": "Est savings of 78 KiB", "metricSavings": {"FCP": 0, "LCP": 0}, "details": {"type": "table", "headings": [{"key": "url", "valueType": "url", "label": "Request"}, {"key": "cacheLifetimeMs", "valueType": "ms", "label": "<PERSON><PERSON>", "displayUnit": "duration"}, {"key": "totalBytes", "valueType": "bytes", "label": "Transfer Size", "displayUnit": "kb", "granularity": 1}], "items": [{"url": "https://rag-prompt-library.firebaseapp.com/__/auth/iframe.js", "cacheLifetimeMs": 1800000, "totalBytes": 92182, "wastedBytes": 79506.975}], "sortedBy": ["wastedBytes"], "skipSumming": ["cacheLifetimeMs"], "debugData": {"type": "debugdata", "wastedBytes": 79506.975}}, "guidanceLevel": 3, "replacesAudits": ["uses-long-cache-ttl"]}, "cls-culprits-insight": {"id": "cls-culprits-insight", "title": "Layout shift culprits", "description": "Layout shifts occur when elements move absent any user interaction. [Investigate the causes of layout shifts](https://web.dev/articles/optimize-cls), such as elements being added, removed, or their fonts changing as the page loads.", "score": 1, "scoreDisplayMode": "numeric", "metricSavings": {"CLS": 0}, "details": {"type": "list", "items": []}, "guidanceLevel": 3, "replacesAudits": ["layout-shifts", "non-composited-animations", "unsized-images"]}, "document-latency-insight": {"id": "document-latency-insight", "title": "Document request latency", "description": "Your first network request is the most important.  Reduce its latency by avoiding redirects, ensuring a fast server response, and enabling text compression.", "score": 1, "scoreDisplayMode": "metricSavings", "metricSavings": {"FCP": 0, "LCP": 0}, "details": {"type": "checklist", "items": {"noRedirects": {"label": "Avoids redirects", "value": true}, "serverResponseIsFast": {"label": "Server responds quickly (observed 172 ms)", "value": true}, "usesCompression": {"label": "Applies text compression", "value": true}}, "debugData": {"type": "debugdata", "redirectDuration": 0, "serverResponseTime": 172, "uncompressedResponseBytes": 0, "wastedBytes": 0}}, "guidanceLevel": 3, "replacesAudits": ["redirects", "server-response-time", "uses-text-compression"]}, "dom-size-insight": {"id": "dom-size-insight", "title": "Optimize DOM size", "description": "A large DOM can increase the duration of style calculations and layout reflows, impacting page responsiveness. A large DOM will also increase memory usage. [Learn how to avoid an excessive DOM size](https://developer.chrome.com/docs/lighthouse/performance/dom-size/).", "score": 1, "scoreDisplayMode": "numeric", "metricSavings": {"INP": 0}, "details": {"type": "table", "headings": [{"key": "statistic", "valueType": "text", "label": "Statistic"}, {"key": "node", "valueType": "node", "label": "Element"}, {"key": "value", "valueType": "numeric", "label": "Value"}], "items": [{"statistic": "Total elements", "value": {"type": "numeric", "granularity": 1, "value": 97}}, {"statistic": "Most children", "node": {"type": "node", "lhId": "page-2-svg", "path": "1,HTML,1,BODY,0,DIV,0,DIV,0,DIV,0,DIV,0,DIV,0,DIV,0,svg", "selector": "div.w-full > div.text-center > div.flex > svg.lucide", "boundingRect": {"top": 16, "bottom": 64, "left": 73, "right": 121, "width": 48, "height": 48}, "snippet": "<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\" class=\"lucide lucide-brain w-12 h-12 text-blue-400 mr-3\" aria-hidden=\"true\">", "nodeLabel": "div.w-full > div.text-center > div.flex > svg.lucide"}, "value": {"type": "numeric", "granularity": 1, "value": 9}}, {"statistic": "DOM depth", "node": {"type": "node", "lhId": "page-3-path", "path": "1,HTML,1,BODY,0,DIV,0,DIV,0,DIV,0,DIV,1,DIV,0,DIV,0,DIV,1,FORM,1,DIV,1,DIV,2,BUTTON,0,svg,0,path", "selector": "div.relative > button.absolute > svg.lucide > path", "boundingRect": {"top": 833, "bottom": 845, "left": 342, "right": 358, "width": 17, "height": 12}, "snippet": "<path d=\"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696…\">", "nodeLabel": "div.relative > button.absolute > svg.lucide > path"}, "value": {"type": "numeric", "granularity": 1, "value": 14}}], "debugData": {"type": "debugdata", "totalElements": 97, "maxChildren": 9, "maxDepth": 14}}, "guidanceLevel": 3, "replacesAudits": ["dom-size"]}, "duplicated-javascript-insight": {"id": "duplicated-javascript-insight", "title": "Duplicated JavaScript", "description": "Remove large, duplicate JavaScript modules from bundles to reduce unnecessary bytes consumed by network activity.", "score": null, "scoreDisplayMode": "notApplicable", "details": {"type": "table", "headings": [{"key": "source", "valueType": "code", "subItemsHeading": {"key": "url", "valueType": "url"}, "label": "Source"}, {"key": "wastedBytes", "valueType": "bytes", "subItemsHeading": {"key": "sourceTransferBytes"}, "granularity": 10, "label": "Duplicated bytes"}], "items": []}, "guidanceLevel": 2, "replacesAudits": ["duplicated-javascript"]}, "font-display-insight": {"id": "font-display-insight", "title": "Font display", "description": "Consider setting [font-display](https://developer.chrome.com/blog/font-display) to swap or optional to ensure text is consistently visible. swap can be further optimized to mitigate layout shifts with [font metric overrides](https://developer.chrome.com/blog/font-fallbacks).", "score": null, "scoreDisplayMode": "notApplicable", "details": {"type": "table", "headings": [{"key": "url", "valueType": "url", "label": "URL"}, {"key": "wastedMs", "valueType": "ms", "label": "Est Savings"}], "items": []}, "guidanceLevel": 3, "replacesAudits": ["font-display"]}, "forced-reflow-insight": {"id": "forced-reflow-insight", "title": "Forced reflow", "description": "A forced reflow occurs when JavaScript queries geometric properties (such as offsetWidth) after styles have been invalidated by a change to the DOM state. This can result in poor performance. Learn more about [forced reflows](https://developers.google.com/web/fundamentals/performance/rendering/avoid-large-complex-layouts-and-layout-thrashing#avoid-forced-synchronous-layouts) and possible mitigations.", "score": 1, "scoreDisplayMode": "numeric", "details": {"type": "list", "items": [{"type": "table", "headings": [{"key": "source", "valueType": "source-location", "label": "Source"}, {"key": "reflowTime", "valueType": "ms", "granularity": 1, "label": "Total reflow time"}], "items": []}]}, "guidanceLevel": 3}, "image-delivery-insight": {"id": "image-delivery-insight", "title": "Improve image delivery", "description": "Reducing the download time of images can improve the perceived load time of the page and LCP. [Learn more about optimizing image size](https://developer.chrome.com/docs/lighthouse/performance/uses-optimized-images/)", "score": null, "scoreDisplayMode": "notApplicable", "guidanceLevel": 3, "replacesAudits": ["modern-image-formats", "uses-optimized-images", "efficient-animated-content", "uses-responsive-images"]}, "inp-breakdown-insight": {"id": "inp-breakdown-insight", "title": "INP breakdown", "description": "Start investigating with the longest subpart. [Delays can be minimized](https://web.dev/articles/optimize-inp#optimize_interactions). To reduce processing duration, [optimize the main-thread costs](https://web.dev/articles/optimize-long-tasks), often JS.", "score": null, "scoreDisplayMode": "notApplicable", "guidanceLevel": 3, "replacesAudits": ["work-during-interaction"]}, "lcp-breakdown-insight": {"id": "lcp-breakdown-insight", "title": "LCP breakdown", "description": "Each [subpart has specific improvement strategies](https://web.dev/articles/optimize-lcp#lcp-breakdown). Ideally, most of the LCP time should be spent on loading the resources, not within delays.", "score": 1, "scoreDisplayMode": "informative", "metricSavings": {"LCP": 0}, "details": {"type": "list", "items": [{"type": "table", "headings": [{"key": "label", "valueType": "text", "label": "Subpart"}, {"key": "duration", "valueType": "ms", "label": "Duration"}], "items": [{"subpart": "timeToFirstByte", "label": "Time to first byte", "duration": 534.03}, {"subpart": "elementRenderDelay", "label": "Element render delay", "duration": 2565.016}]}, {"type": "node", "lhId": "page-0-P", "path": "1,HTML,1,BODY,0,DIV,0,DIV,0,DIV,0,DIV,0,DIV,2,P", "selector": "div.min-h-screen > div.w-full > div.text-center > p.text-xl", "boundingRect": {"top": 232, "bottom": 344, "left": 16, "right": 396, "width": 380, "height": 112}, "snippet": "<p class=\"text-xl text-gray-200 mb-8\">", "nodeLabel": "Create, manage, and execute AI prompts with powerful retrieval-augmented genera…"}]}, "guidanceLevel": 3, "replacesAudits": ["largest-contentful-paint-element"]}, "lcp-discovery-insight": {"id": "lcp-discovery-insight", "title": "LCP request discovery", "description": "Optimize LCP by making the LCP image [discoverable](https://web.dev/articles/optimize-lcp#1_eliminate_resource_load_delay) from the HTML immediately, and [avoiding lazy-loading](https://web.dev/articles/lcp-lazy-loading)", "score": null, "scoreDisplayMode": "notApplicable", "guidanceLevel": 3, "replacesAudits": ["prioritize-lcp-image", "lcp-lazy-loaded"]}, "legacy-javascript-insight": {"id": "legacy-javascript-insight", "title": "Legacy JavaScript", "description": "Polyfills and transforms enable older browsers to use new JavaScript features. However, many aren't necessary for modern browsers. Consider modifying your JavaScript build process to not transpile [Baseline](https://web.dev/articles/baseline-and-polyfills) features, unless you know you must support older browsers. [Learn why most sites can deploy ES6+ code without transpiling](https://philipwalton.com/articles/the-state-of-es5-on-the-web/)", "score": null, "scoreDisplayMode": "notApplicable", "details": {"type": "table", "headings": [{"key": "url", "valueType": "url", "subItemsHeading": {"key": "location", "valueType": "source-location"}, "label": "URL"}, {"key": null, "valueType": "code", "subItemsHeading": {"key": "signal"}, "label": ""}, {"key": "wastedBytes", "valueType": "bytes", "label": "Wasted bytes"}], "items": []}, "guidanceLevel": 2}, "modern-http-insight": {"id": "modern-http-insight", "title": "Modern HTTP", "description": "HTTP/2 and HTTP/3 offer many benefits over HTTP/1.1, such as multiplexing. [Learn more about using modern HTTP](https://developer.chrome.com/docs/lighthouse/best-practices/uses-http2/).", "score": null, "scoreDisplayMode": "notApplicable", "details": {"type": "table", "headings": [{"key": "url", "valueType": "url", "label": "URL"}, {"key": "protocol", "valueType": "text", "label": "Protocol"}], "items": []}, "guidanceLevel": 3}, "network-dependency-tree-insight": {"id": "network-dependency-tree-insight", "title": "Network dependency tree", "description": "[Avoid chaining critical requests](https://developer.chrome.com/docs/lighthouse/performance/critical-request-chains) by reducing the length of chains, reducing the download size of resources, or deferring the download of unnecessary resources to improve page load.", "score": 0, "scoreDisplayMode": "numeric", "metricSavings": {"LCP": 0}, "details": {"type": "list", "items": [{"type": "list-section", "value": {"type": "network-tree", "chains": {"6C69CF588F9957C8EAAACC3AA07E5C6C": {"url": "https://rag-prompt-library.web.app/", "navStartToEndTime": 571, "transferSize": 1978, "isLongest": true, "children": {"6000.2": {"url": "https://rag-prompt-library.firebaseapp.com/__/auth/iframe.js", "navStartToEndTime": 5170, "transferSize": 92182, "isLongest": true, "children": {"6000.4": {"url": "https://www.googleapis.com/identitytoolkit/v3/relyingparty/getProjectConfig?key=AIzaSyDJWjw2e8FayU3CvIWyGXXFAqDCTFN5CJs&cb=1753730490214", "navStartToEndTime": 5215, "transferSize": 0, "isLongest": true, "children": {}}}}, "6556.2": {"url": "https://rag-prompt-library.web.app/assets/js/index-CrS6G9IP.js", "navStartToEndTime": 961, "transferSize": 2610, "children": {"6556.7": {"url": "https://rag-prompt-library.web.app/assets/js/components-common-Bz06cWsP.js", "navStartToEndTime": 2567, "transferSize": 67648, "children": {}}, "6556.6": {"url": "https://rag-prompt-library.web.app/assets/js/vendor-react-core-DBAjAgoh.js", "navStartToEndTime": 2335, "transferSize": 180719, "children": {}}}}, "6556.3": {"url": "https://rag-prompt-library.web.app/assets/index-U1UX038B.css", "navStartToEndTime": 861, "transferSize": 10366, "children": {}}}}}, "longestChain": {"duration": 5215}}}, {"type": "list-section", "title": "Preconnected origins", "description": "[preconnect](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/) hints help the browser establish a connection earlier in the page load, saving time when the first request for that origin is made. The following are the origins that the page preconnected to.", "value": {"type": "text", "value": "no origins were preconnected"}}, {"type": "list-section", "title": "Preconnect candidates", "description": "Add [preconnect](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/) hints to your most important origins, but try to use no more than 4.", "value": {"type": "table", "headings": [{"key": "origin", "valueType": "text", "label": "Origin"}, {"key": "wastedMs", "valueType": "ms", "label": "Est LCP savings"}], "items": [{"origin": "https://apis.google.com", "wastedMs": 383.576}]}}]}, "guidanceLevel": 1, "replacesAudits": ["critical-request-chains", "uses-rel-preconnect"]}, "render-blocking-insight": {"id": "render-blocking-insight", "title": "Render blocking requests", "description": "Requests are blocking the page's initial render, which may delay LCP. [Deferring or inlining](https://web.dev/learn/performance/understanding-the-critical-path#render-blocking_resources) can move these network requests out of the critical path.", "score": 0.5, "scoreDisplayMode": "metricSavings", "metricSavings": {"FCP": 0, "LCP": 0}, "details": {"type": "table", "headings": [{"key": "url", "valueType": "url", "label": "URL"}, {"key": "totalBytes", "valueType": "bytes", "label": "Transfer Size"}, {"key": "wastedMs", "valueType": "timespanMs", "label": "Duration"}], "items": [{"url": "https://rag-prompt-library.web.app/assets/index-U1UX038B.css", "totalBytes": 10366}]}, "guidanceLevel": 3, "replacesAudits": ["render-blocking-resources"]}, "third-parties-insight": {"id": "third-parties-insight", "title": "3rd parties", "description": "3rd party code can significantly impact load performance. [Reduce and defer loading of 3rd party code](https://web.dev/articles/optimizing-content-efficiency-loading-third-party-javascript/) to prioritize your page's content.", "score": 1, "scoreDisplayMode": "informative", "details": {"type": "table", "headings": [{"key": "entity", "valueType": "text", "label": "3rd party", "subItemsHeading": {"key": "url", "valueType": "url"}}, {"key": "transferSize", "granularity": 1, "valueType": "bytes", "label": "Transfer size", "subItemsHeading": {"key": "transferSize"}}, {"key": "mainThreadTime", "granularity": 1, "valueType": "ms", "label": "Main thread time", "subItemsHeading": {"key": "mainThreadTime"}}], "items": [{"entity": "Other Google APIs/SDKs", "mainThreadTime": 13.530000000144355, "transferSize": 40725, "subItems": {"type": "subitems", "items": [{"url": "https://apis.google.com/_/scs/abc-static/_/js/k=gapi.lb.en.iFs_Bhrqdio.O/m=gapi_iframes/rt=j/sv=1/d=1/ed=1/rs=AHpOoo-IvlQWaLHZdUKQFbafXd_3OEvU9Q/cb=gapi.loaded_0?le=scs", "mainThreadTime": 10.898000000161119, "transferSize": 34384}, {"url": "https://apis.google.com/js/api.js?onload=__iframefcb151178", "mainThreadTime": 2.631999999983236, "transferSize": 6341}]}}], "isEntityGrouped": true}, "guidanceLevel": 3, "replacesAudits": ["third-party-summary"]}, "viewport-insight": {"id": "viewport-insight", "title": "Optimize viewport for mobile", "description": "Tap interactions may be [delayed by up to 300 ms](https://developer.chrome.com/blog/300ms-tap-delay-gone-away/) if the viewport is not optimized for mobile.", "score": 1, "scoreDisplayMode": "numeric", "metricSavings": {"INP": 0}, "details": {"type": "table", "headings": [{"key": "node", "valueType": "node", "label": ""}], "items": [{"node": {"type": "node", "lhId": "page-1-META", "path": "1,<PERSON><PERSON><PERSON>,0,<PERSON><PERSON><PERSON>,2,<PERSON><PERSON>", "selector": "head > meta", "boundingRect": {"top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "snippet": "<meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">", "nodeLabel": "head > meta"}}]}, "guidanceLevel": 3, "replacesAudits": ["viewport"]}}, "configSettings": {"output": ["json"], "maxWaitForFcp": 30000, "maxWaitForLoad": 45000, "pauseAfterFcpMs": 1000, "pauseAfterLoadMs": 1000, "networkQuietThresholdMs": 1000, "cpuQuietThresholdMs": 1000, "formFactor": "mobile", "throttling": {"rttMs": 150, "throughputKbps": 1638.4, "requestLatencyMs": 562.5, "downloadThroughputKbps": 1474.5600000000002, "uploadThroughputKbps": 675, "cpuSlowdownMultiplier": 4}, "throttlingMethod": "simulate", "screenEmulation": {"mobile": true, "width": 412, "height": 823, "deviceScaleFactor": 1.75, "disabled": false}, "emulatedUserAgent": "Mozilla/5.0 (Linux; Android 11; moto g power (2022)) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "auditMode": false, "gatherMode": false, "clearStorageTypes": ["file_systems", "shader_cache", "service_workers", "cache_storage"], "disableStorageReset": false, "debugNavigation": false, "channel": "cli", "usePassiveGathering": false, "disableFullPageScreenshot": false, "skipAboutBlank": false, "blankPage": "about:blank", "ignoreStatusCode": false, "locale": "en-US", "blockedUrlPatterns": null, "additionalTraceCategories": null, "extraHeaders": null, "precomputedLanternData": null, "onlyAudits": null, "onlyCategories": null, "skipAudits": null}, "categories": {"performance": {"title": "Performance", "supportedModes": ["navigation", "timespan", "snapshot"], "auditRefs": [{"id": "first-contentful-paint", "weight": 10, "group": "metrics", "acronym": "FCP"}, {"id": "largest-contentful-paint", "weight": 25, "group": "metrics", "acronym": "LCP"}, {"id": "total-blocking-time", "weight": 30, "group": "metrics", "acronym": "TBT"}, {"id": "cumulative-layout-shift", "weight": 25, "group": "metrics", "acronym": "CLS"}, {"id": "speed-index", "weight": 10, "group": "metrics", "acronym": "SI"}, {"id": "cache-insight", "weight": 0, "group": "hidden"}, {"id": "cls-culprits-insight", "weight": 0, "group": "hidden"}, {"id": "document-latency-insight", "weight": 0, "group": "hidden"}, {"id": "dom-size-insight", "weight": 0, "group": "hidden"}, {"id": "duplicated-javascript-insight", "weight": 0, "group": "hidden"}, {"id": "font-display-insight", "weight": 0, "group": "hidden"}, {"id": "forced-reflow-insight", "weight": 0, "group": "hidden"}, {"id": "image-delivery-insight", "weight": 0, "group": "hidden"}, {"id": "inp-breakdown-insight", "weight": 0, "group": "hidden"}, {"id": "lcp-breakdown-insight", "weight": 0, "group": "hidden"}, {"id": "lcp-discovery-insight", "weight": 0, "group": "hidden"}, {"id": "legacy-javascript-insight", "weight": 0, "group": "hidden"}, {"id": "modern-http-insight", "weight": 0, "group": "hidden"}, {"id": "network-dependency-tree-insight", "weight": 0, "group": "hidden"}, {"id": "render-blocking-insight", "weight": 0, "group": "hidden"}, {"id": "third-parties-insight", "weight": 0, "group": "hidden"}, {"id": "viewport-insight", "weight": 0, "group": "hidden"}, {"id": "interactive", "weight": 0, "group": "hidden", "acronym": "TTI"}, {"id": "max-potential-fid", "weight": 0, "group": "hidden"}, {"id": "first-meaningful-paint", "weight": 0, "acronym": "FMP", "group": "hidden"}, {"id": "render-blocking-resources", "weight": 0, "group": "diagnostics"}, {"id": "uses-responsive-images", "weight": 0, "group": "diagnostics"}, {"id": "offscreen-images", "weight": 0, "group": "diagnostics"}, {"id": "unminified-css", "weight": 0, "group": "diagnostics"}, {"id": "unminified-javascript", "weight": 0, "group": "diagnostics"}, {"id": "unused-css-rules", "weight": 0, "group": "diagnostics"}, {"id": "unused-javascript", "weight": 0, "group": "diagnostics"}, {"id": "uses-optimized-images", "weight": 0, "group": "diagnostics"}, {"id": "modern-image-formats", "weight": 0, "group": "diagnostics"}, {"id": "uses-text-compression", "weight": 0, "group": "diagnostics"}, {"id": "uses-rel-preconnect", "weight": 0, "group": "diagnostics"}, {"id": "server-response-time", "weight": 0, "group": "diagnostics"}, {"id": "redirects", "weight": 0, "group": "diagnostics"}, {"id": "uses-http2", "weight": 0, "group": "diagnostics"}, {"id": "efficient-animated-content", "weight": 0, "group": "diagnostics"}, {"id": "duplicated-javascript", "weight": 0, "group": "diagnostics"}, {"id": "legacy-javascript", "weight": 0, "group": "diagnostics"}, {"id": "prioritize-lcp-image", "weight": 0, "group": "diagnostics"}, {"id": "total-byte-weight", "weight": 0, "group": "diagnostics"}, {"id": "uses-long-cache-ttl", "weight": 0, "group": "diagnostics"}, {"id": "dom-size", "weight": 0, "group": "diagnostics"}, {"id": "critical-request-chains", "weight": 0, "group": "diagnostics"}, {"id": "user-timings", "weight": 0, "group": "diagnostics"}, {"id": "bootup-time", "weight": 0, "group": "diagnostics"}, {"id": "mainthread-work-breakdown", "weight": 0, "group": "diagnostics"}, {"id": "font-display", "weight": 0, "group": "diagnostics"}, {"id": "third-party-summary", "weight": 0, "group": "diagnostics"}, {"id": "third-party-facades", "weight": 0, "group": "diagnostics"}, {"id": "largest-contentful-paint-element", "weight": 0, "group": "diagnostics"}, {"id": "lcp-lazy-loaded", "weight": 0, "group": "diagnostics"}, {"id": "layout-shifts", "weight": 0, "group": "diagnostics"}, {"id": "uses-passive-event-listeners", "weight": 0, "group": "diagnostics"}, {"id": "no-document-write", "weight": 0, "group": "diagnostics"}, {"id": "long-tasks", "weight": 0, "group": "diagnostics"}, {"id": "non-composited-animations", "weight": 0, "group": "diagnostics"}, {"id": "unsized-images", "weight": 0, "group": "diagnostics"}, {"id": "viewport", "weight": 0, "group": "diagnostics"}, {"id": "bf-cache", "weight": 0, "group": "diagnostics"}, {"id": "network-requests", "weight": 0, "group": "hidden"}, {"id": "network-rtt", "weight": 0, "group": "hidden"}, {"id": "network-server-latency", "weight": 0, "group": "hidden"}, {"id": "main-thread-tasks", "weight": 0, "group": "hidden"}, {"id": "diagnostics", "weight": 0, "group": "hidden"}, {"id": "metrics", "weight": 0, "group": "hidden"}, {"id": "screenshot-thumbnails", "weight": 0, "group": "hidden"}, {"id": "final-screenshot", "weight": 0, "group": "hidden"}, {"id": "script-treemap-data", "weight": 0, "group": "hidden"}, {"id": "resource-summary", "weight": 0, "group": "hidden"}], "id": "performance", "score": 0.77}, "accessibility": {"title": "Accessibility", "description": "These checks highlight opportunities to [improve the accessibility of your web app](https://developer.chrome.com/docs/lighthouse/accessibility/). Automatic detection can only detect a subset of issues and does not guarantee the accessibility of your web app, so [manual testing](https://web.dev/articles/how-to-review) is also encouraged.", "manualDescription": "These items address areas which an automated testing tool cannot cover. Learn more in our guide on [conducting an accessibility review](https://web.dev/articles/how-to-review).", "supportedModes": ["navigation", "snapshot"], "auditRefs": [{"id": "accesskeys", "weight": 0, "group": "a11y-navigation"}, {"id": "aria-allowed-attr", "weight": 10, "group": "a11y-aria"}, {"id": "aria-allowed-role", "weight": 0, "group": "a11y-aria"}, {"id": "aria-command-name", "weight": 0, "group": "a11y-aria"}, {"id": "aria-conditional-attr", "weight": 7, "group": "a11y-aria"}, {"id": "aria-deprecated-role", "weight": 0, "group": "a11y-aria"}, {"id": "aria-dialog-name", "weight": 0, "group": "a11y-aria"}, {"id": "aria-hidden-body", "weight": 10, "group": "a11y-aria"}, {"id": "aria-hidden-focus", "weight": 7, "group": "a11y-aria"}, {"id": "aria-input-field-name", "weight": 0, "group": "a11y-aria"}, {"id": "aria-meter-name", "weight": 0, "group": "a11y-aria"}, {"id": "aria-progressbar-name", "weight": 0, "group": "a11y-aria"}, {"id": "aria-prohibited-attr", "weight": 7, "group": "a11y-aria"}, {"id": "aria-required-attr", "weight": 0, "group": "a11y-aria"}, {"id": "aria-required-children", "weight": 0, "group": "a11y-aria"}, {"id": "aria-required-parent", "weight": 0, "group": "a11y-aria"}, {"id": "aria-roles", "weight": 0, "group": "a11y-aria"}, {"id": "aria-text", "weight": 0, "group": "a11y-aria"}, {"id": "aria-toggle-field-name", "weight": 0, "group": "a11y-aria"}, {"id": "aria-tooltip-name", "weight": 0, "group": "a11y-aria"}, {"id": "aria-treeitem-name", "weight": 0, "group": "a11y-aria"}, {"id": "aria-valid-attr-value", "weight": 10, "group": "a11y-aria"}, {"id": "aria-valid-attr", "weight": 10, "group": "a11y-aria"}, {"id": "button-name", "weight": 10, "group": "a11y-names-labels"}, {"id": "bypass", "weight": 0, "group": "a11y-navigation"}, {"id": "color-contrast", "weight": 7, "group": "a11y-color-contrast"}, {"id": "definition-list", "weight": 0, "group": "a11y-tables-lists"}, {"id": "dlitem", "weight": 0, "group": "a11y-tables-lists"}, {"id": "document-title", "weight": 7, "group": "a11y-names-labels"}, {"id": "duplicate-id-aria", "weight": 0, "group": "a11y-aria"}, {"id": "form-field-multiple-labels", "weight": 0, "group": "a11y-names-labels"}, {"id": "frame-title", "weight": 0, "group": "a11y-names-labels"}, {"id": "heading-order", "weight": 3, "group": "a11y-navigation"}, {"id": "html-has-lang", "weight": 7, "group": "a11y-language"}, {"id": "html-lang-valid", "weight": 7, "group": "a11y-language"}, {"id": "html-xml-lang-mismatch", "weight": 0, "group": "a11y-language"}, {"id": "image-alt", "weight": 0, "group": "a11y-names-labels"}, {"id": "image-redundant-alt", "weight": 0, "group": "a11y-names-labels"}, {"id": "input-button-name", "weight": 0, "group": "a11y-names-labels"}, {"id": "input-image-alt", "weight": 0, "group": "a11y-names-labels"}, {"id": "label", "weight": 7, "group": "a11y-names-labels"}, {"id": "link-in-text-block", "weight": 0, "group": "a11y-color-contrast"}, {"id": "link-name", "weight": 0, "group": "a11y-names-labels"}, {"id": "list", "weight": 0, "group": "a11y-tables-lists"}, {"id": "listitem", "weight": 0, "group": "a11y-tables-lists"}, {"id": "meta-refresh", "weight": 0, "group": "a11y-best-practices"}, {"id": "meta-viewport", "weight": 10, "group": "a11y-best-practices"}, {"id": "object-alt", "weight": 0, "group": "a11y-names-labels"}, {"id": "select-name", "weight": 0, "group": "a11y-names-labels"}, {"id": "skip-link", "weight": 0, "group": "a11y-names-labels"}, {"id": "tabindex", "weight": 0, "group": "a11y-navigation"}, {"id": "table-duplicate-name", "weight": 0, "group": "a11y-tables-lists"}, {"id": "target-size", "weight": 7, "group": "a11y-best-practices"}, {"id": "td-headers-attr", "weight": 0, "group": "a11y-tables-lists"}, {"id": "th-has-data-cells", "weight": 0, "group": "a11y-tables-lists"}, {"id": "valid-lang", "weight": 0, "group": "a11y-language"}, {"id": "video-caption", "weight": 0, "group": "a11y-audio-video"}, {"id": "focusable-controls", "weight": 0}, {"id": "interactive-element-affordance", "weight": 0}, {"id": "logical-tab-order", "weight": 0}, {"id": "visual-order-follows-dom", "weight": 0}, {"id": "focus-traps", "weight": 0}, {"id": "managed-focus", "weight": 0}, {"id": "use-landmarks", "weight": 0}, {"id": "offscreen-content-hidden", "weight": 0}, {"id": "custom-controls-labels", "weight": 0}, {"id": "custom-controls-roles", "weight": 0}, {"id": "empty-heading", "weight": 0, "group": "hidden"}, {"id": "identical-links-same-purpose", "weight": 0, "group": "hidden"}, {"id": "landmark-one-main", "weight": 0, "group": "hidden"}, {"id": "label-content-name-mismatch", "weight": 0, "group": "hidden"}, {"id": "table-fake-caption", "weight": 0, "group": "hidden"}, {"id": "td-has-header", "weight": 0, "group": "hidden"}], "id": "accessibility", "score": 0.87}, "best-practices": {"title": "Best Practices", "supportedModes": ["navigation", "timespan", "snapshot"], "auditRefs": [{"id": "is-on-https", "weight": 5, "group": "best-practices-trust-safety"}, {"id": "redirects-http", "weight": 0, "group": "best-practices-trust-safety"}, {"id": "geolocation-on-start", "weight": 1, "group": "best-practices-trust-safety"}, {"id": "notification-on-start", "weight": 1, "group": "best-practices-trust-safety"}, {"id": "csp-xss", "weight": 0, "group": "best-practices-trust-safety"}, {"id": "has-hsts", "weight": 0, "group": "best-practices-trust-safety"}, {"id": "origin-isolation", "weight": 0, "group": "best-practices-trust-safety"}, {"id": "clickjacking-mitigation", "weight": 0, "group": "best-practices-trust-safety"}, {"id": "trusted-types-xss", "weight": 0, "group": "best-practices-trust-safety"}, {"id": "paste-preventing-inputs", "weight": 3, "group": "best-practices-ux"}, {"id": "image-aspect-ratio", "weight": 1, "group": "best-practices-ux"}, {"id": "image-size-responsive", "weight": 1, "group": "best-practices-ux"}, {"id": "viewport", "weight": 1, "group": "best-practices-ux"}, {"id": "font-size", "weight": 1, "group": "best-practices-ux"}, {"id": "doctype", "weight": 1, "group": "best-practices-browser-compat"}, {"id": "charset", "weight": 1, "group": "best-practices-browser-compat"}, {"id": "js-libraries", "weight": 0, "group": "best-practices-general"}, {"id": "deprecations", "weight": 5, "group": "best-practices-general"}, {"id": "third-party-cookies", "weight": 5, "group": "best-practices-general"}, {"id": "errors-in-console", "weight": 1, "group": "best-practices-general"}, {"id": "valid-source-maps", "weight": 0, "group": "best-practices-general"}, {"id": "inspector-issues", "weight": 1, "group": "best-practices-general"}], "id": "best-practices", "score": 1}, "seo": {"title": "SEO", "description": "These checks ensure that your page is following basic search engine optimization advice. There are many additional factors Lighthouse does not score here that may affect your search ranking, including performance on [Core Web Vitals](https://web.dev/explore/vitals). [Learn more about Google Search Essentials](https://support.google.com/webmasters/answer/35769).", "manualDescription": "Run these additional validators on your site to check additional SEO best practices.", "supportedModes": ["navigation", "snapshot"], "auditRefs": [{"id": "is-crawlable", "weight": 4.043478260869565, "group": "seo-crawl"}, {"id": "document-title", "weight": 1, "group": "seo-content"}, {"id": "meta-description", "weight": 1, "group": "seo-content"}, {"id": "http-status-code", "weight": 1, "group": "seo-crawl"}, {"id": "link-text", "weight": 1, "group": "seo-content"}, {"id": "crawlable-anchors", "weight": 1, "group": "seo-crawl"}, {"id": "robots-txt", "weight": 1, "group": "seo-crawl"}, {"id": "image-alt", "weight": 0, "group": "seo-content"}, {"id": "hreflang", "weight": 1, "group": "seo-content"}, {"id": "canonical", "weight": 0, "group": "seo-content"}, {"id": "structured-data", "weight": 0}], "id": "seo", "score": 0.91}}, "categoryGroups": {"metrics": {"title": "Metrics"}, "insights": {"title": "Insights", "description": "These insights are also available in the Chrome DevTools Performance Panel - [record a trace](https://developer.chrome.com/docs/devtools/performance/reference) to view more detailed information."}, "diagnostics": {"title": "Diagnostics", "description": "More information about the performance of your application. These numbers don't [directly affect](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) the Performance score."}, "a11y-best-practices": {"title": "Best practices", "description": "These items highlight common accessibility best practices."}, "a11y-color-contrast": {"title": "Contrast", "description": "These are opportunities to improve the legibility of your content."}, "a11y-names-labels": {"title": "Names and labels", "description": "These are opportunities to improve the semantics of the controls in your application. This may enhance the experience for users of assistive technology, like a screen reader."}, "a11y-navigation": {"title": "Navigation", "description": "These are opportunities to improve keyboard navigation in your application."}, "a11y-aria": {"title": "ARIA", "description": "These are opportunities to improve the usage of ARIA in your application which may enhance the experience for users of assistive technology, like a screen reader."}, "a11y-language": {"title": "Internationalization and localization", "description": "These are opportunities to improve the interpretation of your content by users in different locales."}, "a11y-audio-video": {"title": "Audio and video", "description": "These are opportunities to provide alternative content for audio and video. This may improve the experience for users with hearing or vision impairments."}, "a11y-tables-lists": {"title": "Tables and lists", "description": "These are opportunities to improve the experience of reading tabular or list data using assistive technology, like a screen reader."}, "seo-mobile": {"title": "Mobile Friendly", "description": "Make sure your pages are mobile friendly so users don’t have to pinch or zoom in order to read the content pages. [Learn how to make pages mobile-friendly](https://developers.google.com/search/mobile-sites/)."}, "seo-content": {"title": "Content Best Practices", "description": "Format your HTML in a way that enables crawlers to better understand your app’s content."}, "seo-crawl": {"title": "Crawling and Indexing", "description": "To appear in search results, crawlers need access to your app."}, "best-practices-trust-safety": {"title": "Trust and Safety"}, "best-practices-ux": {"title": "User Experience"}, "best-practices-browser-compat": {"title": "Browser Compatibility"}, "best-practices-general": {"title": "General"}, "hidden": {"title": ""}}, "stackPacks": [], "entities": [{"name": "web.app", "origins": ["https://rag-prompt-library.web.app"], "isFirstParty": true, "isUnrecognized": true}, {"name": "Other Google APIs/SDKs", "homepage": "https://developers.google.com/apis-explorer/#p/", "origins": ["https://apis.google.com", "https://www.googleapis.com"], "category": "utility"}, {"name": "firebaseapp.com", "origins": ["https://rag-prompt-library.firebaseapp.com"], "isUnrecognized": true}], "fullPageScreenshot": {"screenshot": {"data": "data:image/webp;base64,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*******************************************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", "width": 412, "height": 1082}, "nodes": {"page-0-P": {"id": "", "top": 232, "bottom": 344, "left": 16, "right": 396, "width": 380, "height": 112}, "page-1-META": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "page-2-svg": {"id": "", "top": 16, "bottom": 64, "left": 73, "right": 121, "width": 48, "height": 48}, "page-3-path": {"id": "", "top": 833, "bottom": 845, "left": 342, "right": 358, "width": 17, "height": 12}, "page-4-DIV": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "page-5-DIV": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-0-BUTTON": {"id": "", "top": 8, "bottom": 28, "left": 356, "right": 376, "width": 20, "height": 20}, "1-1-BUTTON": {"id": "", "top": 829, "bottom": 849, "left": 340, "right": 360, "width": 20, "height": 20}, "1-2-INPUT": {"id": "password", "top": 818, "bottom": 860, "left": 40, "right": 372, "width": 332, "height": 42}, "1-3-H1": {"id": "", "top": 22, "bottom": 58, "left": 133, "right": 339, "width": 206, "height": 36}, "1-4-DIV": {"id": "", "top": 0, "bottom": 1082, "left": 0, "right": 412, "width": 412, "height": 1082}, "1-5-path": {"id": "", "top": 833, "bottom": 845, "left": 342, "right": 358, "width": 17, "height": 12}, "1-6-svg": {"id": "", "top": 16, "bottom": 64, "left": 73, "right": 121, "width": 48, "height": 48}, "1-7-FORM": {"id": "", "top": 712, "bottom": 916, "left": 40, "right": 372, "width": 332, "height": 204}, "1-8-LABEL": {"id": "", "top": 712, "bottom": 732, "left": 40, "right": 372, "width": 332, "height": 20}, "1-9-LABEL": {"id": "", "top": 794, "bottom": 814, "left": 40, "right": 372, "width": 332, "height": 20}, "1-10-INPUT": {"id": "email", "top": 736, "bottom": 778, "left": 40, "right": 372, "width": 332, "height": 42}, "1-11-LINK": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-12-LINK": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-13-LINK": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-14-LINK": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-15-LINK": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-16-META": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-17-META": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-18-META": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-19-META": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-20-META": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-21-META": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-22-META": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-23-META": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-24-META": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-25-META": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}}}, "timing": {"entries": [{"startTime": 21774.1, "name": "lh:config", "duration": 983.86, "entryType": "measure"}, {"startTime": 21775.7, "name": "lh:config:resolveArtifactsToDefns", "duration": 84.29, "entryType": "measure"}, {"startTime": 22758.16, "name": "lh:runner:gather", "duration": 10672.25, "entryType": "measure"}, {"startTime": 22873.02, "name": "lh:driver:connect", "duration": 20.55, "entryType": "measure"}, {"startTime": 22894.12, "name": "lh:driver:navigate", "duration": 10.06, "entryType": "measure"}, {"startTime": 22904.81, "name": "lh:gather:getBenchmarkIndex", "duration": 1009.22, "entryType": "measure"}, {"startTime": 23914.67, "name": "lh:gather:getVersion", "duration": 2.05, "entryType": "measure"}, {"startTime": 23916.89, "name": "lh:gather:getDevicePixelRatio", "duration": 1.88, "entryType": "measure"}, {"startTime": 23920.07, "name": "lh:prepare:navigationMode", "duration": 74.56, "entryType": "measure"}, {"startTime": 23942.2, "name": "lh:storage:clearDataForOrigin", "duration": 15.22, "entryType": "measure"}, {"startTime": 23957.68, "name": "lh:storage:clearBrowserCaches", "duration": 33.76, "entryType": "measure"}, {"startTime": 23992.81, "name": "lh:gather:prepareThrottlingAndNetwork", "duration": 1.76, "entryType": "measure"}, {"startTime": 24066.76, "name": "lh:driver:navigate", "duration": 5390.41, "entryType": "measure"}, {"startTime": 29912.99, "name": "lh:computed:NetworkRecords", "duration": 1.54, "entryType": "measure"}, {"startTime": 29915.22, "name": "lh:gather:getArtifact:DevtoolsLog", "duration": 0.25, "entryType": "measure"}, {"startTime": 29915.52, "name": "lh:gather:getArtifact:Trace", "duration": 0.19, "entryType": "measure"}, {"startTime": 29915.74, "name": "lh:gather:getArtifact:Accessibility", "duration": 229.85, "entryType": "measure"}, {"startTime": 30145.68, "name": "lh:gather:getArtifact:AnchorElements", "duration": 8.77, "entryType": "measure"}, {"startTime": 30154.54, "name": "lh:gather:getArtifact:ConsoleMessages", "duration": 0.36, "entryType": "measure"}, {"startTime": 30154.95, "name": "lh:gather:getArtifact:CSSUsage", "duration": 166.67, "entryType": "measure"}, {"startTime": 30321.67, "name": "lh:gather:getArtifact:Doctype", "duration": 1.53, "entryType": "measure"}, {"startTime": 30323.3, "name": "lh:gather:getArtifact:DOMStats", "duration": 5.77, "entryType": "measure"}, {"startTime": 30329.15, "name": "lh:gather:getArtifact:FontSize", "duration": 11.46, "entryType": "measure"}, {"startTime": 30340.68, "name": "lh:gather:getArtifact:Inputs", "duration": 8.83, "entryType": "measure"}, {"startTime": 30349.65, "name": "lh:gather:getArtifact:ImageElements", "duration": 11.16, "entryType": "measure"}, {"startTime": 30361.03, "name": "lh:gather:getArtifact:InspectorIssues", "duration": 0.59, "entryType": "measure"}, {"startTime": 30361.67, "name": "lh:gather:getArtifact:JsUsage", "duration": 0.24, "entryType": "measure"}, {"startTime": 30361.98, "name": "lh:gather:getArtifact:LinkElements", "duration": 4.89, "entryType": "measure"}, {"startTime": 30366.6, "name": "lh:computed:MainResource", "duration": 0.22, "entryType": "measure"}, {"startTime": 30366.91, "name": "lh:gather:getArtifact:MainDocumentContent", "duration": 2.15, "entryType": "measure"}, {"startTime": 30369.1, "name": "lh:gather:getArtifact:MetaElements", "duration": 3.49, "entryType": "measure"}, {"startTime": 30372.73, "name": "lh:gather:getArtifact:NetworkUserAgent", "duration": 0.3, "entryType": "measure"}, {"startTime": 30373.09, "name": "lh:gather:getArtifact:OptimizedImages", "duration": 0.41, "entryType": "measure"}, {"startTime": 30373.55, "name": "lh:gather:getArtifact:ResponseCompression", "duration": 0.73, "entryType": "measure"}, {"startTime": 30374.33, "name": "lh:gather:getArtifact:RobotsTxt", "duration": 658.58, "entryType": "measure"}, {"startTime": 31032.96, "name": "lh:gather:getArtifact:Scripts", "duration": 0.33, "entryType": "measure"}, {"startTime": 31033.36, "name": "lh:gather:getArtifact:SourceMaps", "duration": 0.19, "entryType": "measure"}, {"startTime": 31033.58, "name": "lh:gather:getArtifact:Stacks", "duration": 12.92, "entryType": "measure"}, {"startTime": 31033.78, "name": "lh:gather:collectStacks", "duration": 12.65, "entryType": "measure"}, {"startTime": 31046.53, "name": "lh:gather:getArtifact:Stylesheets", "duration": 11.48, "entryType": "measure"}, {"startTime": 31058.08, "name": "lh:gather:getArtifact:TraceElements", "duration": 944.65, "entryType": "measure"}, {"startTime": 31058.5, "name": "lh:computed:TraceEngineResult", "duration": 834.99, "entryType": "measure"}, {"startTime": 31058.6, "name": "lh:computed:ProcessedTrace", "duration": 46.98, "entryType": "measure"}, {"startTime": 31108.24, "name": "lh:computed:TraceEngineResult:total", "duration": 771.04, "entryType": "measure"}, {"startTime": 31108.5, "name": "lh:computed:TraceEngineResult:parse", "duration": 672.14, "entryType": "measure"}, {"startTime": 31109.51, "name": "lh:computed:TraceEngineResult:parse:handleEvent", "duration": 188.9, "entryType": "measure"}, {"startTime": 31298.49, "name": "lh:computed:TraceEngineResult:parse:Meta:finalize", "duration": 1.71, "entryType": "measure"}, {"startTime": 31300.8, "name": "lh:computed:TraceEngineResult:parse:AnimationFrames:finalize", "duration": 13.72, "entryType": "measure"}, {"startTime": 31314.65, "name": "lh:computed:TraceEngineResult:parse:Animations:finalize", "duration": 16.66, "entryType": "measure"}, {"startTime": 31331.38, "name": "lh:computed:TraceEngineResult:parse:Samples:finalize", "duration": 14.81, "entryType": "measure"}, {"startTime": 31346.43, "name": "lh:computed:TraceEngineResult:parse:AuctionWorklets:finalize", "duration": 15.42, "entryType": "measure"}, {"startTime": 31361.96, "name": "lh:computed:TraceEngineResult:parse:NetworkRequests:finalize", "duration": 22.18, "entryType": "measure"}, {"startTime": 31384.25, "name": "lh:computed:TraceEngineResult:parse:<PERSON><PERSON>er:finalize", "duration": 29.55, "entryType": "measure"}, {"startTime": 31413.93, "name": "lh:computed:TraceEngineResult:parse:Flows:finalize", "duration": 12.38, "entryType": "measure"}, {"startTime": 31426.38, "name": "lh:computed:TraceEngineResult:parse:AsyncJSCalls:finalize", "duration": 17.77, "entryType": "measure"}, {"startTime": 31444.26, "name": "lh:computed:TraceEngineResult:parse:DOMStats:finalize", "duration": 11.4, "entryType": "measure"}, {"startTime": 31455.77, "name": "lh:computed:TraceEngineResult:parse:UserTimings:finalize", "duration": 16.12, "entryType": "measure"}, {"startTime": 31472.07, "name": "lh:computed:TraceEngineResult:parse:ExtensionTraceData:finalize", "duration": 16.36, "entryType": "measure"}, {"startTime": 31488.53, "name": "lh:computed:TraceEngineResult:parse:LayerTree:finalize", "duration": 15.79, "entryType": "measure"}, {"startTime": 31504.38, "name": "lh:computed:TraceEngineResult:parse:Frames:finalize", "duration": 35.43, "entryType": "measure"}, {"startTime": 31539.87, "name": "lh:computed:TraceEngineResult:parse:GPU:finalize", "duration": 12.42, "entryType": "measure"}, {"startTime": 31552.37, "name": "lh:computed:TraceEngineResult:parse:ImagePainting:finalize", "duration": 15.4, "entryType": "measure"}, {"startTime": 31567.87, "name": "lh:computed:TraceEngineResult:parse:Initiators:finalize", "duration": 16.57, "entryType": "measure"}, {"startTime": 31584.61, "name": "lh:computed:TraceEngineResult:parse:Invalidations:finalize", "duration": 14.86, "entryType": "measure"}, {"startTime": 31599.59, "name": "lh:computed:TraceEngineResult:parse:PageLoadMetrics:finalize", "duration": 17.66, "entryType": "measure"}, {"startTime": 31617.33, "name": "lh:computed:TraceEngineResult:parse:LargestImagePaint:finalize", "duration": 14.9, "entryType": "measure"}, {"startTime": 31632.33, "name": "lh:computed:TraceEngineResult:parse:LargestTextPaint:finalize", "duration": 15.35, "entryType": "measure"}, {"startTime": 31647.85, "name": "lh:computed:TraceEngineResult:parse:Screenshots:finalize", "duration": 19.87, "entryType": "measure"}, {"startTime": 31667.78, "name": "lh:computed:TraceEngineResult:parse:LayoutShifts:finalize", "duration": 13.46, "entryType": "measure"}, {"startTime": 31681.38, "name": "lh:computed:TraceEngineResult:parse:Memory:finalize", "duration": 14.34, "entryType": "measure"}, {"startTime": 31695.91, "name": "lh:computed:TraceEngineResult:parse:PageFrames:finalize", "duration": 14.62, "entryType": "measure"}, {"startTime": 31710.65, "name": "lh:computed:TraceEngineResult:parse:Scripts:finalize", "duration": 16.4, "entryType": "measure"}, {"startTime": 31727.13, "name": "lh:computed:TraceEngineResult:parse:SelectorStats:finalize", "duration": 15.37, "entryType": "measure"}, {"startTime": 31742.62, "name": "lh:computed:TraceEngineResult:parse:UserInteractions:finalize", "duration": 16.96, "entryType": "measure"}, {"startTime": 31759.71, "name": "lh:computed:TraceEngineResult:parse:Workers:finalize", "duration": 0.59, "entryType": "measure"}, {"startTime": 31760.37, "name": "lh:computed:TraceEngineResult:parse:Warnings:finalize", "duration": 14.62, "entryType": "measure"}, {"startTime": 31775.08, "name": "lh:computed:TraceEngineResult:parse:clone", "duration": 5.4, "entryType": "measure"}, {"startTime": 31780.65, "name": "lh:computed:TraceEngineResult:insights", "duration": 98.59, "entryType": "measure"}, {"startTime": 31781.68, "name": "lh:computed:TraceEngineResult:insights:CLSCulprits", "duration": 1.05, "entryType": "measure"}, {"startTime": 31782.78, "name": "lh:computed:TraceEngineResult:insights:<PERSON>ache", "duration": 0.54, "entryType": "measure"}, {"startTime": 31783.35, "name": "lh:computed:TraceEngineResult:insights:DOMSize", "duration": 0.78, "entryType": "measure"}, {"startTime": 31784.16, "name": "lh:computed:TraceEngineResult:insights:DocumentLatency", "duration": 0.39, "entryType": "measure"}, {"startTime": 31784.59, "name": "lh:computed:TraceEngineResult:insights:DuplicatedJavaScript", "duration": 2.8, "entryType": "measure"}, {"startTime": 31787.43, "name": "lh:computed:TraceEngineResult:insights:FontDisplay", "duration": 0.55, "entryType": "measure"}, {"startTime": 31788.03, "name": "lh:computed:TraceEngineResult:insights:ForcedReflow", "duration": 0.58, "entryType": "measure"}, {"startTime": 31788.64, "name": "lh:computed:TraceEngineResult:insights:INPBreakdown", "duration": 0.36, "entryType": "measure"}, {"startTime": 31789.04, "name": "lh:computed:TraceEngineResult:insights:ImageDelivery", "duration": 0.51, "entryType": "measure"}, {"startTime": 31789.58, "name": "lh:computed:TraceEngineResult:insights:LCPBreakdown", "duration": 0.3, "entryType": "measure"}, {"startTime": 31789.91, "name": "lh:computed:TraceEngineResult:insights:LCPDiscovery", "duration": 0.35, "entryType": "measure"}, {"startTime": 31790.31, "name": "lh:computed:TraceEngineResult:insights:LegacyJavaScript", "duration": 0.45, "entryType": "measure"}, {"startTime": 31790.79, "name": "lh:computed:TraceEngineResult:insights:ModernHTTP", "duration": 0.49, "entryType": "measure"}, {"startTime": 31791.3, "name": "lh:computed:TraceEngineResult:insights:NetworkDependencyTree", "duration": 0.25, "entryType": "measure"}, {"startTime": 31791.58, "name": "lh:computed:TraceEngineResult:insights:RenderBlocking", "duration": 0.28, "entryType": "measure"}, {"startTime": 31791.88, "name": "lh:computed:TraceEngineResult:insights:SlowCSSSelector", "duration": 0.33, "entryType": "measure"}, {"startTime": 31792.22, "name": "lh:computed:TraceEngineResult:insights:ThirdParties", "duration": 2.3, "entryType": "measure"}, {"startTime": 31794.55, "name": "lh:computed:TraceEngineResult:insights:Viewport", "duration": 0.4, "entryType": "measure"}, {"startTime": 31795.2, "name": "lh:computed:TraceEngineResult:insights:createLanternContext", "duration": 33.11, "entryType": "measure"}, {"startTime": 31828.38, "name": "lh:computed:TraceEngineResult:insights:CLSCulprits", "duration": 0.69, "entryType": "measure"}, {"startTime": 31829.12, "name": "lh:computed:TraceEngineResult:insights:<PERSON>ache", "duration": 3.66, "entryType": "measure"}, {"startTime": 31832.82, "name": "lh:computed:TraceEngineResult:insights:DOMSize", "duration": 0.42, "entryType": "measure"}, {"startTime": 31833.29, "name": "lh:computed:TraceEngineResult:insights:DocumentLatency", "duration": 0.76, "entryType": "measure"}, {"startTime": 31834.09, "name": "lh:computed:TraceEngineResult:insights:DuplicatedJavaScript", "duration": 0.68, "entryType": "measure"}, {"startTime": 31834.79, "name": "lh:computed:TraceEngineResult:insights:FontDisplay", "duration": 0.18, "entryType": "measure"}, {"startTime": 31835.23, "name": "lh:computed:TraceEngineResult:insights:ForcedReflow", "duration": 0.18, "entryType": "measure"}, {"startTime": 31835.44, "name": "lh:computed:TraceEngineResult:insights:INPBreakdown", "duration": 0.14, "entryType": "measure"}, {"startTime": 31835.6, "name": "lh:computed:TraceEngineResult:insights:ImageDelivery", "duration": 0.18, "entryType": "measure"}, {"startTime": 31835.8, "name": "lh:computed:TraceEngineResult:insights:LCPBreakdown", "duration": 0.48, "entryType": "measure"}, {"startTime": 31836.3, "name": "lh:computed:TraceEngineResult:insights:LCPDiscovery", "duration": 0.2, "entryType": "measure"}, {"startTime": 31836.52, "name": "lh:computed:TraceEngineResult:insights:LegacyJavaScript", "duration": 20.9, "entryType": "measure"}, {"startTime": 31857.48, "name": "lh:computed:TraceEngineResult:insights:ModernHTTP", "duration": 1.95, "entryType": "measure"}, {"startTime": 31859.45, "name": "lh:computed:TraceEngineResult:insights:NetworkDependencyTree", "duration": 2.14, "entryType": "measure"}, {"startTime": 31861.61, "name": "lh:computed:TraceEngineResult:insights:RenderBlocking", "duration": 0.47, "entryType": "measure"}, {"startTime": 31862.1, "name": "lh:computed:TraceEngineResult:insights:SlowCSSSelector", "duration": 0.16, "entryType": "measure"}, {"startTime": 31862.28, "name": "lh:computed:TraceEngineResult:insights:ThirdParties", "duration": 15.27, "entryType": "measure"}, {"startTime": 31877.61, "name": "lh:computed:TraceEngineResult:insights:Viewport", "duration": 0.44, "entryType": "measure"}, {"startTime": 31897.86, "name": "lh:computed:ProcessedNavigation", "duration": 1.31, "entryType": "measure"}, {"startTime": 31899.31, "name": "lh:computed:CumulativeLayoutShift", "duration": 83.14, "entryType": "measure"}, {"startTime": 31983.43, "name": "lh:computed:Responsiveness", "duration": 0.28, "entryType": "measure"}, {"startTime": 32002.77, "name": "lh:gather:getArtifact:ViewportDimensions", "duration": 1.42, "entryType": "measure"}, {"startTime": 32004.21, "name": "lh:gather:getArtifact:FullPageScreenshot", "duration": 1097.22, "entryType": "measure"}, {"startTime": 33101.49, "name": "lh:gather:getArtifact:BFCacheFailures", "duration": 305.19, "entryType": "measure"}, {"startTime": 33431.55, "name": "lh:runner:audit", "duration": 1195.06, "entryType": "measure"}, {"startTime": 33431.75, "name": "lh:runner:auditing", "duration": 1193.76, "entryType": "measure"}, {"startTime": 33433.53, "name": "lh:audit:is-on-https", "duration": 3.92, "entryType": "measure"}, {"startTime": 33437.98, "name": "lh:audit:redirects-http", "duration": 2.01, "entryType": "measure"}, {"startTime": 33440.66, "name": "lh:audit:viewport", "duration": 3.36, "entryType": "measure"}, {"startTime": 33441.79, "name": "lh:computed:ViewportMeta", "duration": 0.99, "entryType": "measure"}, {"startTime": 33444.34, "name": "lh:audit:first-contentful-paint", "duration": 15.37, "entryType": "measure"}, {"startTime": 33445.84, "name": "lh:computed:First<PERSON><PERSON>ntful<PERSON><PERSON>t", "duration": 10.96, "entryType": "measure"}, {"startTime": 33446.65, "name": "lh:computed:Lantern<PERSON><PERSON>t<PERSON><PERSON>ntful<PERSON><PERSON>t", "duration": 10.13, "entryType": "measure"}, {"startTime": 33446.84, "name": "lh:computed:PageDependencyGraph", "duration": 6.5, "entryType": "measure"}, {"startTime": 33453.47, "name": "lh:computed:LoadSimulator", "duration": 1.56, "entryType": "measure"}, {"startTime": 33453.68, "name": "lh:computed:NetworkAnalysis", "duration": 1.13, "entryType": "measure"}, {"startTime": 33459.99, "name": "lh:audit:largest-contentful-paint", "duration": 3.73, "entryType": "measure"}, {"startTime": 33460.8, "name": "lh:computed:Largest<PERSON><PERSON>ntful<PERSON><PERSON>t", "duration": 1.92, "entryType": "measure"}, {"startTime": 33460.97, "name": "lh:computed:LanternLargestContentfulPaint", "duration": 1.73, "entryType": "measure"}, {"startTime": 33464.05, "name": "lh:audit:first-meaningful-paint", "duration": 2.48, "entryType": "measure"}, {"startTime": 33467.13, "name": "lh:audit:speed-index", "duration": 272.04, "entryType": "measure"}, {"startTime": 33468.34, "name": "lh:computed:SpeedIndex", "duration": 269.38, "entryType": "measure"}, {"startTime": 33468.53, "name": "lh:computed:LanternSpeedIndex", "duration": 269.15, "entryType": "measure"}, {"startTime": 33468.63, "name": "lh:computed:Speedline", "duration": 265.91, "entryType": "measure"}, {"startTime": 33739.22, "name": "lh:audit:screenshot-thumbnails", "duration": 0.83, "entryType": "measure"}, {"startTime": 33740.1, "name": "lh:audit:final-screenshot", "duration": 1.76, "entryType": "measure"}, {"startTime": 33740.4, "name": "lh:computed:Screenshots", "duration": 1.41, "entryType": "measure"}, {"startTime": 33742.19, "name": "lh:audit:total-blocking-time", "duration": 8.16, "entryType": "measure"}, {"startTime": 33742.98, "name": "lh:computed:TotalBlockingTime", "duration": 5.66, "entryType": "measure"}, {"startTime": 33743.13, "name": "lh:computed:LanternTotalBlockingTime", "duration": 5.48, "entryType": "measure"}, {"startTime": 33743.33, "name": "lh:computed:LanternInteractive", "duration": 2.81, "entryType": "measure"}, {"startTime": 33750.76, "name": "lh:audit:max-potential-fid", "duration": 6.2, "entryType": "measure"}, {"startTime": 33751.64, "name": "lh:computed:MaxPotentialFID", "duration": 3.13, "entryType": "measure"}, {"startTime": 33751.8, "name": "lh:computed:LanternMaxPotentialFID", "duration": 2.94, "entryType": "measure"}, {"startTime": 33757.22, "name": "lh:audit:cumulative-layout-shift", "duration": 1.37, "entryType": "measure"}, {"startTime": 33758.83, "name": "lh:audit:errors-in-console", "duration": 1.73, "entryType": "measure"}, {"startTime": 33759.56, "name": "lh:computed:<PERSON><PERSON><PERSON><PERSON>", "duration": 0.13, "entryType": "measure"}, {"startTime": 33760.78, "name": "lh:audit:server-response-time", "duration": 1.69, "entryType": "measure"}, {"startTime": 33762.74, "name": "lh:audit:interactive", "duration": 1.37, "entryType": "measure"}, {"startTime": 33763.28, "name": "lh:computed:Interactive", "duration": 0.13, "entryType": "measure"}, {"startTime": 33764.3, "name": "lh:audit:user-timings", "duration": 4.39, "entryType": "measure"}, {"startTime": 33764.9, "name": "lh:computed:UserTimings", "duration": 2.62, "entryType": "measure"}, {"startTime": 33769.03, "name": "lh:audit:critical-request-chains", "duration": 7.58, "entryType": "measure"}, {"startTime": 33769.93, "name": "lh:computed:CriticalRequest<PERSON><PERSON>ns", "duration": 1.09, "entryType": "measure"}, {"startTime": 33777.3, "name": "lh:audit:redirects", "duration": 3.11, "entryType": "measure"}, {"startTime": 33780.79, "name": "lh:audit:image-aspect-ratio", "duration": 2.25, "entryType": "measure"}, {"startTime": 33783.57, "name": "lh:audit:image-size-responsive", "duration": 2.4, "entryType": "measure"}, {"startTime": 33784.75, "name": "lh:computed:ImageRecords", "duration": 0.37, "entryType": "measure"}, {"startTime": 33786.3, "name": "lh:audit:deprecations", "duration": 1.9, "entryType": "measure"}, {"startTime": 33788.53, "name": "lh:audit:third-party-cookies", "duration": 1.22, "entryType": "measure"}, {"startTime": 33790.28, "name": "lh:audit:mainthread-work-breakdown", "duration": 22.6, "entryType": "measure"}, {"startTime": 33791.5, "name": "lh:computed:MainThreadTasks", "duration": 18.19, "entryType": "measure"}, {"startTime": 33813.38, "name": "lh:audit:bootup-time", "duration": 18.95, "entryType": "measure"}, {"startTime": 33818.81, "name": "lh:computed:TBTImpactTasks", "duration": 10.36, "entryType": "measure"}, {"startTime": 33832.61, "name": "lh:audit:uses-rel-preconnect", "duration": 3.92, "entryType": "measure"}, {"startTime": 33836.77, "name": "lh:audit:font-display", "duration": 1.57, "entryType": "measure"}, {"startTime": 33838.36, "name": "lh:audit:diagnostics", "duration": 0.82, "entryType": "measure"}, {"startTime": 33839.22, "name": "lh:audit:network-requests", "duration": 2.15, "entryType": "measure"}, {"startTime": 33839.53, "name": "lh:computed:EntityClassification", "duration": 1.35, "entryType": "measure"}, {"startTime": 33841.6, "name": "lh:audit:network-rtt", "duration": 1.49, "entryType": "measure"}, {"startTime": 33843.31, "name": "lh:audit:network-server-latency", "duration": 1.16, "entryType": "measure"}, {"startTime": 33844.5, "name": "lh:audit:main-thread-tasks", "duration": 0.38, "entryType": "measure"}, {"startTime": 33844.91, "name": "lh:audit:metrics", "duration": 3.21, "entryType": "measure"}, {"startTime": 33845.2, "name": "lh:computed:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "duration": 2.67, "entryType": "measure"}, {"startTime": 33845.56, "name": "lh:computed:FirstContentfulPaintAllFrames", "duration": 0.09, "entryType": "measure"}, {"startTime": 33845.68, "name": "lh:computed:LargestContentfulPaintAllFrames", "duration": 0.07, "entryType": "measure"}, {"startTime": 33845.82, "name": "lh:computed:LCPBreakdown", "duration": 1.64, "entryType": "measure"}, {"startTime": 33845.92, "name": "lh:computed:TimeToFirstByte", "duration": 0.18, "entryType": "measure"}, {"startTime": 33846.11, "name": "lh:computed:LCPImageRecord", "duration": 1.32, "entryType": "measure"}, {"startTime": 33848.15, "name": "lh:audit:resource-summary", "duration": 2.24, "entryType": "measure"}, {"startTime": 33848.7, "name": "lh:computed:ResourceSummary", "duration": 0.7, "entryType": "measure"}, {"startTime": 33850.83, "name": "lh:audit:third-party-summary", "duration": 5, "entryType": "measure"}, {"startTime": 33856.08, "name": "lh:audit:third-party-facades", "duration": 6.2, "entryType": "measure"}, {"startTime": 33862.65, "name": "lh:audit:largest-contentful-paint-element", "duration": 2.74, "entryType": "measure"}, {"startTime": 33865.87, "name": "lh:audit:lcp-lazy-loaded", "duration": 2.54, "entryType": "measure"}, {"startTime": 33868.66, "name": "lh:audit:layout-shifts", "duration": 1.82, "entryType": "measure"}, {"startTime": 33870.85, "name": "lh:audit:long-tasks", "duration": 5.11, "entryType": "measure"}, {"startTime": 33876.14, "name": "lh:audit:non-composited-animations", "duration": 1.6, "entryType": "measure"}, {"startTime": 33878.13, "name": "lh:audit:unsized-images", "duration": 1.44, "entryType": "measure"}, {"startTime": 33879.79, "name": "lh:audit:valid-source-maps", "duration": 1.94, "entryType": "measure"}, {"startTime": 33882, "name": "lh:audit:prioritize-lcp-image", "duration": 2.76, "entryType": "measure"}, {"startTime": 33885.14, "name": "lh:audit:csp-xss", "duration": 15.51, "entryType": "measure"}, {"startTime": 33901.1, "name": "lh:audit:has-hsts", "duration": 1.71, "entryType": "measure"}, {"startTime": 33903, "name": "lh:audit:origin-isolation", "duration": 1.59, "entryType": "measure"}, {"startTime": 33904.91, "name": "lh:audit:clickjacking-mitigation", "duration": 1.25, "entryType": "measure"}, {"startTime": 33906.33, "name": "lh:audit:trusted-types-xss", "duration": 1.29, "entryType": "measure"}, {"startTime": 33907.64, "name": "lh:audit:script-treemap-data", "duration": 19.88, "entryType": "measure"}, {"startTime": 33908.15, "name": "lh:computed:ModuleDuplication", "duration": 0.23, "entryType": "measure"}, {"startTime": 33908.42, "name": "lh:computed:UnusedJavascriptSummary", "duration": 0.62, "entryType": "measure"}, {"startTime": 33909.15, "name": "lh:computed:UnusedJavascriptSummary", "duration": 9.76, "entryType": "measure"}, {"startTime": 33919.06, "name": "lh:computed:UnusedJavascriptSummary", "duration": 7.23, "entryType": "measure"}, {"startTime": 33926.44, "name": "lh:computed:UnusedJavascriptSummary", "duration": 0.21, "entryType": "measure"}, {"startTime": 33926.75, "name": "lh:computed:UnusedJavascriptSummary", "duration": 0.69, "entryType": "measure"}, {"startTime": 33928.06, "name": "lh:audit:accesskeys", "duration": 1.91, "entryType": "measure"}, {"startTime": 33930.32, "name": "lh:audit:aria-allowed-attr", "duration": 5.31, "entryType": "measure"}, {"startTime": 33935.88, "name": "lh:audit:aria-allowed-role", "duration": 1.5, "entryType": "measure"}, {"startTime": 33937.79, "name": "lh:audit:aria-command-name", "duration": 1.26, "entryType": "measure"}, {"startTime": 33939.31, "name": "lh:audit:aria-conditional-attr", "duration": 5.54, "entryType": "measure"}, {"startTime": 33945.24, "name": "lh:audit:aria-deprecated-role", "duration": 1.84, "entryType": "measure"}, {"startTime": 33947.38, "name": "lh:audit:aria-dialog-name", "duration": 2.12, "entryType": "measure"}, {"startTime": 33949.94, "name": "lh:audit:aria-hidden-body", "duration": 7.02, "entryType": "measure"}, {"startTime": 33968.1, "name": "lh:audit:aria-hidden-focus", "duration": 7.21, "entryType": "measure"}, {"startTime": 33975.87, "name": "lh:audit:aria-input-field-name", "duration": 2.41, "entryType": "measure"}, {"startTime": 33978.77, "name": "lh:audit:aria-meter-name", "duration": 2.2, "entryType": "measure"}, {"startTime": 33981.54, "name": "lh:audit:aria-progressbar-name", "duration": 4.03, "entryType": "measure"}, {"startTime": 33985.85, "name": "lh:audit:aria-prohibited-attr", "duration": 6.3, "entryType": "measure"}, {"startTime": 33992.51, "name": "lh:audit:aria-required-attr", "duration": 1.59, "entryType": "measure"}, {"startTime": 33994.37, "name": "lh:audit:aria-required-children", "duration": 2.47, "entryType": "measure"}, {"startTime": 33997.26, "name": "lh:audit:aria-required-parent", "duration": 2.51, "entryType": "measure"}, {"startTime": 34000.08, "name": "lh:audit:aria-roles", "duration": 2.7, "entryType": "measure"}, {"startTime": 34003.26, "name": "lh:audit:aria-text", "duration": 1.62, "entryType": "measure"}, {"startTime": 34005.08, "name": "lh:audit:aria-toggle-field-name", "duration": 2.41, "entryType": "measure"}, {"startTime": 34007.84, "name": "lh:audit:aria-tooltip-name", "duration": 1.84, "entryType": "measure"}, {"startTime": 34010.05, "name": "lh:audit:aria-treeitem-name", "duration": 3.08, "entryType": "measure"}, {"startTime": 34013.47, "name": "lh:audit:aria-valid-attr-value", "duration": 12.88, "entryType": "measure"}, {"startTime": 34026.73, "name": "lh:audit:aria-valid-attr", "duration": 6.2, "entryType": "measure"}, {"startTime": 34033.4, "name": "lh:audit:button-name", "duration": 4.25, "entryType": "measure"}, {"startTime": 34038.09, "name": "lh:audit:bypass", "duration": 2.99, "entryType": "measure"}, {"startTime": 34041.57, "name": "lh:audit:color-contrast", "duration": 6.77, "entryType": "measure"}, {"startTime": 34048.91, "name": "lh:audit:definition-list", "duration": 3.84, "entryType": "measure"}, {"startTime": 34053.12, "name": "lh:audit:dlitem", "duration": 2.13, "entryType": "measure"}, {"startTime": 34055.62, "name": "lh:audit:document-title", "duration": 17.08, "entryType": "measure"}, {"startTime": 34073.04, "name": "lh:audit:duplicate-id-aria", "duration": 7.53, "entryType": "measure"}, {"startTime": 34081.07, "name": "lh:audit:empty-heading", "duration": 9.82, "entryType": "measure"}, {"startTime": 34091.83, "name": "lh:audit:form-field-multiple-labels", "duration": 17.63, "entryType": "measure"}, {"startTime": 34109.86, "name": "lh:audit:frame-title", "duration": 3.82, "entryType": "measure"}, {"startTime": 34114.14, "name": "lh:audit:heading-order", "duration": 7.65, "entryType": "measure"}, {"startTime": 34122.31, "name": "lh:audit:html-has-lang", "duration": 5.54, "entryType": "measure"}, {"startTime": 34128.16, "name": "lh:audit:html-lang-valid", "duration": 5.29, "entryType": "measure"}, {"startTime": 34134.1, "name": "lh:audit:html-xml-lang-mismatch", "duration": 4.38, "entryType": "measure"}, {"startTime": 34138.89, "name": "lh:audit:identical-links-same-purpose", "duration": 1.98, "entryType": "measure"}, {"startTime": 34141.12, "name": "lh:audit:image-alt", "duration": 2.13, "entryType": "measure"}, {"startTime": 34143.48, "name": "lh:audit:image-redundant-alt", "duration": 3.24, "entryType": "measure"}, {"startTime": 34147.01, "name": "lh:audit:input-button-name", "duration": 3.29, "entryType": "measure"}, {"startTime": 34150.65, "name": "lh:audit:input-image-alt", "duration": 2.82, "entryType": "measure"}, {"startTime": 34153.89, "name": "lh:audit:label-content-name-mismatch", "duration": 3.85, "entryType": "measure"}, {"startTime": 34158.16, "name": "lh:audit:label", "duration": 12.79, "entryType": "measure"}, {"startTime": 34171.19, "name": "lh:audit:landmark-one-main", "duration": 3.08, "entryType": "measure"}, {"startTime": 34174.49, "name": "lh:audit:link-name", "duration": 2.33, "entryType": "measure"}, {"startTime": 34177.01, "name": "lh:audit:link-in-text-block", "duration": 3.38, "entryType": "measure"}, {"startTime": 34180.71, "name": "lh:audit:list", "duration": 4.18, "entryType": "measure"}, {"startTime": 34185.29, "name": "lh:audit:listitem", "duration": 2.26, "entryType": "measure"}, {"startTime": 34187.77, "name": "lh:audit:meta-refresh", "duration": 11.29, "entryType": "measure"}, {"startTime": 34199.72, "name": "lh:audit:meta-viewport", "duration": 6.61, "entryType": "measure"}, {"startTime": 34206.7, "name": "lh:audit:object-alt", "duration": 3.39, "entryType": "measure"}, {"startTime": 34210.28, "name": "lh:audit:select-name", "duration": 2.2, "entryType": "measure"}, {"startTime": 34212.67, "name": "lh:audit:skip-link", "duration": 3.98, "entryType": "measure"}, {"startTime": 34217.15, "name": "lh:audit:tabindex", "duration": 4.1, "entryType": "measure"}, {"startTime": 34221.64, "name": "lh:audit:table-duplicate-name", "duration": 2.99, "entryType": "measure"}, {"startTime": 34224.84, "name": "lh:audit:table-fake-caption", "duration": 5.47, "entryType": "measure"}, {"startTime": 34230.74, "name": "lh:audit:target-size", "duration": 4.85, "entryType": "measure"}, {"startTime": 34243.69, "name": "lh:audit:td-has-header", "duration": 6.02, "entryType": "measure"}, {"startTime": 34250.03, "name": "lh:audit:td-headers-attr", "duration": 3.12, "entryType": "measure"}, {"startTime": 34253.37, "name": "lh:audit:th-has-data-cells", "duration": 2.94, "entryType": "measure"}, {"startTime": 34256.58, "name": "lh:audit:valid-lang", "duration": 2.93, "entryType": "measure"}, {"startTime": 34259.79, "name": "lh:audit:video-caption", "duration": 5.07, "entryType": "measure"}, {"startTime": 34264.96, "name": "lh:audit:custom-controls-labels", "duration": 0.56, "entryType": "measure"}, {"startTime": 34265.56, "name": "lh:audit:custom-controls-roles", "duration": 0.25, "entryType": "measure"}, {"startTime": 34265.87, "name": "lh:audit:focus-traps", "duration": 0.22, "entryType": "measure"}, {"startTime": 34266.13, "name": "lh:audit:focusable-controls", "duration": 0.21, "entryType": "measure"}, {"startTime": 34266.37, "name": "lh:audit:interactive-element-affordance", "duration": 0.15, "entryType": "measure"}, {"startTime": 34266.54, "name": "lh:audit:logical-tab-order", "duration": 0.14, "entryType": "measure"}, {"startTime": 34266.7, "name": "lh:audit:managed-focus", "duration": 0.13, "entryType": "measure"}, {"startTime": 34266.86, "name": "lh:audit:offscreen-content-hidden", "duration": 0.12, "entryType": "measure"}, {"startTime": 34267, "name": "lh:audit:use-landmarks", "duration": 0.12, "entryType": "measure"}, {"startTime": 34267.15, "name": "lh:audit:visual-order-follows-dom", "duration": 0.12, "entryType": "measure"}, {"startTime": 34267.84, "name": "lh:audit:uses-long-cache-ttl", "duration": 3.26, "entryType": "measure"}, {"startTime": 34271.47, "name": "lh:audit:total-byte-weight", "duration": 13.71, "entryType": "measure"}, {"startTime": 34285.57, "name": "lh:audit:offscreen-images", "duration": 5.31, "entryType": "measure"}, {"startTime": 34291.19, "name": "lh:audit:render-blocking-resources", "duration": 2.44, "entryType": "measure"}, {"startTime": 34292.03, "name": "lh:computed:UnusedCSS", "duration": 0.59, "entryType": "measure"}, {"startTime": 34292.67, "name": "lh:computed:NavigationInsights", "duration": 0.14, "entryType": "measure"}, {"startTime": 34292.9, "name": "lh:computed:First<PERSON><PERSON>ntful<PERSON><PERSON>t", "duration": 0.09, "entryType": "measure"}, {"startTime": 34293.79, "name": "lh:audit:unminified-css", "duration": 15.39, "entryType": "measure"}, {"startTime": 34309.52, "name": "lh:audit:unminified-javascript", "duration": 80.7, "entryType": "measure"}, {"startTime": 34390.56, "name": "lh:audit:unused-css-rules", "duration": 3.23, "entryType": "measure"}, {"startTime": 34394.07, "name": "lh:audit:unused-javascript", "duration": 4.96, "entryType": "measure"}, {"startTime": 34399.39, "name": "lh:audit:modern-image-formats", "duration": 4.86, "entryType": "measure"}, {"startTime": 34404.59, "name": "lh:audit:uses-optimized-images", "duration": 4.28, "entryType": "measure"}, {"startTime": 34409.16, "name": "lh:audit:uses-text-compression", "duration": 2.91, "entryType": "measure"}, {"startTime": 34412.34, "name": "lh:audit:uses-responsive-images", "duration": 5.02, "entryType": "measure"}, {"startTime": 34417.68, "name": "lh:audit:efficient-animated-content", "duration": 2.83, "entryType": "measure"}, {"startTime": 34420.67, "name": "lh:audit:duplicated-javascript", "duration": 2.6, "entryType": "measure"}, {"startTime": 34423.65, "name": "lh:audit:legacy-javascript", "duration": 116.35, "entryType": "measure"}, {"startTime": 34540.41, "name": "lh:audit:doctype", "duration": 1.38, "entryType": "measure"}, {"startTime": 34542, "name": "lh:audit:charset", "duration": 1.15, "entryType": "measure"}, {"startTime": 34543.38, "name": "lh:audit:dom-size", "duration": 2.31, "entryType": "measure"}, {"startTime": 34545.88, "name": "lh:audit:geolocation-on-start", "duration": 1.48, "entryType": "measure"}, {"startTime": 34547.69, "name": "lh:audit:inspector-issues", "duration": 1.02, "entryType": "measure"}, {"startTime": 34549.05, "name": "lh:audit:no-document-write", "duration": 1.28, "entryType": "measure"}, {"startTime": 34550.49, "name": "lh:audit:js-libraries", "duration": 0.78, "entryType": "measure"}, {"startTime": 34551.46, "name": "lh:audit:notification-on-start", "duration": 1.3, "entryType": "measure"}, {"startTime": 34553.14, "name": "lh:audit:paste-preventing-inputs", "duration": 0.96, "entryType": "measure"}, {"startTime": 34554.26, "name": "lh:audit:uses-http2", "duration": 2.52, "entryType": "measure"}, {"startTime": 34556.98, "name": "lh:audit:uses-passive-event-listeners", "duration": 1.46, "entryType": "measure"}, {"startTime": 34558.66, "name": "lh:audit:meta-description", "duration": 0.93, "entryType": "measure"}, {"startTime": 34559.83, "name": "lh:audit:http-status-code", "duration": 0.91, "entryType": "measure"}, {"startTime": 34560.96, "name": "lh:audit:font-size", "duration": 1.41, "entryType": "measure"}, {"startTime": 34562.57, "name": "lh:audit:link-text", "duration": 0.8, "entryType": "measure"}, {"startTime": 34563.54, "name": "lh:audit:crawlable-anchors", "duration": 5.41, "entryType": "measure"}, {"startTime": 34569.2, "name": "lh:audit:is-crawlable", "duration": 2.78, "entryType": "measure"}, {"startTime": 34572.17, "name": "lh:audit:robots-txt", "duration": 2.03, "entryType": "measure"}, {"startTime": 34574.57, "name": "lh:audit:hreflang", "duration": 1.55, "entryType": "measure"}, {"startTime": 34576.46, "name": "lh:audit:canonical", "duration": 1.1, "entryType": "measure"}, {"startTime": 34577.73, "name": "lh:audit:structured-data", "duration": 0.78, "entryType": "measure"}, {"startTime": 34579.18, "name": "lh:audit:bf-cache", "duration": 1.16, "entryType": "measure"}, {"startTime": 34580.72, "name": "lh:audit:cache-insight", "duration": 2.86, "entryType": "measure"}, {"startTime": 34584, "name": "lh:audit:cls-culprits-insight", "duration": 2.07, "entryType": "measure"}, {"startTime": 34586.34, "name": "lh:audit:document-latency-insight", "duration": 1.61, "entryType": "measure"}, {"startTime": 34588.29, "name": "lh:audit:dom-size-insight", "duration": 2.19, "entryType": "measure"}, {"startTime": 34590.77, "name": "lh:audit:duplicated-javascript-insight", "duration": 1.66, "entryType": "measure"}, {"startTime": 34592.75, "name": "lh:audit:font-display-insight", "duration": 1.42, "entryType": "measure"}, {"startTime": 34594.53, "name": "lh:audit:forced-reflow-insight", "duration": 1.21, "entryType": "measure"}, {"startTime": 34595.93, "name": "lh:audit:image-delivery-insight", "duration": 1.35, "entryType": "measure"}, {"startTime": 34597.59, "name": "lh:audit:inp-breakdown-insight", "duration": 1.62, "entryType": "measure"}, {"startTime": 34599.74, "name": "lh:audit:lcp-breakdown-insight", "duration": 3.26, "entryType": "measure"}, {"startTime": 34603.35, "name": "lh:audit:lcp-discovery-insight", "duration": 1.43, "entryType": "measure"}, {"startTime": 34605.14, "name": "lh:audit:legacy-javascript-insight", "duration": 1.94, "entryType": "measure"}, {"startTime": 34607.45, "name": "lh:audit:modern-http-insight", "duration": 1.18, "entryType": "measure"}, {"startTime": 34608.84, "name": "lh:audit:network-dependency-tree-insight", "duration": 1.96, "entryType": "measure"}, {"startTime": 34611.12, "name": "lh:audit:render-blocking-insight", "duration": 1.67, "entryType": "measure"}, {"startTime": 34613.06, "name": "lh:audit:third-parties-insight", "duration": 10.4, "entryType": "measure"}, {"startTime": 34623.86, "name": "lh:audit:viewport-insight", "duration": 1.62, "entryType": "measure"}, {"startTime": 34625.53, "name": "lh:runner:generate", "duration": 1.05, "entryType": "measure"}], "total": 11867.31}, "i18n": {"rendererFormattedStrings": {"calculatorLink": "See calculator.", "collapseView": "Collapse view", "crcInitialNavigation": "Initial Navigation", "crcLongestDurationLabel": "Maximum critical path latency:", "dropdownCopyJSON": "Copy JSON", "dropdownDarkTheme": "Toggle Dark Theme", "dropdownPrintExpanded": "Print Expanded", "dropdownPrintSummary": "Print Summary", "dropdownSaveGist": "Save as Gist", "dropdownSaveHTML": "Save as HTML", "dropdownSaveJSON": "Save as JSON", "dropdownViewUnthrottledTrace": "View Unthrottled Trace", "dropdownViewer": "Open in Viewer", "errorLabel": "Error!", "errorMissingAuditInfo": "Report error: no audit information", "expandView": "Expand view", "firstPartyChipLabel": "1st party", "footerIssue": "File an issue", "goBackToAudits": "Go back to audits", "hide": "<PERSON>de", "insightsNotice": "Later this year, insights will replace performance audits. [Learn more and provide feedback here](https://github.com/GoogleChrome/lighthouse/discussions/16462).", "labDataTitle": "Lab Data", "lsPerformanceCategoryDescription": "[Lighthouse](https://developers.google.com/web/tools/lighthouse/) analysis of the current page on an emulated mobile network. Values are estimated and may vary.", "manualAuditsGroupTitle": "Additional items to manually check", "notApplicableAuditsGroupTitle": "Not applicable", "openInANewTabTooltip": "Open in a new tab", "opportunityResourceColumnLabel": "Opportunity", "opportunitySavingsColumnLabel": "Estimated Savings", "passedAuditsGroupTitle": "Passed audits", "runtimeAnalysisWindow": "Initial page load", "runtimeAnalysisWindowSnapshot": "Point-in-time snapshot", "runtimeAnalysisWindowTimespan": "User interactions timespan", "runtimeCustom": "Custom throttling", "runtimeDesktopEmulation": "Emulated Desktop", "runtimeMobileEmulation": "Emulated Moto G Power", "runtimeNoEmulation": "No emulation", "runtimeSettingsAxeVersion": "Axe version", "runtimeSettingsBenchmark": "Unthrottled CPU/Memory Power", "runtimeSettingsCPUThrottling": "CPU throttling", "runtimeSettingsDevice": "<PERSON><PERSON>", "runtimeSettingsNetworkThrottling": "Network throttling", "runtimeSettingsScreenEmulation": "Screen emulation", "runtimeSettingsUANetwork": "User agent (network)", "runtimeSingleLoad": "Single page session", "runtimeSingleLoadTooltip": "This data is taken from a single page session, as opposed to field data summarizing many sessions.", "runtimeSlow4g": "Slow 4G throttling", "runtimeUnknown": "Unknown", "show": "Show", "showRelevantAudits": "Show audits relevant to:", "snippetCollapseButtonLabel": "Collapse snippet", "snippetExpandButtonLabel": "Expand snippet", "thirdPartyResourcesLabel": "Show 3rd-party resources", "throttlingProvided": "Provided by environment", "toplevelWarningsMessage": "There were issues affecting this run of Lighthouse:", "tryInsights": "Try insights", "unattributable": "Unattributable", "varianceDisclaimer": "Values are estimated and may vary. The [performance score is calculated](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) directly from these metrics.", "viewTraceLabel": "View Trace", "viewTreemapLabel": "View Treemap", "warningAuditsGroupTitle": "Passed audits but with warnings", "warningHeader": "Warnings: "}, "icuMessagePaths": {"core/audits/is-on-https.js | title": ["audits[is-on-https].title"], "core/audits/is-on-https.js | description": ["audits[is-on-https].description"], "core/audits/is-on-https.js | columnInsecureURL": ["audits[is-on-https].details.headings[0].label"], "core/audits/is-on-https.js | columnResolution": ["audits[is-on-https].details.headings[1].label"], "core/audits/redirects-http.js | title": ["audits[redirects-http].title"], "core/audits/redirects-http.js | description": ["audits[redirects-http].description"], "core/audits/viewport.js | title": ["audits.viewport.title"], "core/audits/viewport.js | description": ["audits.viewport.description"], "core/lib/i18n/i18n.js | firstContentfulPaintMetric": ["audits[first-contentful-paint].title"], "core/audits/metrics/first-contentful-paint.js | description": ["audits[first-contentful-paint].description"], "core/lib/i18n/i18n.js | seconds": [{"values": {"timeInMs": 1082.559}, "path": "audits[first-contentful-paint].displayValue"}, {"values": {"timeInMs": 4680.45975}, "path": "audits[largest-contentful-paint].displayValue"}, {"values": {"timeInMs": 6600.253369201369}, "path": "audits[speed-index].displayValue"}, {"values": {"timeInMs": 6185.************}, "path": "audits.interactive.displayValue"}, {"values": {"timeInMs": 898.6600000000009}, "path": "audits[mainthread-work-breakdown].displayValue"}, {"values": {"timeInMs": 285.67200000000014}, "path": "audits[bootup-time].displayValue"}], "core/lib/i18n/i18n.js | largestContentfulPaintMetric": ["audits[largest-contentful-paint].title"], "core/audits/metrics/largest-contentful-paint.js | description": ["audits[largest-contentful-paint].description"], "core/lib/i18n/i18n.js | firstMeaningfulPaintMetric": ["audits[first-meaningful-paint].title"], "core/audits/metrics/first-meaningful-paint.js | description": ["audits[first-meaningful-paint].description"], "core/lib/i18n/i18n.js | speedIndexMetric": ["audits[speed-index].title"], "core/audits/metrics/speed-index.js | description": ["audits[speed-index].description"], "core/lib/i18n/i18n.js | totalBlockingTimeMetric": ["audits[total-blocking-time].title"], "core/audits/metrics/total-blocking-time.js | description": ["audits[total-blocking-time].description"], "core/lib/i18n/i18n.js | ms": [{"values": {"timeInMs": 72.5}, "path": "audits[total-blocking-time].displayValue"}, {"values": {"timeInMs": 87}, "path": "audits[max-potential-fid].displayValue"}, {"values": {"timeInMs": 202.90800000000002}, "path": "audits[network-rtt].displayValue"}, {"values": {"timeInMs": 537.1755}, "path": "audits[network-server-latency].displayValue"}, {"values": {"timeInMs": 4680.45975}, "path": "audits[largest-contentful-paint-element].displayValue"}], "core/lib/i18n/i18n.js | maxPotentialFIDMetric": ["audits[max-potential-fid].title"], "core/audits/metrics/max-potential-fid.js | description": ["audits[max-potential-fid].description"], "core/lib/i18n/i18n.js | cumulativeLayoutShiftMetric": ["audits[cumulative-layout-shift].title"], "core/audits/metrics/cumulative-layout-shift.js | description": ["audits[cumulative-layout-shift].description"], "core/audits/errors-in-console.js | title": ["audits[errors-in-console].title"], "core/audits/errors-in-console.js | description": ["audits[errors-in-console].description"], "core/lib/i18n/i18n.js | columnSource": ["audits[errors-in-console].details.headings[0].label", "audits.deprecations.details.headings[1].label", "audits[geolocation-on-start].details.headings[0].label", "audits[no-document-write].details.headings[0].label", "audits[notification-on-start].details.headings[0].label", "audits[uses-passive-event-listeners].details.headings[0].label", "audits[font-size].details.headings[0].label", "audits[forced-reflow-insight].details.items[0].headings[0].label"], "core/lib/i18n/i18n.js | columnDescription": ["audits[errors-in-console].details.headings[1].label", "audits[csp-xss].details.headings[0].label", "audits[has-hsts].details.headings[0].label", "audits[origin-isolation].details.headings[0].label", "audits[clickjacking-mitigation].details.headings[0].label", "audits[trusted-types-xss].details.headings[0].label"], "core/audits/server-response-time.js | title": ["audits[server-response-time].title"], "core/audits/server-response-time.js | description": ["audits[server-response-time].description"], "core/audits/server-response-time.js | displayValue": [{"values": {"timeInMs": 171.41099999999994}, "path": "audits[server-response-time].displayValue"}], "core/lib/i18n/i18n.js | columnURL": ["audits[server-response-time].details.headings[0].label", "audits[image-aspect-ratio].details.headings[1].label", "audits[image-size-responsive].details.headings[1].label", "audits[third-party-cookies].details.headings[1].label", "audits[bootup-time].details.headings[0].label", "audits[uses-rel-preconnect].details.headings[0].label", "audits[font-display].details.headings[0].label", "audits[network-rtt].details.headings[0].label", "audits[network-server-latency].details.headings[0].label", "audits[long-tasks].details.headings[0].label", "audits[unsized-images].details.headings[1].label", "audits[valid-source-maps].details.headings[0].label", "audits[uses-long-cache-ttl].details.headings[0].label", "audits[total-byte-weight].details.headings[0].label", "audits[unminified-javascript].details.headings[0].label", "audits[unused-javascript].details.headings[0].label", "audits[font-display-insight].details.headings[0].label", "audits[legacy-javascript-insight].details.headings[0].label", "audits[modern-http-insight].details.headings[0].label", "audits[render-blocking-insight].details.headings[0].label"], "core/lib/i18n/i18n.js | columnTimeSpent": ["audits[server-response-time].details.headings[1].label", "audits[mainthread-work-breakdown].details.headings[1].label", "audits[network-rtt].details.headings[1].label", "audits[network-server-latency].details.headings[1].label"], "core/lib/i18n/i18n.js | interactiveMetric": ["audits.interactive.title"], "core/audits/metrics/interactive.js | description": ["audits.interactive.description"], "core/audits/user-timings.js | title": ["audits[user-timings].title"], "core/audits/user-timings.js | description": ["audits[user-timings].description"], "core/lib/i18n/i18n.js | columnName": ["audits[user-timings].details.headings[0].label", "audits[third-party-cookies].details.headings[0].label"], "core/audits/user-timings.js | columnType": ["audits[user-timings].details.headings[1].label"], "core/lib/i18n/i18n.js | columnStartTime": ["audits[user-timings].details.headings[2].label", "audits[long-tasks].details.headings[1].label"], "core/lib/i18n/i18n.js | columnDuration": ["audits[user-timings].details.headings[3].label", "audits[long-tasks].details.headings[2].label", "audits[lcp-breakdown-insight].details.items[0].headings[1].label", "audits[render-blocking-insight].details.headings[2].label"], "core/audits/critical-request-chains.js | title": ["audits[critical-request-chains].title"], "core/audits/critical-request-chains.js | description": ["audits[critical-request-chains].description"], "core/audits/critical-request-chains.js | displayValue": [{"values": {"itemCount": 3}, "path": "audits[critical-request-chains].displayValue"}], "core/audits/redirects.js | title": ["audits.redirects.title"], "core/audits/redirects.js | description": ["audits.redirects.description"], "core/audits/image-aspect-ratio.js | title": ["audits[image-aspect-ratio].title"], "core/audits/image-aspect-ratio.js | description": ["audits[image-aspect-ratio].description"], "core/audits/image-aspect-ratio.js | columnDisplayed": ["audits[image-aspect-ratio].details.headings[2].label"], "core/audits/image-aspect-ratio.js | columnActual": ["audits[image-aspect-ratio].details.headings[3].label"], "core/audits/image-size-responsive.js | title": ["audits[image-size-responsive].title"], "core/audits/image-size-responsive.js | description": ["audits[image-size-responsive].description"], "core/audits/image-size-responsive.js | columnDisplayed": ["audits[image-size-responsive].details.headings[2].label"], "core/audits/image-size-responsive.js | columnActual": ["audits[image-size-responsive].details.headings[3].label"], "core/audits/image-size-responsive.js | columnExpected": ["audits[image-size-responsive].details.headings[4].label"], "core/audits/deprecations.js | title": ["audits.deprecations.title"], "core/audits/deprecations.js | description": ["audits.deprecations.description"], "core/audits/deprecations.js | columnDeprecate": ["audits.deprecations.details.headings[0].label"], "core/audits/third-party-cookies.js | title": ["audits[third-party-cookies].title"], "core/audits/third-party-cookies.js | description": ["audits[third-party-cookies].description"], "core/audits/mainthread-work-breakdown.js | title": ["audits[mainthread-work-breakdown].title"], "core/audits/mainthread-work-breakdown.js | description": ["audits[mainthread-work-breakdown].description"], "core/audits/mainthread-work-breakdown.js | columnCategory": ["audits[mainthread-work-breakdown].details.headings[0].label"], "core/audits/bootup-time.js | title": ["audits[bootup-time].title"], "core/audits/bootup-time.js | description": ["audits[bootup-time].description"], "core/audits/bootup-time.js | columnTotal": ["audits[bootup-time].details.headings[1].label"], "core/audits/bootup-time.js | columnScriptEval": ["audits[bootup-time].details.headings[2].label"], "core/audits/bootup-time.js | columnScriptParse": ["audits[bootup-time].details.headings[3].label"], "core/audits/uses-rel-preconnect.js | title": ["audits[uses-rel-preconnect].title"], "core/audits/uses-rel-preconnect.js | description": ["audits[uses-rel-preconnect].description"], "core/lib/i18n/i18n.js | displayValueMsSavings": [{"values": {"wastedMs": 705.816}, "path": "audits[uses-rel-preconnect].displayValue"}], "core/lib/i18n/i18n.js | columnWastedBytes": ["audits[uses-rel-preconnect].details.headings[1].label", "audits[font-display].details.headings[1].label", "audits[unminified-javascript].details.headings[2].label", "audits[unused-javascript].details.headings[2].label", "audits[font-display-insight].details.headings[1].label"], "core/audits/font-display.js | title": ["audits[font-display].title"], "core/audits/font-display.js | description": ["audits[font-display].description"], "core/audits/network-rtt.js | title": ["audits[network-rtt].title"], "core/audits/network-rtt.js | description": ["audits[network-rtt].description"], "core/audits/network-server-latency.js | title": ["audits[network-server-latency].title"], "core/audits/network-server-latency.js | description": ["audits[network-server-latency].description"], "core/lib/i18n/i18n.js | columnResourceType": ["audits[resource-summary].details.headings[0].label"], "core/lib/i18n/i18n.js | columnRequests": ["audits[resource-summary].details.headings[1].label"], "core/lib/i18n/i18n.js | columnTransferSize": ["audits[resource-summary].details.headings[2].label", "audits[third-party-summary].details.headings[1].label", "audits[uses-long-cache-ttl].details.headings[2].label", "audits[total-byte-weight].details.headings[1].label", "audits[unminified-javascript].details.headings[1].label", "audits[unused-javascript].details.headings[1].label", "audits[cache-insight].details.headings[2].label", "audits[render-blocking-insight].details.headings[1].label"], "core/lib/i18n/i18n.js | total": ["audits[resource-summary].details.items[0].label"], "core/lib/i18n/i18n.js | scriptResourceType": ["audits[resource-summary].details.items[1].label"], "core/lib/i18n/i18n.js | stylesheetResourceType": ["audits[resource-summary].details.items[2].label"], "core/lib/i18n/i18n.js | documentResourceType": ["audits[resource-summary].details.items[3].label"], "core/lib/i18n/i18n.js | otherResourceType": ["audits[resource-summary].details.items[4].label"], "core/lib/i18n/i18n.js | imageResourceType": ["audits[resource-summary].details.items[5].label"], "core/lib/i18n/i18n.js | mediaResourceType": ["audits[resource-summary].details.items[6].label"], "core/lib/i18n/i18n.js | fontResourceType": ["audits[resource-summary].details.items[7].label"], "core/lib/i18n/i18n.js | thirdPartyResourceType": ["audits[resource-summary].details.items[8].label"], "core/audits/third-party-summary.js | title": ["audits[third-party-summary].title"], "core/audits/third-party-summary.js | description": ["audits[third-party-summary].description"], "core/audits/third-party-summary.js | displayValue": [{"values": {"timeInMs": 7.11449332285937}, "path": "audits[third-party-summary].displayValue"}], "core/audits/third-party-summary.js | columnThirdParty": ["audits[third-party-summary].details.headings[0].label"], "core/lib/i18n/i18n.js | columnBlockingTime": ["audits[third-party-summary].details.headings[2].label"], "core/audits/third-party-facades.js | title": ["audits[third-party-facades].title"], "core/audits/third-party-facades.js | description": ["audits[third-party-facades].description"], "core/audits/largest-contentful-paint-element.js | title": ["audits[largest-contentful-paint-element].title"], "core/audits/largest-contentful-paint-element.js | description": ["audits[largest-contentful-paint-element].description"], "core/lib/i18n/i18n.js | columnElement": ["audits[largest-contentful-paint-element].details.items[0].headings[0].label", "audits[layout-shifts].details.headings[0].label", "audits[non-composited-animations].details.headings[0].label", "audits[dom-size].details.headings[1].label", "audits[dom-size-insight].details.headings[1].label"], "core/audits/largest-contentful-paint-element.js | columnPhase": ["audits[largest-contentful-paint-element].details.items[1].headings[0].label"], "core/audits/largest-contentful-paint-element.js | columnPercentOfLCP": ["audits[largest-contentful-paint-element].details.items[1].headings[1].label"], "core/audits/largest-contentful-paint-element.js | columnTiming": ["audits[largest-contentful-paint-element].details.items[1].headings[2].label"], "core/audits/largest-contentful-paint-element.js | itemTTFB": ["audits[largest-contentful-paint-element].details.items[1].items[0].phase"], "core/audits/largest-contentful-paint-element.js | itemLoadDelay": ["audits[largest-contentful-paint-element].details.items[1].items[1].phase"], "core/audits/largest-contentful-paint-element.js | itemLoadTime": ["audits[largest-contentful-paint-element].details.items[1].items[2].phase"], "core/audits/largest-contentful-paint-element.js | itemRenderDelay": ["audits[largest-contentful-paint-element].details.items[1].items[3].phase"], "core/audits/lcp-lazy-loaded.js | title": ["audits[lcp-lazy-loaded].title"], "core/audits/lcp-lazy-loaded.js | description": ["audits[lcp-lazy-loaded].description"], "core/audits/layout-shifts.js | title": ["audits[layout-shifts].title"], "core/audits/layout-shifts.js | description": ["audits[layout-shifts].description"], "core/audits/layout-shifts.js | columnScore": ["audits[layout-shifts].details.headings[1].label"], "core/audits/long-tasks.js | title": ["audits[long-tasks].title"], "core/audits/long-tasks.js | description": ["audits[long-tasks].description"], "core/audits/long-tasks.js | displayValue": [{"values": {"itemCount": 5}, "path": "audits[long-tasks].displayValue"}], "core/audits/non-composited-animations.js | title": ["audits[non-composited-animations].title"], "core/audits/non-composited-animations.js | description": ["audits[non-composited-animations].description"], "core/audits/unsized-images.js | title": ["audits[unsized-images].title"], "core/audits/unsized-images.js | description": ["audits[unsized-images].description"], "core/audits/valid-source-maps.js | failureTitle": ["audits[valid-source-maps].title"], "core/audits/valid-source-maps.js | description": ["audits[valid-source-maps].description"], "core/audits/valid-source-maps.js | columnMapURL": ["audits[valid-source-maps].details.headings[1].label"], "core/audits/valid-source-maps.js | missingSourceMapErrorMessage": ["audits[valid-source-maps].details.items[0].subItems.items[0].error"], "core/audits/prioritize-lcp-image.js | title": ["audits[prioritize-lcp-image].title"], "core/audits/prioritize-lcp-image.js | description": ["audits[prioritize-lcp-image].description"], "core/audits/csp-xss.js | title": ["audits[csp-xss].title"], "core/audits/csp-xss.js | description": ["audits[csp-xss].description"], "core/audits/csp-xss.js | columnDirective": ["audits[csp-xss].details.headings[1].label"], "core/audits/csp-xss.js | columnSeverity": ["audits[csp-xss].details.headings[2].label"], "core/lib/csp-evaluator.js | strictDynamic": ["audits[csp-xss].details.items[0].description"], "core/lib/i18n/i18n.js | itemSeverityHigh": ["audits[csp-xss].details.items[0].severity", "audits[csp-xss].details.items[1].severity", "audits[csp-xss].details.items[2].severity", "audits[csp-xss].details.items[3].severity", "audits[trusted-types-xss].details.items[0].severity"], "core/lib/csp-evaluator.js | unsafeInline": ["audits[csp-xss].details.items[1].description"], "core/lib/csp-evaluator.js | plainUrlScheme": [{"values": {"keyword": "data:"}, "path": "audits[csp-xss].details.items[2].description"}, {"values": {"keyword": "https:"}, "path": "audits[csp-xss].details.items[3].description"}], "core/audits/has-hsts.js | title": ["audits[has-hsts].title"], "core/audits/has-hsts.js | description": ["audits[has-hsts].description"], "core/audits/has-hsts.js | columnDirective": ["audits[has-hsts].details.headings[1].label"], "core/audits/has-hsts.js | columnSeverity": ["audits[has-hsts].details.headings[2].label"], "core/audits/origin-isolation.js | title": ["audits[origin-isolation].title"], "core/audits/origin-isolation.js | description": ["audits[origin-isolation].description"], "core/audits/origin-isolation.js | columnDirective": ["audits[origin-isolation].details.headings[1].label"], "core/audits/origin-isolation.js | columnSeverity": ["audits[origin-isolation].details.headings[2].label"], "core/audits/clickjacking-mitigation.js | title": ["audits[clickjacking-mitigation].title"], "core/audits/clickjacking-mitigation.js | description": ["audits[clickjacking-mitigation].description"], "core/audits/clickjacking-mitigation.js | columnSeverity": ["audits[clickjacking-mitigation].details.headings[1].label"], "core/audits/trusted-types-xss.js | title": ["audits[trusted-types-xss].title"], "core/audits/trusted-types-xss.js | description": ["audits[trusted-types-xss].description"], "core/audits/trusted-types-xss.js | columnSeverity": ["audits[trusted-types-xss].details.headings[1].label"], "core/audits/trusted-types-xss.js | noTrustedTypesToMitigateXss": ["audits[trusted-types-xss].details.items[0].description"], "core/audits/accessibility/accesskeys.js | title": ["audits.accesskeys.title"], "core/audits/accessibility/accesskeys.js | description": ["audits.accesskeys.description"], "core/audits/accessibility/aria-allowed-attr.js | title": ["audits[aria-allowed-attr].title"], "core/audits/accessibility/aria-allowed-attr.js | description": ["audits[aria-allowed-attr].description"], "core/lib/i18n/i18n.js | columnFailingElem": ["audits[aria-allowed-attr].details.headings[0].label", "audits[aria-conditional-attr].details.headings[0].label", "audits[aria-hidden-body].details.headings[0].label", "audits[aria-hidden-focus].details.headings[0].label", "audits[aria-prohibited-attr].details.headings[0].label", "audits[aria-valid-attr-value].details.headings[0].label", "audits[aria-valid-attr].details.headings[0].label", "audits[button-name].details.headings[0].label", "audits[color-contrast].details.headings[0].label", "audits[document-title].details.headings[0].label", "audits[heading-order].details.headings[0].label", "audits[html-has-lang].details.headings[0].label", "audits[html-lang-valid].details.headings[0].label", "audits.label.details.headings[0].label", "audits[meta-viewport].details.headings[0].label", "audits[target-size].details.headings[0].label", "audits[paste-preventing-inputs].details.headings[0].label"], "core/audits/accessibility/aria-allowed-role.js | title": ["audits[aria-allowed-role].title"], "core/audits/accessibility/aria-allowed-role.js | description": ["audits[aria-allowed-role].description"], "core/audits/accessibility/aria-command-name.js | title": ["audits[aria-command-name].title"], "core/audits/accessibility/aria-command-name.js | description": ["audits[aria-command-name].description"], "core/audits/accessibility/aria-conditional-attr.js | title": ["audits[aria-conditional-attr].title"], "core/audits/accessibility/aria-conditional-attr.js | description": ["audits[aria-conditional-attr].description"], "core/audits/accessibility/aria-deprecated-role.js | title": ["audits[aria-deprecated-role].title"], "core/audits/accessibility/aria-deprecated-role.js | description": ["audits[aria-deprecated-role].description"], "core/audits/accessibility/aria-dialog-name.js | title": ["audits[aria-dialog-name].title"], "core/audits/accessibility/aria-dialog-name.js | description": ["audits[aria-dialog-name].description"], "core/audits/accessibility/aria-hidden-body.js | title": ["audits[aria-hidden-body].title"], "core/audits/accessibility/aria-hidden-body.js | description": ["audits[aria-hidden-body].description"], "core/audits/accessibility/aria-hidden-focus.js | title": ["audits[aria-hidden-focus].title"], "core/audits/accessibility/aria-hidden-focus.js | description": ["audits[aria-hidden-focus].description"], "core/audits/accessibility/aria-input-field-name.js | title": ["audits[aria-input-field-name].title"], "core/audits/accessibility/aria-input-field-name.js | description": ["audits[aria-input-field-name].description"], "core/audits/accessibility/aria-meter-name.js | title": ["audits[aria-meter-name].title"], "core/audits/accessibility/aria-meter-name.js | description": ["audits[aria-meter-name].description"], "core/audits/accessibility/aria-progressbar-name.js | title": ["audits[aria-progressbar-name].title"], "core/audits/accessibility/aria-progressbar-name.js | description": ["audits[aria-progressbar-name].description"], "core/audits/accessibility/aria-prohibited-attr.js | title": ["audits[aria-prohibited-attr].title"], "core/audits/accessibility/aria-prohibited-attr.js | description": ["audits[aria-prohibited-attr].description"], "core/audits/accessibility/aria-required-attr.js | title": ["audits[aria-required-attr].title"], "core/audits/accessibility/aria-required-attr.js | description": ["audits[aria-required-attr].description"], "core/audits/accessibility/aria-required-children.js | title": ["audits[aria-required-children].title"], "core/audits/accessibility/aria-required-children.js | description": ["audits[aria-required-children].description"], "core/audits/accessibility/aria-required-parent.js | title": ["audits[aria-required-parent].title"], "core/audits/accessibility/aria-required-parent.js | description": ["audits[aria-required-parent].description"], "core/audits/accessibility/aria-roles.js | title": ["audits[aria-roles].title"], "core/audits/accessibility/aria-roles.js | description": ["audits[aria-roles].description"], "core/audits/accessibility/aria-text.js | title": ["audits[aria-text].title"], "core/audits/accessibility/aria-text.js | description": ["audits[aria-text].description"], "core/audits/accessibility/aria-toggle-field-name.js | title": ["audits[aria-toggle-field-name].title"], "core/audits/accessibility/aria-toggle-field-name.js | description": ["audits[aria-toggle-field-name].description"], "core/audits/accessibility/aria-tooltip-name.js | title": ["audits[aria-tooltip-name].title"], "core/audits/accessibility/aria-tooltip-name.js | description": ["audits[aria-tooltip-name].description"], "core/audits/accessibility/aria-treeitem-name.js | title": ["audits[aria-treeitem-name].title"], "core/audits/accessibility/aria-treeitem-name.js | description": ["audits[aria-treeitem-name].description"], "core/audits/accessibility/aria-valid-attr-value.js | title": ["audits[aria-valid-attr-value].title"], "core/audits/accessibility/aria-valid-attr-value.js | description": ["audits[aria-valid-attr-value].description"], "core/audits/accessibility/aria-valid-attr.js | title": ["audits[aria-valid-attr].title"], "core/audits/accessibility/aria-valid-attr.js | description": ["audits[aria-valid-attr].description"], "core/audits/accessibility/button-name.js | failureTitle": ["audits[button-name].title"], "core/audits/accessibility/button-name.js | description": ["audits[button-name].description"], "core/audits/accessibility/bypass.js | title": ["audits.bypass.title"], "core/audits/accessibility/bypass.js | description": ["audits.bypass.description"], "core/audits/accessibility/color-contrast.js | title": ["audits[color-contrast].title"], "core/audits/accessibility/color-contrast.js | description": ["audits[color-contrast].description"], "core/audits/accessibility/definition-list.js | title": ["audits[definition-list].title"], "core/audits/accessibility/definition-list.js | description": ["audits[definition-list].description"], "core/audits/accessibility/dlitem.js | title": ["audits.dlitem.title"], "core/audits/accessibility/dlitem.js | description": ["audits.dlitem.description"], "core/audits/accessibility/document-title.js | title": ["audits[document-title].title"], "core/audits/accessibility/document-title.js | description": ["audits[document-title].description"], "core/audits/accessibility/duplicate-id-aria.js | title": ["audits[duplicate-id-aria].title"], "core/audits/accessibility/duplicate-id-aria.js | description": ["audits[duplicate-id-aria].description"], "core/audits/accessibility/empty-heading.js | title": ["audits[empty-heading].title"], "core/audits/accessibility/empty-heading.js | description": ["audits[empty-heading].description"], "core/audits/accessibility/form-field-multiple-labels.js | title": ["audits[form-field-multiple-labels].title"], "core/audits/accessibility/form-field-multiple-labels.js | description": ["audits[form-field-multiple-labels].description"], "core/audits/accessibility/frame-title.js | title": ["audits[frame-title].title"], "core/audits/accessibility/frame-title.js | description": ["audits[frame-title].description"], "core/audits/accessibility/heading-order.js | title": ["audits[heading-order].title"], "core/audits/accessibility/heading-order.js | description": ["audits[heading-order].description"], "core/audits/accessibility/html-has-lang.js | title": ["audits[html-has-lang].title"], "core/audits/accessibility/html-has-lang.js | description": ["audits[html-has-lang].description"], "core/audits/accessibility/html-lang-valid.js | title": ["audits[html-lang-valid].title"], "core/audits/accessibility/html-lang-valid.js | description": ["audits[html-lang-valid].description"], "core/audits/accessibility/html-xml-lang-mismatch.js | title": ["audits[html-xml-lang-mismatch].title"], "core/audits/accessibility/html-xml-lang-mismatch.js | description": ["audits[html-xml-lang-mismatch].description"], "core/audits/accessibility/identical-links-same-purpose.js | title": ["audits[identical-links-same-purpose].title"], "core/audits/accessibility/identical-links-same-purpose.js | description": ["audits[identical-links-same-purpose].description"], "core/audits/accessibility/image-alt.js | title": ["audits[image-alt].title"], "core/audits/accessibility/image-alt.js | description": ["audits[image-alt].description"], "core/audits/accessibility/image-redundant-alt.js | title": ["audits[image-redundant-alt].title"], "core/audits/accessibility/image-redundant-alt.js | description": ["audits[image-redundant-alt].description"], "core/audits/accessibility/input-button-name.js | title": ["audits[input-button-name].title"], "core/audits/accessibility/input-button-name.js | description": ["audits[input-button-name].description"], "core/audits/accessibility/input-image-alt.js | title": ["audits[input-image-alt].title"], "core/audits/accessibility/input-image-alt.js | description": ["audits[input-image-alt].description"], "core/audits/accessibility/label-content-name-mismatch.js | title": ["audits[label-content-name-mismatch].title"], "core/audits/accessibility/label-content-name-mismatch.js | description": ["audits[label-content-name-mismatch].description"], "core/audits/accessibility/label.js | title": ["audits.label.title"], "core/audits/accessibility/label.js | description": ["audits.label.description"], "core/audits/accessibility/landmark-one-main.js | title": ["audits[landmark-one-main].title"], "core/audits/accessibility/landmark-one-main.js | description": ["audits[landmark-one-main].description"], "core/audits/accessibility/link-name.js | title": ["audits[link-name].title"], "core/audits/accessibility/link-name.js | description": ["audits[link-name].description"], "core/audits/accessibility/link-in-text-block.js | title": ["audits[link-in-text-block].title"], "core/audits/accessibility/link-in-text-block.js | description": ["audits[link-in-text-block].description"], "core/audits/accessibility/list.js | title": ["audits.list.title"], "core/audits/accessibility/list.js | description": ["audits.list.description"], "core/audits/accessibility/listitem.js | title": ["audits.listitem.title"], "core/audits/accessibility/listitem.js | description": ["audits.listitem.description"], "core/audits/accessibility/meta-refresh.js | title": ["audits[meta-refresh].title"], "core/audits/accessibility/meta-refresh.js | description": ["audits[meta-refresh].description"], "core/audits/accessibility/meta-viewport.js | title": ["audits[meta-viewport].title"], "core/audits/accessibility/meta-viewport.js | description": ["audits[meta-viewport].description"], "core/audits/accessibility/object-alt.js | title": ["audits[object-alt].title"], "core/audits/accessibility/object-alt.js | description": ["audits[object-alt].description"], "core/audits/accessibility/select-name.js | title": ["audits[select-name].title"], "core/audits/accessibility/select-name.js | description": ["audits[select-name].description"], "core/audits/accessibility/skip-link.js | title": ["audits[skip-link].title"], "core/audits/accessibility/skip-link.js | description": ["audits[skip-link].description"], "core/audits/accessibility/tabindex.js | title": ["audits.tabindex.title"], "core/audits/accessibility/tabindex.js | description": ["audits.tabindex.description"], "core/audits/accessibility/table-duplicate-name.js | title": ["audits[table-duplicate-name].title"], "core/audits/accessibility/table-duplicate-name.js | description": ["audits[table-duplicate-name].description"], "core/audits/accessibility/table-fake-caption.js | title": ["audits[table-fake-caption].title"], "core/audits/accessibility/table-fake-caption.js | description": ["audits[table-fake-caption].description"], "core/audits/accessibility/target-size.js | failureTitle": ["audits[target-size].title"], "core/audits/accessibility/target-size.js | description": ["audits[target-size].description"], "core/audits/accessibility/td-has-header.js | title": ["audits[td-has-header].title"], "core/audits/accessibility/td-has-header.js | description": ["audits[td-has-header].description"], "core/audits/accessibility/td-headers-attr.js | title": ["audits[td-headers-attr].title"], "core/audits/accessibility/td-headers-attr.js | description": ["audits[td-headers-attr].description"], "core/audits/accessibility/th-has-data-cells.js | title": ["audits[th-has-data-cells].title"], "core/audits/accessibility/th-has-data-cells.js | description": ["audits[th-has-data-cells].description"], "core/audits/accessibility/valid-lang.js | title": ["audits[valid-lang].title"], "core/audits/accessibility/valid-lang.js | description": ["audits[valid-lang].description"], "core/audits/accessibility/video-caption.js | title": ["audits[video-caption].title"], "core/audits/accessibility/video-caption.js | description": ["audits[video-caption].description"], "core/audits/byte-efficiency/uses-long-cache-ttl.js | failureTitle": ["audits[uses-long-cache-ttl].title"], "core/audits/byte-efficiency/uses-long-cache-ttl.js | description": ["audits[uses-long-cache-ttl].description"], "core/audits/byte-efficiency/uses-long-cache-ttl.js | displayValue": [{"values": {"itemCount": 1}, "path": "audits[uses-long-cache-ttl].displayValue"}], "core/lib/i18n/i18n.js | columnCacheTTL": ["audits[uses-long-cache-ttl].details.headings[1].label", "audits[cache-insight].details.headings[1].label"], "core/audits/byte-efficiency/total-byte-weight.js | title": ["audits[total-byte-weight].title"], "core/audits/byte-efficiency/total-byte-weight.js | description": ["audits[total-byte-weight].description"], "core/audits/byte-efficiency/total-byte-weight.js | displayValue": [{"values": {"totalBytes": 398556}, "path": "audits[total-byte-weight].displayValue"}], "core/audits/byte-efficiency/offscreen-images.js | title": ["audits[offscreen-images].title"], "core/audits/byte-efficiency/offscreen-images.js | description": ["audits[offscreen-images].description"], "core/audits/byte-efficiency/render-blocking-resources.js | title": ["audits[render-blocking-resources].title"], "core/audits/byte-efficiency/render-blocking-resources.js | description": ["audits[render-blocking-resources].description"], "core/audits/byte-efficiency/unminified-css.js | title": ["audits[unminified-css].title"], "core/audits/byte-efficiency/unminified-css.js | description": ["audits[unminified-css].description"], "core/audits/byte-efficiency/unminified-javascript.js | title": ["audits[unminified-javascript].title"], "core/audits/byte-efficiency/unminified-javascript.js | description": ["audits[unminified-javascript].description"], "core/lib/i18n/i18n.js | displayValueByteSavings": [{"values": {"wastedBytes": 39487}, "path": "audits[unminified-javascript].displayValue"}, {"values": {"wastedBytes": 155047}, "path": "audits[unused-javascript].displayValue"}, {"values": {"wastedBytes": 79506.975}, "path": "audits[cache-insight].displayValue"}], "core/audits/byte-efficiency/unused-css-rules.js | title": ["audits[unused-css-rules].title"], "core/audits/byte-efficiency/unused-css-rules.js | description": ["audits[unused-css-rules].description"], "core/audits/byte-efficiency/unused-javascript.js | title": ["audits[unused-javascript].title"], "core/audits/byte-efficiency/unused-javascript.js | description": ["audits[unused-javascript].description"], "core/audits/byte-efficiency/modern-image-formats.js | title": ["audits[modern-image-formats].title"], "core/audits/byte-efficiency/modern-image-formats.js | description": ["audits[modern-image-formats].description"], "core/audits/byte-efficiency/uses-optimized-images.js | title": ["audits[uses-optimized-images].title"], "core/audits/byte-efficiency/uses-optimized-images.js | description": ["audits[uses-optimized-images].description"], "core/audits/byte-efficiency/uses-text-compression.js | title": ["audits[uses-text-compression].title"], "core/audits/byte-efficiency/uses-text-compression.js | description": ["audits[uses-text-compression].description"], "core/audits/byte-efficiency/uses-responsive-images.js | title": ["audits[uses-responsive-images].title"], "core/audits/byte-efficiency/uses-responsive-images.js | description": ["audits[uses-responsive-images].description"], "core/audits/byte-efficiency/efficient-animated-content.js | title": ["audits[efficient-animated-content].title"], "core/audits/byte-efficiency/efficient-animated-content.js | description": ["audits[efficient-animated-content].description"], "core/audits/byte-efficiency/duplicated-javascript.js | title": ["audits[duplicated-javascript].title"], "core/audits/byte-efficiency/duplicated-javascript.js | description": ["audits[duplicated-javascript].description"], "core/audits/byte-efficiency/legacy-javascript.js | title": ["audits[legacy-javascript].title"], "core/audits/byte-efficiency/legacy-javascript.js | description": ["audits[legacy-javascript].description"], "core/audits/dobetterweb/doctype.js | title": ["audits.doctype.title"], "core/audits/dobetterweb/doctype.js | description": ["audits.doctype.description"], "core/audits/dobetterweb/charset.js | title": ["audits.charset.title"], "core/audits/dobetterweb/charset.js | description": ["audits.charset.description"], "core/audits/dobetterweb/dom-size.js | title": ["audits[dom-size].title"], "core/audits/dobetterweb/dom-size.js | description": ["audits[dom-size].description"], "core/audits/dobetterweb/dom-size.js | displayValue": [{"values": {"itemCount": 95}, "path": "audits[dom-size].displayValue"}], "core/audits/dobetterweb/dom-size.js | columnStatistic": ["audits[dom-size].details.headings[0].label"], "core/audits/dobetterweb/dom-size.js | columnValue": ["audits[dom-size].details.headings[2].label"], "core/audits/dobetterweb/dom-size.js | statisticDOMElements": ["audits[dom-size].details.items[0].statistic"], "core/audits/dobetterweb/dom-size.js | statisticDOMDepth": ["audits[dom-size].details.items[1].statistic"], "core/audits/dobetterweb/dom-size.js | statisticDOMWidth": ["audits[dom-size].details.items[2].statistic"], "core/audits/dobetterweb/geolocation-on-start.js | title": ["audits[geolocation-on-start].title"], "core/audits/dobetterweb/geolocation-on-start.js | description": ["audits[geolocation-on-start].description"], "core/audits/dobetterweb/inspector-issues.js | title": ["audits[inspector-issues].title"], "core/audits/dobetterweb/inspector-issues.js | description": ["audits[inspector-issues].description"], "core/audits/dobetterweb/inspector-issues.js | columnIssueType": ["audits[inspector-issues].details.headings[0].label"], "core/audits/dobetterweb/no-document-write.js | title": ["audits[no-document-write].title"], "core/audits/dobetterweb/no-document-write.js | description": ["audits[no-document-write].description"], "core/audits/dobetterweb/js-libraries.js | title": ["audits[js-libraries].title"], "core/audits/dobetterweb/js-libraries.js | description": ["audits[js-libraries].description"], "core/audits/dobetterweb/notification-on-start.js | title": ["audits[notification-on-start].title"], "core/audits/dobetterweb/notification-on-start.js | description": ["audits[notification-on-start].description"], "core/audits/dobetterweb/paste-preventing-inputs.js | title": ["audits[paste-preventing-inputs].title"], "core/audits/dobetterweb/paste-preventing-inputs.js | description": ["audits[paste-preventing-inputs].description"], "core/audits/dobetterweb/uses-http2.js | title": ["audits[uses-http2].title"], "core/audits/dobetterweb/uses-http2.js | description": ["audits[uses-http2].description"], "core/audits/dobetterweb/uses-passive-event-listeners.js | title": ["audits[uses-passive-event-listeners].title"], "core/audits/dobetterweb/uses-passive-event-listeners.js | description": ["audits[uses-passive-event-listeners].description"], "core/audits/seo/meta-description.js | title": ["audits[meta-description].title"], "core/audits/seo/meta-description.js | description": ["audits[meta-description].description"], "core/audits/seo/http-status-code.js | title": ["audits[http-status-code].title"], "core/audits/seo/http-status-code.js | description": ["audits[http-status-code].description"], "core/audits/seo/font-size.js | title": ["audits[font-size].title"], "core/audits/seo/font-size.js | description": ["audits[font-size].description"], "core/audits/seo/font-size.js | displayValue": [{"values": {"decimalProportion": 1}, "path": "audits[font-size].displayValue"}], "core/audits/seo/font-size.js | columnSelector": ["audits[font-size].details.headings[1].label"], "core/audits/seo/font-size.js | columnPercentPageText": ["audits[font-size].details.headings[2].label"], "core/audits/seo/font-size.js | columnFontSize": ["audits[font-size].details.headings[3].label"], "core/audits/seo/font-size.js | legibleText": ["audits[font-size].details.items[0].source.value"], "core/audits/seo/link-text.js | title": ["audits[link-text].title"], "core/audits/seo/link-text.js | description": ["audits[link-text].description"], "core/audits/seo/crawlable-anchors.js | title": ["audits[crawlable-anchors].title"], "core/audits/seo/crawlable-anchors.js | description": ["audits[crawlable-anchors].description"], "core/audits/seo/crawlable-anchors.js | columnFailingLink": ["audits[crawlable-anchors].details.headings[0].label"], "core/audits/seo/is-crawlable.js | title": ["audits[is-crawlable].title"], "core/audits/seo/is-crawlable.js | description": ["audits[is-crawlable].description"], "core/audits/seo/robots-txt.js | failureTitle": ["audits[robots-txt].title"], "core/audits/seo/robots-txt.js | description": ["audits[robots-txt].description"], "core/audits/seo/robots-txt.js | displayValueValidationError": [{"values": {"itemCount": 47}, "path": "audits[robots-txt].displayValue"}], "core/audits/seo/hreflang.js | title": ["audits.hreflang.title"], "core/audits/seo/hreflang.js | description": ["audits.hreflang.description"], "core/audits/seo/canonical.js | title": ["audits.canonical.title"], "core/audits/seo/canonical.js | description": ["audits.canonical.description"], "core/audits/seo/manual/structured-data.js | title": ["audits[structured-data].title"], "core/audits/seo/manual/structured-data.js | description": ["audits[structured-data].description"], "core/audits/bf-cache.js | title": ["audits[bf-cache].title"], "core/audits/bf-cache.js | description": ["audits[bf-cache].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/Cache.js | title": ["audits[cache-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/Cache.js | description": ["audits[cache-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/Cache.js | requestColumn": ["audits[cache-insight].details.headings[0].label"], "node_modules/@paulirish/trace_engine/models/trace/insights/CLSCulprits.js | title": ["audits[cls-culprits-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/CLSCulprits.js | description": ["audits[cls-culprits-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/DocumentLatency.js | title": ["audits[document-latency-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/DocumentLatency.js | description": ["audits[document-latency-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/DocumentLatency.js | passingRedirects": ["audits[document-latency-insight].details.items.noRedirects.label"], "node_modules/@paulirish/trace_engine/models/trace/insights/DocumentLatency.js | passingServerResponseTime": [{"values": {"PH1": "172 ms"}, "path": "audits[document-latency-insight].details.items.serverResponseIsFast.label"}], "node_modules/@paulirish/trace_engine/models/trace/insights/DocumentLatency.js | passingTextCompression": ["audits[document-latency-insight].details.items.usesCompression.label"], "node_modules/@paulirish/trace_engine/models/trace/insights/DOMSize.js | title": ["audits[dom-size-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/DOMSize.js | description": ["audits[dom-size-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/DOMSize.js | statistic": ["audits[dom-size-insight].details.headings[0].label"], "node_modules/@paulirish/trace_engine/models/trace/insights/DOMSize.js | value": ["audits[dom-size-insight].details.headings[2].label"], "node_modules/@paulirish/trace_engine/models/trace/insights/DOMSize.js | totalElements": ["audits[dom-size-insight].details.items[0].statistic"], "node_modules/@paulirish/trace_engine/models/trace/insights/DOMSize.js | maxChildren": ["audits[dom-size-insight].details.items[1].statistic"], "node_modules/@paulirish/trace_engine/models/trace/insights/DOMSize.js | maxDOMDepth": ["audits[dom-size-insight].details.items[2].statistic"], "node_modules/@paulirish/trace_engine/models/trace/insights/DuplicatedJavaScript.js | title": ["audits[duplicated-javascript-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/DuplicatedJavaScript.js | description": ["audits[duplicated-javascript-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/DuplicatedJavaScript.js | columnSource": ["audits[duplicated-javascript-insight].details.headings[0].label"], "node_modules/@paulirish/trace_engine/models/trace/insights/DuplicatedJavaScript.js | columnDuplicatedBytes": ["audits[duplicated-javascript-insight].details.headings[1].label"], "node_modules/@paulirish/trace_engine/models/trace/insights/FontDisplay.js | title": ["audits[font-display-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/FontDisplay.js | description": ["audits[font-display-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/ForcedReflow.js | title": ["audits[forced-reflow-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/ForcedReflow.js | description": ["audits[forced-reflow-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/ForcedReflow.js | totalReflowTime": ["audits[forced-reflow-insight].details.items[0].headings[1].label"], "node_modules/@paulirish/trace_engine/models/trace/insights/ImageDelivery.js | title": ["audits[image-delivery-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/ImageDelivery.js | description": ["audits[image-delivery-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/INPBreakdown.js | title": ["audits[inp-breakdown-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/INPBreakdown.js | description": ["audits[inp-breakdown-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/LCPBreakdown.js | title": ["audits[lcp-breakdown-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/LCPBreakdown.js | description": ["audits[lcp-breakdown-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/LCPBreakdown.js | subpart": ["audits[lcp-breakdown-insight].details.items[0].headings[0].label"], "node_modules/@paulirish/trace_engine/models/trace/insights/LCPBreakdown.js | timeToFirstByte": ["audits[lcp-breakdown-insight].details.items[0].items[0].label"], "node_modules/@paulirish/trace_engine/models/trace/insights/LCPBreakdown.js | elementRenderDelay": ["audits[lcp-breakdown-insight].details.items[0].items[1].label"], "node_modules/@paulirish/trace_engine/models/trace/insights/LCPDiscovery.js | title": ["audits[lcp-discovery-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/LCPDiscovery.js | description": ["audits[lcp-discovery-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/LegacyJavaScript.js | title": ["audits[legacy-javascript-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/LegacyJavaScript.js | description": ["audits[legacy-javascript-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/LegacyJavaScript.js | columnWastedBytes": ["audits[legacy-javascript-insight].details.headings[2].label"], "node_modules/@paulirish/trace_engine/models/trace/insights/ModernHTTP.js | title": ["audits[modern-http-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/ModernHTTP.js | description": ["audits[modern-http-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/ModernHTTP.js | protocol": ["audits[modern-http-insight].details.headings[1].label"], "node_modules/@paulirish/trace_engine/models/trace/insights/NetworkDependencyTree.js | title": ["audits[network-dependency-tree-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/NetworkDependencyTree.js | description": ["audits[network-dependency-tree-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/NetworkDependencyTree.js | preconnectOriginsTableTitle": ["audits[network-dependency-tree-insight].details.items[1].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/NetworkDependencyTree.js | preconnectOriginsTableDescription": ["audits[network-dependency-tree-insight].details.items[1].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/NetworkDependencyTree.js | noPreconnectOrigins": ["audits[network-dependency-tree-insight].details.items[1].value.value"], "node_modules/@paulirish/trace_engine/models/trace/insights/NetworkDependencyTree.js | estSavingTableTitle": ["audits[network-dependency-tree-insight].details.items[2].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/NetworkDependencyTree.js | estSavingTableDescription": ["audits[network-dependency-tree-insight].details.items[2].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/NetworkDependencyTree.js | columnOrigin": ["audits[network-dependency-tree-insight].details.items[2].value.headings[0].label"], "node_modules/@paulirish/trace_engine/models/trace/insights/NetworkDependencyTree.js | columnWastedMs": ["audits[network-dependency-tree-insight].details.items[2].value.headings[1].label"], "node_modules/@paulirish/trace_engine/models/trace/insights/RenderBlocking.js | title": ["audits[render-blocking-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/RenderBlocking.js | description": ["audits[render-blocking-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/ThirdParties.js | title": ["audits[third-parties-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/ThirdParties.js | description": ["audits[third-parties-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/ThirdParties.js | columnThirdParty": ["audits[third-parties-insight].details.headings[0].label"], "node_modules/@paulirish/trace_engine/models/trace/insights/ThirdParties.js | columnTransferSize": ["audits[third-parties-insight].details.headings[1].label"], "node_modules/@paulirish/trace_engine/models/trace/insights/ThirdParties.js | columnMainThreadTime": ["audits[third-parties-insight].details.headings[2].label"], "node_modules/@paulirish/trace_engine/models/trace/insights/Viewport.js | title": ["audits[viewport-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/Viewport.js | description": ["audits[viewport-insight].description"], "core/config/default-config.js | performanceCategoryTitle": ["categories.performance.title"], "core/config/default-config.js | a11yCategoryTitle": ["categories.accessibility.title"], "core/config/default-config.js | a11yCategoryDescription": ["categories.accessibility.description"], "core/config/default-config.js | a11yCategoryManualDescription": ["categories.accessibility.manualDescription"], "core/config/default-config.js | bestPracticesCategoryTitle": ["categories[best-practices].title"], "core/config/default-config.js | seoCategoryTitle": ["categories.seo.title"], "core/config/default-config.js | seoCategoryDescription": ["categories.seo.description"], "core/config/default-config.js | seoCategoryManualDescription": ["categories.seo.manualDescription"], "core/config/default-config.js | metricGroupTitle": ["categoryGroups.metrics.title"], "core/config/default-config.js | insightsGroupTitle": ["categoryGroups.insights.title"], "core/config/default-config.js | insightsGroupDescription": ["categoryGroups.insights.description"], "core/config/default-config.js | diagnosticsGroupTitle": ["categoryGroups.diagnostics.title"], "core/config/default-config.js | diagnosticsGroupDescription": ["categoryGroups.diagnostics.description"], "core/config/default-config.js | a11yBestPracticesGroupTitle": ["categoryGroups[a11y-best-practices].title"], "core/config/default-config.js | a11yBestPracticesGroupDescription": ["categoryGroups[a11y-best-practices].description"], "core/config/default-config.js | a11yColorContrastGroupTitle": ["categoryGroups[a11y-color-contrast].title"], "core/config/default-config.js | a11yColorContrastGroupDescription": ["categoryGroups[a11y-color-contrast].description"], "core/config/default-config.js | a11yNamesLabelsGroupTitle": ["categoryGroups[a11y-names-labels].title"], "core/config/default-config.js | a11yNamesLabelsGroupDescription": ["categoryGroups[a11y-names-labels].description"], "core/config/default-config.js | a11yNavigationGroupTitle": ["categoryGroups[a11y-navigation].title"], "core/config/default-config.js | a11yNavigationGroupDescription": ["categoryGroups[a11y-navigation].description"], "core/config/default-config.js | a11yAriaGroupTitle": ["categoryGroups[a11y-aria].title"], "core/config/default-config.js | a11yAriaGroupDescription": ["categoryGroups[a11y-aria].description"], "core/config/default-config.js | a11yLanguageGroupTitle": ["categoryGroups[a11y-language].title"], "core/config/default-config.js | a11yLanguageGroupDescription": ["categoryGroups[a11y-language].description"], "core/config/default-config.js | a11yAudioVideoGroupTitle": ["categoryGroups[a11y-audio-video].title"], "core/config/default-config.js | a11yAudioVideoGroupDescription": ["categoryGroups[a11y-audio-video].description"], "core/config/default-config.js | a11yTablesListsVideoGroupTitle": ["categoryGroups[a11y-tables-lists].title"], "core/config/default-config.js | a11yTablesListsVideoGroupDescription": ["categoryGroups[a11y-tables-lists].description"], "core/config/default-config.js | seoMobileGroupTitle": ["categoryGroups[seo-mobile].title"], "core/config/default-config.js | seoMobileGroupDescription": ["categoryGroups[seo-mobile].description"], "core/config/default-config.js | seoContentGroupTitle": ["categoryGroups[seo-content].title"], "core/config/default-config.js | seoContentGroupDescription": ["categoryGroups[seo-content].description"], "core/config/default-config.js | seoCrawlingGroupTitle": ["categoryGroups[seo-crawl].title"], "core/config/default-config.js | seoCrawlingGroupDescription": ["categoryGroups[seo-crawl].description"], "core/config/default-config.js | bestPracticesTrustSafetyGroupTitle": ["categoryGroups[best-practices-trust-safety].title"], "core/config/default-config.js | bestPracticesUXGroupTitle": ["categoryGroups[best-practices-ux].title"], "core/config/default-config.js | bestPracticesBrowserCompatGroupTitle": ["categoryGroups[best-practices-browser-compat].title"], "core/config/default-config.js | bestPracticesGeneralGroupTitle": ["categoryGroups[best-practices-general].title"]}}}