import React, { useState } from 'react';
import { 
  ChartBarIcon, 
  ClockIcon, 
  CurrencyDollarIcon, 
  CheckCircleIcon, 
  XCircleIcon,
  StarIcon,
  DocumentDuplicateIcon
} from '@heroicons/react/24/outline';

interface ModelExecutionResult {
  model_name: string;
  provider: string;
  response: string;
  latency: number;
  cost: number;
  token_count: number;
  quality_score?: number;
  error?: string;
}

interface ModelComparisonMetrics {
  total_models: number;
  successful_executions: number;
  failed_executions: number;
  avg_latency: number;
  total_tokens: number;
  cost_breakdown: Record<string, number>;
}

interface ModelComparisonProps {
  results: ModelExecutionResult[];
  bestModel: string;
  totalCost: number;
  executionTime: number;
  comparisonMetrics: ModelComparisonMetrics;
}

const ModelComparison: React.FC<ModelComparisonProps> = ({
  results,
  bestModel,
  totalCost,
  executionTime,
  comparisonMetrics
}) => {
  const [selectedTab, setSelectedTab] = useState<'overview' | 'responses' | 'metrics'>('overview');
  const [selectedResponse, setSelectedResponse] = useState<string | null>(null);

  const successfulResults = results.filter(r => !r.error);
  const failedResults = results.filter(r => r.error);

  const getProviderColor = (provider: string) => {
    const colors = {
      openrouter: 'bg-blue-100 text-blue-800 border-blue-200',
      openai: 'bg-green-100 text-green-800 border-green-200',
      anthropic: 'bg-purple-100 text-purple-800 border-purple-200',
      cohere: 'bg-orange-100 text-orange-800 border-orange-200'
    };
    return colors[provider as keyof typeof colors] || 'bg-gray-100 text-gray-800 border-gray-200';
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  const formatLatency = (latency: number) => {
    return latency < 1 ? `${(latency * 1000).toFixed(0)}ms` : `${latency.toFixed(2)}s`;
  };

  const formatCost = (cost: number) => {
    return cost === 0 ? 'Free' : `$${cost.toFixed(4)}`;
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200">
      {/* Header */}
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium text-gray-900">Model Comparison Results</h3>
          <div className="flex items-center space-x-4 text-sm text-gray-500">
            <span className="flex items-center space-x-1">
              <ClockIcon className="h-4 w-4" />
              <span>{formatLatency(executionTime)}</span>
            </span>
            <span className="flex items-center space-x-1">
              <CurrencyDollarIcon className="h-4 w-4" />
              <span>{formatCost(totalCost)}</span>
            </span>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8 px-6">
          {[
            { key: 'overview', label: 'Overview', icon: ChartBarIcon },
            { key: 'responses', label: 'Responses', icon: DocumentDuplicateIcon },
            { key: 'metrics', label: 'Metrics', icon: ChartBarIcon }
          ].map(({ key, label, icon: Icon }) => (
            <button
              key={key}
              onClick={() => setSelectedTab(key as any)}
              className={`py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${
                selectedTab === key
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <Icon className="h-4 w-4" />
              <span>{label}</span>
            </button>
          ))}
        </nav>
      </div>

      {/* Content */}
      <div className="p-6">
        {selectedTab === 'overview' && (
          <div className="space-y-6">
            {/* Summary Stats */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="bg-green-50 p-4 rounded-lg">
                <div className="flex items-center">
                  <CheckCircleIcon className="h-8 w-8 text-green-600" />
                  <div className="ml-3">
                    <p className="text-sm font-medium text-green-900">Successful</p>
                    <p className="text-2xl font-bold text-green-600">{successfulResults.length}</p>
                  </div>
                </div>
              </div>
              
              <div className="bg-red-50 p-4 rounded-lg">
                <div className="flex items-center">
                  <XCircleIcon className="h-8 w-8 text-red-600" />
                  <div className="ml-3">
                    <p className="text-sm font-medium text-red-900">Failed</p>
                    <p className="text-2xl font-bold text-red-600">{failedResults.length}</p>
                  </div>
                </div>
              </div>
              
              <div className="bg-blue-50 p-4 rounded-lg">
                <div className="flex items-center">
                  <ClockIcon className="h-8 w-8 text-blue-600" />
                  <div className="ml-3">
                    <p className="text-sm font-medium text-blue-900">Avg Latency</p>
                    <p className="text-2xl font-bold text-blue-600">
                      {formatLatency(comparisonMetrics.avg_latency)}
                    </p>
                  </div>
                </div>
              </div>
              
              <div className="bg-yellow-50 p-4 rounded-lg">
                <div className="flex items-center">
                  <CurrencyDollarIcon className="h-8 w-8 text-yellow-600" />
                  <div className="ml-3">
                    <p className="text-sm font-medium text-yellow-900">Total Cost</p>
                    <p className="text-2xl font-bold text-yellow-600">{formatCost(totalCost)}</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Best Model */}
            {bestModel && (
              <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-4 rounded-lg border border-blue-200">
                <div className="flex items-center space-x-2">
                  <StarIcon className="h-5 w-5 text-yellow-500" />
                  <span className="font-medium text-gray-900">Best Performing Model:</span>
                  <span className="font-bold text-blue-600">{bestModel}</span>
                  <span className="text-sm text-gray-500">(lowest latency)</span>
                </div>
              </div>
            )}

            {/* Model Results Table */}
            <div className="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
              <table className="min-w-full divide-y divide-gray-300">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wide">
                      Model
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wide">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wide">
                      Latency
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wide">
                      Cost
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wide">
                      Tokens
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {results.map((result, index) => (
                    <tr key={index} className={result.model_name === bestModel ? 'bg-blue-50' : ''}>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center space-x-2">
                          <span className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium border ${getProviderColor(result.provider)}`}>
                            {result.provider}
                          </span>
                          <span className="text-sm font-medium text-gray-900">
                            {result.model_name}
                          </span>
                          {result.model_name === bestModel && (
                            <StarIcon className="h-4 w-4 text-yellow-500" />
                          )}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {result.error ? (
                          <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                            Failed
                          </span>
                        ) : (
                          <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            Success
                          </span>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {formatLatency(result.latency)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {formatCost(result.cost)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {result.token_count.toLocaleString()}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}

        {selectedTab === 'responses' && (
          <div className="space-y-4">
            {successfulResults.map((result, index) => (
              <div key={index} className="border border-gray-200 rounded-lg">
                <div className="px-4 py-3 bg-gray-50 border-b border-gray-200 flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <span className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium border ${getProviderColor(result.provider)}`}>
                      {result.provider}
                    </span>
                    <span className="font-medium text-gray-900">{result.model_name}</span>
                    {result.model_name === bestModel && (
                      <StarIcon className="h-4 w-4 text-yellow-500" />
                    )}
                  </div>
                  <button
                    onClick={() => copyToClipboard(result.response)}
                    className="text-sm text-blue-600 hover:text-blue-800"
                  >
                    Copy
                  </button>
                </div>
                <div className="p-4">
                  <div className="prose prose-sm max-w-none">
                    <pre className="whitespace-pre-wrap text-sm text-gray-700 font-sans">
                      {result.response}
                    </pre>
                  </div>
                </div>
              </div>
            ))}

            {failedResults.length > 0 && (
              <div className="mt-6">
                <h4 className="text-sm font-medium text-red-900 mb-3">Failed Executions</h4>
                {failedResults.map((result, index) => (
                  <div key={index} className="border border-red-200 rounded-lg bg-red-50 p-4">
                    <div className="flex items-center space-x-2 mb-2">
                      <span className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium border ${getProviderColor(result.provider)}`}>
                        {result.provider}
                      </span>
                      <span className="font-medium text-gray-900">{result.model_name}</span>
                    </div>
                    <p className="text-sm text-red-700">{result.error}</p>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}

        {selectedTab === 'metrics' && (
          <div className="space-y-6">
            {/* Detailed Metrics */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="bg-gray-50 p-4 rounded-lg">
                <h4 className="font-medium text-gray-900 mb-3">Execution Summary</h4>
                <dl className="space-y-2">
                  <div className="flex justify-between">
                    <dt className="text-sm text-gray-500">Total Models:</dt>
                    <dd className="text-sm font-medium text-gray-900">{comparisonMetrics.total_models}</dd>
                  </div>
                  <div className="flex justify-between">
                    <dt className="text-sm text-gray-500">Successful:</dt>
                    <dd className="text-sm font-medium text-green-600">{comparisonMetrics.successful_executions}</dd>
                  </div>
                  <div className="flex justify-between">
                    <dt className="text-sm text-gray-500">Failed:</dt>
                    <dd className="text-sm font-medium text-red-600">{comparisonMetrics.failed_executions}</dd>
                  </div>
                  <div className="flex justify-between">
                    <dt className="text-sm text-gray-500">Total Tokens:</dt>
                    <dd className="text-sm font-medium text-gray-900">{comparisonMetrics.total_tokens.toLocaleString()}</dd>
                  </div>
                </dl>
              </div>

              <div className="bg-gray-50 p-4 rounded-lg">
                <h4 className="font-medium text-gray-900 mb-3">Cost Breakdown</h4>
                <dl className="space-y-2">
                  {Object.entries(comparisonMetrics.cost_breakdown).map(([model, cost]) => (
                    <div key={model} className="flex justify-between">
                      <dt className="text-sm text-gray-500 truncate">{model}:</dt>
                      <dd className="text-sm font-medium text-gray-900">{formatCost(cost)}</dd>
                    </div>
                  ))}
                  <div className="border-t border-gray-200 pt-2 mt-2">
                    <div className="flex justify-between">
                      <dt className="text-sm font-medium text-gray-900">Total:</dt>
                      <dd className="text-sm font-bold text-gray-900">{formatCost(totalCost)}</dd>
                    </div>
                  </div>
                </dl>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ModelComparison;
