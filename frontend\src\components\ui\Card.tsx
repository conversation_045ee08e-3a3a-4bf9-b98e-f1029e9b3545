/**
 * Reusable Card Component
 * Standardized card implementation with consistent styling and behavior
 */

import React from 'react';
import type { CardProps } from '@/components/common/types';

export const Card: React.FC<CardProps> = ({
  variant = 'default',
  padding = 'md',
  hoverable = false,
  clickable = false,
  onClick,
  header,
  footer,
  actions,
  children,
  className = '',
  'data-testid': testId,
  id
}) => {
  const baseClasses = 'bg-white dark:bg-gray-800 rounded-lg transition-all duration-200';
  
  const variantClasses = {
    default: 'border border-gray-200 dark:border-gray-700',
    elevated: 'shadow-md border border-gray-200 dark:border-gray-700',
    outlined: 'border-2 border-gray-300 dark:border-gray-600',
    ghost: 'border-0'
  };

  const paddingClasses = {
    xs: 'p-2',
    sm: 'p-3',
    md: 'p-4',
    lg: 'p-6',
    xl: 'p-8'
  };

  const hoverClasses = hoverable || clickable 
    ? 'hover:shadow-lg hover:border-gray-300 dark:hover:border-gray-500 cursor-pointer' 
    : '';

  const handleClick = () => {
    if (clickable && onClick) {
      onClick();
    }
  };

  const cardClasses = `
    ${baseClasses}
    ${variantClasses[variant]}
    ${hoverClasses}
    ${className}
  `.trim();

  return (
    <div
      className={cardClasses}
      onClick={handleClick}
      data-testid={testId}
      id={id}
      role={clickable ? 'button' : undefined}
      tabIndex={clickable ? 0 : undefined}
      onKeyDown={clickable ? (e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          handleClick();
        }
      } : undefined}
    >
      {/* Header */}
      {header && (
        <div className={`${paddingClasses[padding]} border-b border-gray-200 dark:border-gray-700`}>
          {header}
        </div>
      )}

      {/* Content */}
      <div className={header || footer ? paddingClasses[padding] : paddingClasses[padding]}>
        {children}
      </div>

      {/* Actions */}
      {actions && (
        <div className={`${paddingClasses[padding]} border-t border-gray-200 dark:border-gray-700`}>
          {actions}
        </div>
      )}

      {/* Footer */}
      {footer && (
        <div className={`${paddingClasses[padding]} border-t border-gray-200 dark:border-gray-700`}>
          {footer}
        </div>
      )}
    </div>
  );
};

// Card Header component for consistent header styling
export const CardHeader: React.FC<{
  title?: string;
  subtitle?: string;
  actions?: React.ReactNode;
  children?: React.ReactNode;
  className?: string;
}> = ({ title, subtitle, actions, children, className = '' }) => (
  <div className={`flex items-center justify-between ${className}`}>
    <div className="flex-1">
      {title && (
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
          {title}
        </h3>
      )}
      {subtitle && (
        <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
          {subtitle}
        </p>
      )}
      {children}
    </div>
    {actions && (
      <div className="flex items-center space-x-2 ml-4">
        {actions}
      </div>
    )}
  </div>
);

// Card Body component for consistent body styling
export const CardBody: React.FC<{
  children: React.ReactNode;
  className?: string;
}> = ({ children, className = '' }) => (
  <div className={`text-gray-700 dark:text-gray-300 ${className}`}>
    {children}
  </div>
);

// Card Footer component for consistent footer styling
export const CardFooter: React.FC<{
  children: React.ReactNode;
  className?: string;
  justify?: 'start' | 'center' | 'end' | 'between';
}> = ({ children, className = '', justify = 'end' }) => {
  const justifyClasses = {
    start: 'justify-start',
    center: 'justify-center',
    end: 'justify-end',
    between: 'justify-between'
  };

  return (
    <div className={`flex items-center ${justifyClasses[justify]} space-x-3 ${className}`}>
      {children}
    </div>
  );
};

// Specialized card components for common use cases

// Stats Card for displaying metrics
export interface StatsCardProps {
  title: string;
  value: string | number;
  change?: {
    value: number;
    type: 'increase' | 'decrease';
    period?: string;
  };
  icon?: React.ReactNode;
  className?: string;
}

export const StatsCard: React.FC<StatsCardProps> = ({
  title,
  value,
  change,
  icon,
  className = ''
}) => (
  <Card variant="elevated" padding="lg" className={className}>
    <div className="flex items-center justify-between">
      <div>
        <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
          {title}
        </p>
        <p className="text-2xl font-bold text-gray-900 dark:text-white mt-1">
          {value}
        </p>
        {change && (
          <p className={`text-sm mt-1 ${
            change.type === 'increase' 
              ? 'text-green-600 dark:text-green-400' 
              : 'text-red-600 dark:text-red-400'
          }`}>
            {change.type === 'increase' ? '+' : '-'}{Math.abs(change.value)}%
            {change.period && ` ${change.period}`}
          </p>
        )}
      </div>
      {icon && (
        <div className="p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
          {icon}
        </div>
      )}
    </div>
  </Card>
);

// Feature Card for showcasing features or services
export interface FeatureCardProps {
  title: string;
  description: string;
  icon?: React.ReactNode;
  actions?: React.ReactNode;
  featured?: boolean;
  className?: string;
}

export const FeatureCard: React.FC<FeatureCardProps> = ({
  title,
  description,
  icon,
  actions,
  featured = false,
  className = ''
}) => (
  <Card 
    variant={featured ? 'elevated' : 'default'} 
    padding="lg" 
    hoverable
    className={`${featured ? 'ring-2 ring-blue-500' : ''} ${className}`}
  >
    <div className="text-center">
      {icon && (
        <div className="mx-auto w-12 h-12 flex items-center justify-center bg-blue-50 dark:bg-blue-900/20 rounded-lg mb-4">
          {icon}
        </div>
      )}
      <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
        {title}
      </h3>
      <p className="text-gray-600 dark:text-gray-400 mb-4">
        {description}
      </p>
      {actions && (
        <div className="flex justify-center">
          {actions}
        </div>
      )}
    </div>
  </Card>
);

// Loading Card for skeleton states
export const LoadingCard: React.FC<{
  lines?: number;
  showHeader?: boolean;
  showActions?: boolean;
  className?: string;
}> = ({ 
  lines = 3, 
  showHeader = true, 
  showActions = false,
  className = '' 
}) => (
  <Card padding="lg" className={className}>
    {showHeader && (
      <div className="animate-pulse mb-4">
        <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/3 mb-2"></div>
        <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
      </div>
    )}
    
    <div className="animate-pulse space-y-3">
      {Array.from({ length: lines }).map((_, index) => (
        <div 
          key={index}
          className="h-3 bg-gray-200 dark:bg-gray-700 rounded"
          style={{ width: `${Math.random() * 40 + 60}%` }}
        ></div>
      ))}
    </div>

    {showActions && (
      <div className="animate-pulse mt-4 flex space-x-2">
        <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-20"></div>
        <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-16"></div>
      </div>
    )}
  </Card>
);
