import React, { useState, useEffect } from 'react';
import type { MagnifyingGlassIcon, FunnelIcon, PlusIcon, ShareIcon, StarIcon, EyeIcon, PlayIcon, TagIcon, UserIcon, CalendarIcon } from '@heroicons/react/24/outline';
import { StarIcon as StarIconSolid } from '@heroicons/react/24/solid';
import { useWorkspace } from '@/contexts/WorkspaceContext';
import { useAuth } from '@/contexts/AuthContext';

import type { Prompt } from '@/types';

interface SharedPromptLibraryProps {
  workspaceId: string;
}

interface WorkspacePrompt extends Prompt {
  author: {
    id: string;
    name: string;
    email: string;
  };
  sharedAt: Date;
  isFavorited: boolean;
  workspaceRole: 'public' | 'workspace' | 'private';
  approvalStatus?: 'pending' | 'approved' | 'rejected';
  stats: {
    views: number;
    executions: number;
    favorites: number;
  };
}

export const SharedPromptLibrary: React.FC<SharedPromptLibraryProps> = ({
  workspaceId
}) => {
  const { user } = useAuth();
  const { currentWorkspace, getUserRole, canUserEdit } = useWorkspace();
  const [prompts, setPrompts] = useState<WorkspacePrompt[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedAuthor, setSelectedAuthor] = useState('all');
  const [sortBy, setSortBy] = useState('recent');
  const [showFilters, setShowFilters] = useState(false);

// const userRole = getUserRole(workspaceId); // Unused assignment
  const canEdit = canUserEdit(workspaceId);

  useEffect(() => {
    if (workspaceId) {
      loadWorkspacePrompts();
    }
  }, [workspaceId, selectedCategory, selectedAuthor, sortBy]);

  const loadWorkspacePrompts = async () => {
    setLoading(true);
    try {
      // Simulate API call - replace with actual workspace prompts service
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Mock data - replace with real workspace prompts
      const mockPrompts: WorkspacePrompt[] = [
        {
          id: '1',
          title: 'Customer Support Response Generator',
          description: 'Generate professional customer support responses for common inquiries',
          content: 'You are a helpful customer support representative. Respond to the following customer inquiry: {{inquiry}}',
          category: 'customer-service',
          tags: ['customer-service', 'support', 'professional'],
          variables: [
            { name: 'inquiry', description: 'Customer inquiry or question', type: 'text', required: true, defaultValue: '' }
          ],
          isPublic: false,
          createdAt: new Date('2024-01-15'),
          updatedAt: new Date('2024-01-20'),
          author: {
            id: 'user1',
            name: 'John Doe',
            email: '<EMAIL>'
          },
          sharedAt: new Date('2024-01-15'),
          isFavorited: true,
          workspaceRole: 'workspace',
          approvalStatus: 'approved',
          stats: {
            views: 234,
            executions: 89,
            favorites: 12
          }
        },
        {
          id: '2',
          title: 'Product Description Writer',
          description: 'Create compelling product descriptions for e-commerce',
          content: 'Write a compelling product description for: {{product_name}}. Key features: {{features}}. Target audience: {{audience}}',
          category: 'marketing',
          tags: ['marketing', 'e-commerce', 'copywriting'],
          variables: [
            { name: 'product_name', description: 'Name of the product', type: 'text', required: true, defaultValue: '' },
            { name: 'features', description: 'Key product features', type: 'text', required: true, defaultValue: '' },
            { name: 'audience', description: 'Target audience', type: 'text', required: false, defaultValue: 'general consumers' }
          ],
          isPublic: true,
          createdAt: new Date('2024-01-10'),
          updatedAt: new Date('2024-01-18'),
          author: {
            id: 'user2',
            name: 'Jane Smith',
            email: '<EMAIL>'
          },
          sharedAt: new Date('2024-01-10'),
          isFavorited: false,
          workspaceRole: 'public',
          stats: {
            views: 456,
            executions: 123,
            favorites: 28
          }
        },
        {
          id: '3',
          title: 'Meeting Summary Generator',
          description: 'Generate concise meeting summaries from notes',
          content: 'Create a professional meeting summary from these notes: {{meeting_notes}}. Include action items and key decisions.',
          category: 'productivity',
          tags: ['meetings', 'productivity', 'summary'],
          variables: [
            { name: 'meeting_notes', description: 'Raw meeting notes', type: 'text', required: true, defaultValue: '' }
          ],
          isPublic: false,
          createdAt: new Date('2024-01-12'),
          updatedAt: new Date('2024-01-16'),
          author: {
            id: 'user3',
            name: 'Mike Johnson',
            email: '<EMAIL>'
          },
          sharedAt: new Date('2024-01-12'),
          isFavorited: false,
          workspaceRole: 'workspace',
          approvalStatus: 'pending',
          stats: {
            views: 89,
            executions: 34,
            favorites: 7
          }
        }
      ];

      setPrompts(mockPrompts);
    } catch (error) {
      console.error('Failed to load workspace prompts:', error);
    } finally {
      setLoading(false);
    }
  };

  // TODO: Add try-catch error handling to this async handler
  const handleFavoriteToggle = async (promptId: string) => {
    try {
      // Simulate API call to toggle favorite
      setPrompts(prev => prev.map(prompt =>
        prompt.id === promptId
          ? {
              ...prompt,
              isFavorited: !prompt.isFavorited,
              stats: {
                ...prompt.stats,
                favorites: prompt.isFavorited ? prompt.stats.favorites - 1 : prompt.stats.favorites + 1
              }
            }
          : prompt
      ));
    } catch (error) {
      console.error('Failed to toggle favorite:', error);
    }
  };

  // TODO: Add try-catch error handling to this async handler
  const handleSharePrompt = async (promptId: string) => {
    try {
      // Simulate API call to share prompt
      console.log('Sharing prompt:', promptId);
      alert('Prompt shared successfully!');
    } catch (error) {
      console.error('Failed to share prompt:', error);
    }
  };

  const filteredPrompts = prompts.filter(prompt => {
    const matchesSearch = prompt.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         prompt.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         prompt.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));

    const matchesCategory = selectedCategory === 'all' || prompt.category === selectedCategory;
    const matchesAuthor = selectedAuthor === 'all' || prompt.author.id === selectedAuthor;

    return matchesSearch && matchesCategory && matchesAuthor;
  });

  const sortedPrompts = [...filteredPrompts].sort((a, b) => {
    switch (sortBy) {
      case 'recent':
        return new Date(b.sharedAt).getTime() - new Date(a.sharedAt).getTime();
      case 'popular':
        return b.stats.executions - a.stats.executions;
      case 'favorites':
        return b.stats.favorites - a.stats.favorites;
      case 'alphabetical':
        return a.title.localeCompare(b.title);
      default:
        return 0;
    }
  });

  const categories = ['all', ...Array.from(new Set(prompts.map(p => p.category)))];
  const authors = ['all', ...Array.from(new Set(prompts.map(p => p.author.id)))];
  const authorMap = prompts.reduce((acc, p) => {
    acc[p.author.id] = p.author.name;
    return acc;
  }, {} as Record<string, string>);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-sm text-gray-500">Loading shared prompts...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Shared Prompt Library</h2>
          <p className="text-sm text-gray-500">
            Discover and use prompts shared by your team
          </p>
        </div>
        {canEdit && (
          <button
            onClick={() => {
              // Navigate to create prompt page
              console.log('Navigate to create prompt');
            }}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <PlusIcon className="h-4 w-4 mr-2" />
            Create Prompt
          </button>
        )}
      </div>

      {/* Search and Filters */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex flex-col sm:flex-row gap-4">
          {/* Search */}
          <div className="flex-1">
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
              </div>
              <input
                type="text"
                placeholder="Search prompts..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          </div>

          {/* Filter Toggle */}
          <button
            onClick={() => setShowFilters(!showFilters)}
            className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <FunnelIcon className="h-4 w-4 mr-2" />
            Filters
          </button>
        </div>

        {/* Filters */}
        {showFilters && (
          <div className="mt-4 pt-4 border-t border-gray-200">
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Category
                </label>
                <select
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  {categories.map(category => (
                    <option key={category} value={category}>
                      {category === 'all' ? 'All Categories' : category.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Author
                </label>
                <select
                  value={selectedAuthor}
                  onChange={(e) => setSelectedAuthor(e.target.value)}
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  {authors.map(authorId => (
                    <option key={authorId} value={authorId}>
                      {authorId === 'all' ? 'All Authors' : authorMap[authorId]}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Sort By
                </label>
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value)}
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="recent">Most Recent</option>
                  <option value="popular">Most Popular</option>
                  <option value="favorites">Most Favorited</option>
                  <option value="alphabetical">Alphabetical</option>
                </select>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Results Summary */}
      <div className="flex items-center justify-between text-sm text-gray-500">
        <span>
          {sortedPrompts.length} prompt{sortedPrompts.length !== 1 ? 's' : ''} found
        </span>
        {searchQuery && (
          <button
            onClick={() => setSearchQuery('')}
            className="text-blue-600 hover:text-blue-500"
          >
            Clear search
          </button>
        )}
      </div>

      {/* Prompts Grid */}
      {sortedPrompts.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {sortedPrompts.map((prompt) => (
            <div key={prompt.id} className="bg-white shadow rounded-lg overflow-hidden hover:shadow-lg transition-shadow">
              <div className="p-6">
                {/* Header */}
                <div className="flex items-start justify-between mb-3">
                  <div className="flex-1 min-w-0">
                    <h3 className="text-lg font-medium text-gray-900 truncate">
                      {prompt.title}
                    </h3>
                    <p className="text-sm text-gray-500 mt-1 line-clamp-2">
                      {prompt.description}
                    </p>
                  </div>
                  <div className="flex items-center space-x-1 ml-2">
                    <button
                      onClick={() => handleFavoriteToggle(prompt.id)}
                      className="p-1 text-gray-400 hover:text-yellow-500"
                    >
                      {prompt.isFavorited ? (
                        <StarIconSolid className="h-5 w-5 text-yellow-500" />
                      ) : (
                        <StarIcon className="h-5 w-5" />
                      )}
                    </button>
                    <button
                      onClick={() => handleSharePrompt(prompt.id)}
                      className="p-1 text-gray-400 hover:text-blue-500"
                    >
                      <ShareIcon className="h-5 w-5" />
                    </button>
                  </div>
                </div>

                {/* Author and Date */}
                <div className="flex items-center text-sm text-gray-500 mb-3">
                  <UserIcon className="h-4 w-4 mr-1" />
                  <span className="mr-3">{prompt.author.name}</span>
                  <CalendarIcon className="h-4 w-4 mr-1" />
                  <span>{prompt.sharedAt.toLocaleDateString()}</span>
                </div>

                {/* Tags */}
                <div className="flex flex-wrap gap-1 mb-4">
                  {prompt.tags.slice(0, 3).map((tag) => (
                    <span
                      key={tag}
                      className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                    >
                      <TagIcon className="h-3 w-3 mr-1" />
                      {tag}
                    </span>
                  ))}
                  {prompt.tags.length > 3 && (
                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                      +{prompt.tags.length - 3} more
                    </span>
                  )}
                </div>

                {/* Stats */}
                <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                  <div className="flex items-center space-x-4">
                    <div className="flex items-center">
                      <EyeIcon className="h-4 w-4 mr-1" />
                      {prompt.stats.views}
                    </div>
                    <div className="flex items-center">
                      <PlayIcon className="h-4 w-4 mr-1" />
                      {prompt.stats.executions}
                    </div>
                    <div className="flex items-center">
                      <StarIcon className="h-4 w-4 mr-1" />
                      {prompt.stats.favorites}
                    </div>
                  </div>
                  {prompt.workspaceRole === 'public' && (
                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                      Public
                    </span>
                  )}
                  {prompt.approvalStatus === 'pending' && (
                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                      Pending
                    </span>
                  )}
                </div>

                {/* Actions */}
                <div className="flex space-x-2">
                  <button
                    onClick={() => {
                      // Navigate to prompt detail/execution page
                      console.log('View prompt:', prompt.id);
                    }}
                    className="flex-1 inline-flex items-center justify-center px-3 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  >
                    <EyeIcon className="h-4 w-4 mr-2" />
                    View
                  </button>
                  <button
                    onClick={() => {
                      // Navigate to prompt execution page
                      console.log('Execute prompt:', prompt.id);
                    }}
                    className="flex-1 inline-flex items-center justify-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  >
                    <PlayIcon className="h-4 w-4 mr-2" />
                    Execute
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <div className="mx-auto h-12 w-12 text-gray-400">
            <MagnifyingGlassIcon className="h-12 w-12" />
          </div>
          <h3 className="mt-2 text-sm font-medium text-gray-900">No prompts found</h3>
          <p className="mt-1 text-sm text-gray-500">
            {searchQuery ? 'Try adjusting your search or filters.' : 'No prompts have been shared in this workspace yet.'}
          </p>
          {canEdit && !searchQuery && (
            <div className="mt-6">
              <button
                onClick={() => {
                  // Navigate to create prompt page
                  console.log('Navigate to create prompt');
                }}
                className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <PlusIcon className="h-4 w-4 mr-2" />
                Create First Prompt
              </button>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default SharedPromptLibrary;