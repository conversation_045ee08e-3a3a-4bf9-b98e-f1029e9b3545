// Export unified service architecture
export * from './baseService';
export * from './serviceFactory';
export * from './serviceRegistry';
export * from './serviceInterfaces';
export * from './serviceConfig';

// Export all services
export { DocumentService } from './documentService';
export { TemplateService } from './templateService';
export { PromptGenerationService } from './promptGenerationService';
export { PerformanceMonitoringService } from './performanceMonitoringService';
export { UserFeedbackService } from './userFeedbackService';
export { AnalyticsService } from './analyticsService';
export { WorkspaceService } from './workspaceService';
export { SettingsService } from './settingsService';
export { MarketplaceService } from './marketplaceService';

// Export types
export type { RAGDocument } from '@/types';
export type { 
  PromptGenerationRequest, 
  PromptGenerationResponse,
  PromptEnhancementSuggestion 
} from '@/types';

// Export service instances and utilities
export { 
  serviceFactory, 
  createService, 
  ServicePresets, 
  initializeServiceFactory 
} from './serviceFactory';

export { 
  serviceRegistry, 
  registerService, 
  getService, 
  createAndRegisterService, 
  initializeServices 
} from './serviceRegistry';

export { 
  getServiceConfig, 
  getCacheTTL, 
  getRetryConfig, 
  currentEnvironment, 
  currentEnvironmentConfig 
} from './serviceConfig';
