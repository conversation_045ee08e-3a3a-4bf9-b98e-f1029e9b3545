/**
 * Commitlint Configuration
 * 
 * Enforces conventional commit message format for better changelog generation
 * and semantic versioning support.
 * 
 * Format: <type>[optional scope]: <description>
 * 
 * Examples:
 * - feat: add user authentication
 * - fix(auth): resolve login redirect issue
 * - docs: update API documentation
 * - test: add unit tests for user service
 * - refactor(components): simplify modal component
 * - perf: optimize bundle size
 * - style: fix code formatting
 * - ci: update GitHub Actions workflow
 * 
 * @module CommitlintConfig
 */

module.exports = {
  extends: ['@commitlint/config-conventional'],
  rules: {
    // Type enum - allowed commit types
    'type-enum': [
      2,
      'always',
      [
        'feat',     // New feature
        'fix',      // Bug fix
        'docs',     // Documentation changes
        'style',    // Code style changes (formatting, etc.)
        'refactor', // Code refactoring
        'perf',     // Performance improvements
        'test',     // Adding or updating tests
        'build',    // Build system changes
        'ci',       // CI/CD changes
        'chore',    // Maintenance tasks
        'revert',   // Reverting changes
        'wip',      // Work in progress (for development branches)
      ],
    ],
    
    // Subject case - enforce lowercase
    'subject-case': [2, 'always', 'lower-case'],
    
    // Subject length - max 72 characters
    'subject-max-length': [2, 'always', 72],
    
    // Subject minimum length
    'subject-min-length': [2, 'always', 10],
    
    // Subject should not end with period
    'subject-full-stop': [2, 'never', '.'],
    
    // Body should be separated by blank line
    'body-leading-blank': [2, 'always'],
    
    // Body line length
    'body-max-line-length': [2, 'always', 100],
    
    // Footer should be separated by blank line
    'footer-leading-blank': [2, 'always'],
    
    // Header length
    'header-max-length': [2, 'always', 100],
    
    // Scope case
    'scope-case': [2, 'always', 'lower-case'],
    
    // Type case
    'type-case': [2, 'always', 'lower-case'],
    
    // Type should not be empty
    'type-empty': [2, 'never'],
    
    // Subject should not be empty
    'subject-empty': [2, 'never'],
  },
  
  // Custom rules for project-specific requirements
  plugins: [
    {
      rules: {
        // Custom rule to ensure breaking changes are properly documented
        'breaking-change-format': (parsed) => {
          const { body, footer } = parsed;
          const hasBreakingChange = body?.includes('BREAKING CHANGE') || 
                                   footer?.includes('BREAKING CHANGE');
          
          if (hasBreakingChange) {
            const breakingChangePattern = /BREAKING CHANGE:\s+.+/;
            const isProperlyFormatted = breakingChangePattern.test(body || '') ||
                                       breakingChangePattern.test(footer || '');
            
            return [
              isProperlyFormatted,
              'Breaking changes must be documented with "BREAKING CHANGE: <description>"'
            ];
          }
          
          return [true];
        },
      },
    },
  ],
  
  // Ignore certain commit patterns (for automated commits)
  ignores: [
    (message) => message.includes('WIP'),
    (message) => message.includes('Merge branch'),
    (message) => message.includes('Merge pull request'),
    (message) => message.startsWith('Bump '),
    (message) => message.startsWith('Release '),
  ],
  
  // Default ignore patterns
  defaultIgnores: true,
  
  // Help URL for commit message format
  helpUrl: 'https://github.com/conventional-changelog/commitlint/#what-is-commitlint',
};
