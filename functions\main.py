from firebase_functions import https_fn
from firebase_admin import initialize_app

# Initialize Firebase Admin
try:
    initialize_app()
except ValueError:
    pass

@https_fn.on_call()
def api(req: https_fn.CallableRequest):
    """Main API endpoint - Firebase callable functions handle CORS automatically"""
    data = req.data or {}
    endpoint = data.get('endpoint', 'health')

    if endpoint == 'health':
        return {'status': 'success', 'message': 'API working', 'region': 'australia-southeast1'}
    elif endpoint == 'execute_prompt':
        return {'status': 'success', 'message': 'Mock execution', 'region': 'australia-southeast1', 'response': 'Test response from Australia'}
    elif endpoint == 'test_openrouter_connection':
        return {'status': 'success', 'message': 'Mock connection', 'region': 'australia-southeast1'}
    else:
        return {'status': 'error', 'message': f'Unknown endpoint: {endpoint}'}

@https_fn.on_request()
def health(req):
    """Health check endpoint"""
    return {'status': 'healthy', 'region': 'australia-southeast1'}
