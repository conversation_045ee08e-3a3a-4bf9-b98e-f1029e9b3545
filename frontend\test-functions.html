<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Firebase Functions Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .loading { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; }
        button:hover { background: #0056b3; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>Firebase Functions Test</h1>
    <p>Testing Firebase Functions in australia-southeast1 region</p>
    
    <button onclick="testHealthEndpoint()">Test Health Endpoint</button>
    <button onclick="testExecutePrompt()">Test Execute Prompt</button>
    <button onclick="testOpenRouterConnection()">Test OpenRouter Connection</button>
    
    <div id="results"></div>

    <!-- Firebase SDK -->
    <script type="module">
        import { initializeApp } from 'https://www.gstatic.com/firebasejs/11.10.0/firebase-app.js';
        import { getFunctions, httpsCallable, connectFunctionsEmulator } from 'https://www.gstatic.com/firebasejs/11.10.0/firebase-functions.js';

        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyDJWjw2e8FayU3CvIWyGXXFAqDCTFN5CJs",
            authDomain: "rag-prompt-library.firebaseapp.com",
            projectId: "rag-prompt-library",
            storageBucket: "rag-prompt-library.firebasestorage.app",
            messagingSenderId: "743998930129",
            appId: "1:743998930129:web:69dd61394ed81598cd99f0",
            measurementId: "G-CEDFF0WMPW"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const functions = getFunctions(app, 'australia-southeast1');

        // Make functions available globally
        window.functions = functions;
        window.httpsCallable = httpsCallable;

        // Test functions
        window.testHealthEndpoint = async function() {
            addResult('Testing health endpoint...', 'loading');
            try {
                const apiFunction = httpsCallable(functions, 'api');
                const result = await apiFunction({ endpoint: 'health' });
                addResult('Health endpoint test successful!', 'success', result.data);
            } catch (error) {
                addResult('Health endpoint test failed!', 'error', error);
            }
        };

        window.testExecutePrompt = async function() {
            addResult('Testing execute prompt endpoint...', 'loading');
            try {
                const apiFunction = httpsCallable(functions, 'api');
                const result = await apiFunction({ endpoint: 'execute_prompt' });
                addResult('Execute prompt test successful!', 'success', result.data);
            } catch (error) {
                addResult('Execute prompt test failed!', 'error', error);
            }
        };

        window.testOpenRouterConnection = async function() {
            addResult('Testing OpenRouter connection endpoint...', 'loading');
            try {
                const apiFunction = httpsCallable(functions, 'api');
                const result = await apiFunction({ endpoint: 'test_openrouter_connection' });
                addResult('OpenRouter connection test successful!', 'success', result.data);
            } catch (error) {
                addResult('OpenRouter connection test failed!', 'error', error);
            }
        };

        function addResult(message, type, data = null) {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            
            let content = `<strong>${message}</strong>`;
            if (data) {
                content += `<pre>${JSON.stringify(data, null, 2)}</pre>`;
            }
            
            resultDiv.innerHTML = content;
            resultsDiv.appendChild(resultDiv);
            
            // Scroll to bottom
            resultDiv.scrollIntoView({ behavior: 'smooth' });
        }

        // Auto-test on load
        console.log('Firebase Functions Test Page Loaded');
        console.log('Functions region:', functions.region);
    </script>
</body>
</html>
