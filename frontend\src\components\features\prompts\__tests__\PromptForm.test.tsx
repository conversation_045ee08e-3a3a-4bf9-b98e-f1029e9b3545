import React from 'react';
import { screen, fireEvent, waitFor, act } from '@testing-library/react';
import { renderWithProvidersactAsync} from '../../../test/test-utils';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { PromptForm } from '../PromptForm';
import { ToastProvider } from '@/components/common/Toast';

const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <ToastProvider>
    {children}
  </ToastProvider>
);

describe('PromptForm', () => {
  const mockOnSave = vi.fn();
  const mockOnCancel = vi.fn();

  const existingPrompt = {
    id: 'existing-prompt',
    title: 'Existing Prompt',
    content: 'This is an existing prompt with {{variable1}}',
    variables: [
      { name: 'variable1', type: 'text' as const, description: 'Test variable', required: true }
    ],
    tags: ['existing', 'test'],
    createdAt: new Date(),
    updatedAt: new Date(),
    userId: 'test-user'
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders empty form for new prompt', () => {
    renderWithProviders(
      <TestWrapper>
        <PromptForm onSave={mockOnSave} onCancel={mockOnCancel} />
      </TestWrapper>
    );

    expect(screen.getByLabelText(/title/i)).toHaveValue('');
    expect(screen.getByLabelText(/content/i)).toHaveValue('');
    expect(screen.getByRole('button', { name: /save prompt/i })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /cancel/i })).toBeInTheDocument();
  });

  it('renders form with existing prompt data', () => {
    renderWithProviders(
      <TestWrapper>
        <PromptForm 
          prompt={existingPrompt}
          onSave={mockOnSave} 
          onCancel={mockOnCancel} 
        />
      </TestWrapper>
    );

    expect(screen.getByDisplayValue('Existing Prompt')).toBeInTheDocument();
    expect(screen.getByDisplayValue(/This is an existing prompt/)).toBeInTheDocument();
    expect(screen.getByText('existing')).toBeInTheDocument();
    expect(screen.getByText('test')).toBeInTheDocument();
  });

  it('validates required fields', async () => {
    renderWithProviders(
      <TestWrapper>
        <PromptForm onSave={mockOnSave} onCancel={mockOnCancel} />
      </TestWrapper>
    );

    const saveButton = screen.getByRole('button', { name: /save prompt/i });
    await actAsync(async () => {
      fireEvent.click(saveButton);
    });

    await waitFor(() => {
      expect(screen.getByText(/title is required/i)).toBeInTheDocument();
      expect(screen.getByText(/content is required/i)).toBeInTheDocument();
    });

    expect(mockOnSave).not.toHaveBeenCalled();
  });

  it('detects variables in prompt content', async () => {
    renderWithProviders(
      <TestWrapper>
        <PromptForm onSave={mockOnSave} onCancel={mockOnCancel} />
      </TestWrapper>
    );

    const contentInput = screen.getByLabelText(/content/i);
    await actAsync(async () => {
      fireEvent.change(contentInput, {
        target: { value: 'Hello {{name}}, your age is {{age}}' }
      });
    });

    await waitFor(() => {
      expect(screen.getByDisplayValue('name')).toBeInTheDocument();
      expect(screen.getByDisplayValue('age')).toBeInTheDocument();
    });
  });

  it('allows editing variable properties', async () => {
    renderWithProviders(
      <TestWrapper>
        <PromptForm onSave={mockOnSave} onCancel={mockOnCancel} />
      </TestWrapper>
    );

    // Add content with variables
    const contentInput = screen.getByLabelText(/content/i);
    act(() => { fireEvent.change(contentInput, { 
      target: { value: 'Hello {{name}}' } 
    }); });

    await waitFor(() => {
      expect(screen.getByDisplayValue('name')).toBeInTheDocument();
    });

    // Edit variable description
    const descriptionInput = screen.getByPlaceholderText(/variable description/i);
    act(() => { fireEvent.change(descriptionInput, { 
      target: { value: 'User name' } 
    }); });

    expect(descriptionInput).toHaveValue('User name');
  });

  it('handles tag input', () => {
    renderWithProviders(
      <TestWrapper>
        <PromptForm onSave={mockOnSave} onCancel={mockOnCancel} />
      </TestWrapper>
    );

    const tagsInput = screen.getByTestId('tag-input');
    act(() => { fireEvent.change(tagsInput, { 
      target: { value: 'tag1, tag2, tag3' } 
    }); });

    expect(tagsInput).toHaveValue('tag1, tag2, tag3');
  });

  it('saves prompt with valid data', async () => {
    renderWithProviders(
      <TestWrapper>
        <PromptForm onSave={mockOnSave} onCancel={mockOnCancel} />
      </TestWrapper>
    );

    // Fill in required fields
    fireEvent.change(screen.getByLabelText(/title/i), { 
      target: { value: 'Test Prompt' } 
    });
    fireEvent.change(screen.getByLabelText(/content/i), { 
      target: { value: 'Hello {{name}}!' } 
    });
    fireEvent.change(screen.getByTestId('tag-input'), {
      target: { value: 'test' }
    });
    fireEvent.click(screen.getByTestId('add-tag-button'));

    fireEvent.change(screen.getByTestId('tag-input'), {
      target: { value: 'example' }
    });
    fireEvent.click(screen.getByTestId('add-tag-button'));

    // Wait for variables to be detected
    await waitFor(() => {
      expect(screen.getByDisplayValue('name')).toBeInTheDocument();
    });

    // Save the prompt
    const saveButton = screen.getByRole('button', { name: /save prompt/i });
    act(() => { fireEvent.click(saveButton); });

    await waitFor(() => {
      expect(mockOnSave).toHaveBeenCalledWith({
        title: 'Test Prompt',
        description: '',
        content: 'Hello {{name}}!',
        category: '',
        variables: [
          {
            name: 'name',
            type: 'text',
            description: '',
            required: false,
            defaultValue: ''
          }
        ],
        tags: ['test', 'example'],
        isPublic: false
      });
    });
  });

  it('calls onCancel when cancel button is clicked', () => {
    renderWithProviders(
      <TestWrapper>
        <PromptForm onSave={mockOnSave} onCancel={mockOnCancel} />
      </TestWrapper>
    );

    const cancelButton = screen.getByRole('button', { name: /cancel/i });
    act(() => { fireEvent.click(cancelButton); });

    expect(mockOnCancel).toHaveBeenCalled();
  });

  it('updates existing prompt', async () => {
    renderWithProviders(
      <TestWrapper>
        <PromptForm 
          prompt={existingPrompt}
          onSave={mockOnSave} 
          onCancel={mockOnCancel} 
        />
      </TestWrapper>
    );

    // Modify the title
    const titleInput = screen.getByDisplayValue('Existing Prompt');
    act(() => { fireEvent.change(titleInput, { 
      target: { value: 'Updated Prompt' } 
    }); });

    const saveButton = screen.getByRole('button', { name: /save prompt/i });
    act(() => { fireEvent.click(saveButton); });

    await waitFor(() => {
      expect(mockOnSave).toHaveBeenCalledWith(
        expect.objectContaining({
          title: 'Updated Prompt'
        })
      );
    });
  });

  it('handles variable type changes', async () => {
    renderWithProviders(
      <TestWrapper>
        <PromptForm onSave={mockOnSave} onCancel={mockOnCancel} />
      </TestWrapper>
    );

    // Add content with variables
    const contentInput = screen.getByLabelText(/content/i);
    act(() => { fireEvent.change(contentInput, { 
      target: { value: 'Your age is {{age}}' } 
    }); });

    await waitFor(() => {
      expect(screen.getByDisplayValue('age')).toBeInTheDocument();
    });

    // Change variable type
    const typeSelect = screen.getByTestId('variable-type-0');
    act(() => { fireEvent.change(typeSelect, { target: { value: 'number' } }); });

    expect(typeSelect).toHaveValue('number');
  });

  it('handles variable required toggle', async () => {
    renderWithProviders(
      <TestWrapper>
        <PromptForm onSave={mockOnSave} onCancel={mockOnCancel} />
      </TestWrapper>
    );

    // Add content with variables
    const contentInput = screen.getByLabelText(/content/i);
    act(() => { fireEvent.change(contentInput, { 
      target: { value: 'Hello {{name}}' } 
    }); });

    await waitFor(() => {
      expect(screen.getByDisplayValue('name')).toBeInTheDocument();
    });

    // Toggle required checkbox
    const requiredCheckbox = screen.getByTestId('variable-required-0');

    // Initially should be unchecked
    expect(requiredCheckbox).not.toBeChecked();

    // Click to check it
    act(() => { fireEvent.click(requiredCheckbox); });
    expect(requiredCheckbox).toBeChecked();

    // Click again to uncheck it
    act(() => { fireEvent.click(requiredCheckbox); });
    expect(requiredCheckbox).not.toBeChecked();
  });

  it('shows loading state during save', async () => {
    const mockOnSave = vi.fn();

    renderWithProviders(
      <TestWrapper>
        <PromptForm onSave={mockOnSave} onCancel={mockOnCancel} loading={true} />
      </TestWrapper>
    );

    const saveButton = screen.getByRole('button', { name: /saving/i });

    expect(screen.getByText('Saving...')).toBeInTheDocument();
    expect(saveButton).toBeDisabled();
  });

  it('handles empty tags gracefully', async () => {
    renderWithProviders(
      <TestWrapper>
        <PromptForm onSave={mockOnSave} onCancel={mockOnCancel} />
      </TestWrapper>
    );

    // Fill in required fields without tags
    fireEvent.change(screen.getByLabelText(/title/i), { 
      target: { value: 'Test Prompt' } 
    });
    fireEvent.change(screen.getByLabelText(/content/i), { 
      target: { value: 'Test content' } 
    });

    const saveButton = screen.getByRole('button', { name: /save prompt/i });
    act(() => { fireEvent.click(saveButton); });

    await waitFor(() => {
      expect(mockOnSave).toHaveBeenCalledWith(
        expect.objectContaining({
          tags: []
        })
      );
    });
  });
});
