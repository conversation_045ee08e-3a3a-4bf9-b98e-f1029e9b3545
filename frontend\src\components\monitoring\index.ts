/**
 * Monitoring Components
 * Centralized exports for all performance and monitoring related components
 */

// Performance Monitoring Components
export { PerformanceMonitor, PerformanceWarning } from './PerformanceMonitor';
export { APIPerformanceMonitor, APIPerformanceAlerts } from './APIPerformanceMonitor';
export { WebVitalsDashboard } from './WebVitalsDashboard';

// Performance Dashboard Components
export { PerformanceDashboard } from './PerformanceDashboard';
export { PerformanceMonitoringDashboard } from './PerformanceMonitoringDashboard';
export { PerformanceDashboard as DevPerformanceDashboard } from './DevPerformanceDashboard';
export { PerformanceDashboard as TestPerformanceDashboard } from './TestPerformanceDashboard';

// Re-export types if any components export them
export type { PerformanceDashboardProps } from './PerformanceDashboard';
export type { PerformanceMonitoringDashboardProps } from './PerformanceMonitoringDashboard';

// Convenience exports for common monitoring functionality
export {
  PerformanceMonitor as Monitor,
  APIPerformanceMonitor as APIMonitor,
  WebVitalsDashboard as WebVitals,
  PerformanceDashboard as Dashboard
} from './PerformanceDashboard';
