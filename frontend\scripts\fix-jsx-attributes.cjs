#!/usr/bin/env node

/**
 * Fix malformed JSX attributes in React components
 * Converts div="className" to div className="className"
 */

const fs = require('fs');
const path = require('path');

class JSXAttributeFixer {
  constructor() {
    this.fixedFiles = [];
    this.errors = [];
  }

  /**
   * Fix JSX attributes in a file
   */
  fixFile(filePath) {
    try {
      console.log(`Fixing JSX attributes in: ${filePath}`);
      
      let content = fs.readFileSync(filePath, 'utf8');
      let originalContent = content;
      let fixCount = 0;

      // Fix malformed div attributes: div="className" -> div className="className"
      content = content.replace(/<div="([^"]+)"/g, (match, className) => {
        fixCount++;
        return `<div className="${className}"`;
      });

      // Fix malformed h1-h6 attributes: h3="className" -> h3 className="className"
      content = content.replace(/<(h[1-6])="([^"]+)"/g, (match, tag, className) => {
        fixCount++;
        return `<${tag} className="${className}"`;
      });

      // Fix malformed span attributes: span="className" -> span className="className"
      content = content.replace(/<span="([^"]+)"/g, (match, className) => {
        fixCount++;
        return `<span className="${className}"`;
      });

      // Fix malformed p attributes: p="className" -> p className="className"
      content = content.replace(/<p="([^"]+)"/g, (match, className) => {
        fixCount++;
        return `<p className="${className}"`;
      });

      // Fix malformed button attributes: button="className" -> button className="className"
      content = content.replace(/<button="([^"]+)"/g, (match, className) => {
        fixCount++;
        return `<button className="${className}"`;
      });

      // Fix malformed input attributes: input="className" -> input className="className"
      content = content.replace(/<input="([^"]+)"/g, (match, className) => {
        fixCount++;
        return `<input className="${className}"`;
      });

      // Fix malformed img attributes: img="className" -> img className="className"
      content = content.replace(/<img="([^"]+)"/g, (match, className) => {
        fixCount++;
        return `<img className="${className}"`;
      });

      // Fix malformed a attributes: a="className" -> a className="className"
      content = content.replace(/<a="([^"]+)"/g, (match, className) => {
        fixCount++;
        return `<a className="${className}"`;
      });

      // Fix malformed li attributes: li="className" -> li className="className"
      content = content.replace(/<li="([^"]+)"/g, (match, className) => {
        fixCount++;
        return `<li className="${className}"`;
      });

      // Fix malformed ul attributes: ul="className" -> ul className="className"
      content = content.replace(/<ul="([^"]+)"/g, (match, className) => {
        fixCount++;
        return `<ul className="${className}"`;
      });

      // Fix malformed ol attributes: ol="className" -> ol className="className"
      content = content.replace(/<ol="([^"]+)"/g, (match, className) => {
        fixCount++;
        return `<ol className="${className}"`;
      });

      // Fix malformed form attributes: form="className" -> form className="className"
      content = content.replace(/<form="([^"]+)"/g, (match, className) => {
        fixCount++;
        return `<form className="${className}"`;
      });

      // Fix malformed label attributes: label="className" -> label className="className"
      content = content.replace(/<label="([^"]+)"/g, (match, className) => {
        fixCount++;
        return `<label className="${className}"`;
      });

      // Fix malformed select attributes: select="className" -> select className="className"
      content = content.replace(/<select="([^"]+)"/g, (match, className) => {
        fixCount++;
        return `<select className="${className}"`;
      });

      // Fix malformed textarea attributes: textarea="className" -> textarea className="className"
      content = content.replace(/<textarea="([^"]+)"/g, (match, className) => {
        fixCount++;
        return `<textarea className="${className}"`;
      });

      // Fix malformed table attributes: table="className" -> table className="className"
      content = content.replace(/<table="([^"]+)"/g, (match, className) => {
        fixCount++;
        return `<table className="${className}"`;
      });

      // Fix malformed tr attributes: tr="className" -> tr className="className"
      content = content.replace(/<tr="([^"]+)"/g, (match, className) => {
        fixCount++;
        return `<tr className="${className}"`;
      });

      // Fix malformed td attributes: td="className" -> td className="className"
      content = content.replace(/<td="([^"]+)"/g, (match, className) => {
        fixCount++;
        return `<td className="${className}"`;
      });

      // Fix malformed th attributes: th="className" -> th className="className"
      content = content.replace(/<th="([^"]+)"/g, (match, className) => {
        fixCount++;
        return `<th className="${className}"`;
      });

      // Fix malformed thead attributes: thead="className" -> thead className="className"
      content = content.replace(/<thead="([^"]+)"/g, (match, className) => {
        fixCount++;
        return `<thead className="${className}"`;
      });

      // Fix malformed tbody attributes: tbody="className" -> tbody className="className"
      content = content.replace(/<tbody="([^"]+)"/g, (match, className) => {
        fixCount++;
        return `<tbody className="${className}"`;
      });

      // Fix malformed nav attributes: nav="className" -> nav className="className"
      content = content.replace(/<nav="([^"]+)"/g, (match, className) => {
        fixCount++;
        return `<nav className="${className}"`;
      });

      // Fix malformed section attributes: section="className" -> section className="className"
      content = content.replace(/<section="([^"]+)"/g, (match, className) => {
        fixCount++;
        return `<section className="${className}"`;
      });

      // Fix malformed article attributes: article="className" -> article className="className"
      content = content.replace(/<article="([^"]+)"/g, (match, className) => {
        fixCount++;
        return `<article className="${className}"`;
      });

      // Fix malformed header attributes: header="className" -> header className="className"
      content = content.replace(/<header="([^"]+)"/g, (match, className) => {
        fixCount++;
        return `<header className="${className}"`;
      });

      // Fix malformed footer attributes: footer="className" -> footer className="className"
      content = content.replace(/<footer="([^"]+)"/g, (match, className) => {
        fixCount++;
        return `<footer className="${className}"`;
      });

      // Fix malformed main attributes: main="className" -> main className="className"
      content = content.replace(/<main="([^"]+)"/g, (match, className) => {
        fixCount++;
        return `<main className="${className}"`;
      });

      // Fix malformed aside attributes: aside="className" -> aside className="className"
      content = content.replace(/<aside="([^"]+)"/g, (match, className) => {
        fixCount++;
        return `<aside className="${className}"`;
      });

      // Fix malformed svg attributes: svg="className" -> svg className="className"
      content = content.replace(/<svg="([^"]+)"/g, (match, className) => {
        fixCount++;
        return `<svg className="${className}"`;
      });

      // Fix malformed path attributes: path="className" -> path className="className"
      content = content.replace(/<path="([^"]+)"/g, (match, className) => {
        fixCount++;
        return `<path className="${className}"`;
      });

      // Fix malformed onClick syntax: onClick={() => func()}={className} -> onClick={() => func()} className={className}
      content = content.replace(/onClick=\{([^}]+)\}=\{([^}]+)\}/g, (match, onClick, className) => {
        fixCount++;
        return `onClick={${onClick}} className={${className}}`;
      });

      // Fix malformed onClick syntax with string: onClick={() => func()}="className" -> onClick={() => func()} className="className"
      content = content.replace(/onClick=\{([^}]+)\}="([^"]+)"/g, (match, onClick, className) => {
        fixCount++;
        return `onClick={${onClick}} className="${className}"`;
      });

      if (fixCount > 0) {
        fs.writeFileSync(filePath, content);
        this.fixedFiles.push({
          file: filePath,
          fixes: fixCount
        });
        console.log(`  ✅ Fixed ${fixCount} JSX attribute issues`);
      } else {
        console.log(`  ℹ️  No JSX attribute issues found`);
      }

    } catch (error) {
      this.errors.push({
        file: filePath,
        error: error.message
      });
      console.error(`  ❌ Error fixing ${filePath}: ${error.message}`);
    }
  }

  /**
   * Fix JSX attributes in all React files
   */
  fixAllFiles() {
    console.log('🔧 Fixing JSX attributes in React components...\n');

    const filesToFix = [
      'src/components/monitoring/DevPerformanceDashboard.tsx'
    ];

    filesToFix.forEach(file => {
      const fullPath = path.join(__dirname, '..', file);
      if (fs.existsSync(fullPath)) {
        this.fixFile(fullPath);
      } else {
        console.log(`  ⚠️  File not found: ${file}`);
      }
    });

    this.generateReport();
  }

  /**
   * Generate summary report
   */
  generateReport() {
    console.log('\n📋 JSX Attribute Fix Report');
    console.log('===========================');

    if (this.fixedFiles.length > 0) {
      console.log(`✅ Fixed files: ${this.fixedFiles.length}`);
      this.fixedFiles.forEach(({ file, fixes }) => {
        console.log(`  • ${file}: ${fixes} fixes`);
      });
    }

    if (this.errors.length > 0) {
      console.log(`\n❌ Errors: ${this.errors.length}`);
      this.errors.forEach(({ file, error }) => {
        console.log(`  • ${file}: ${error}`);
      });
    }

    const totalFixes = this.fixedFiles.reduce((sum, { fixes }) => sum + fixes, 0);
    console.log(`\n📊 Total fixes applied: ${totalFixes}`);
  }
}

// Main execution
if (require.main === module) {
  const fixer = new JSXAttributeFixer();
  fixer.fixAllFiles();
}

module.exports = JSXAttributeFixer;
