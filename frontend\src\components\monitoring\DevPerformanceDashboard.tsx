import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { useWebVitals } from '@/hooks/useWebVitals';
import { apiPerformanceMonitor } from '@/utils/apiPerformanceMonitor';
import { performanceProfiler } from '@/utils/performanceProfiler';
import type { BaseComponentProps } from '@/components/common/types';

interface PerformanceDashboardProps extends BaseComponentProps {
  enabled?: boolean;
}

interface WebVitalsMetric {
  name: string;
  value: number;
  rating: 'good' | 'needs-improvement' | 'poor';
  delta?: number;
}

interface APIMetric {
  url: string;
  method: string;
  duration: number;
  status: number;
  timestamp: number;
  size?: number;
}

interface ComponentMetric {
  name: string;
  renderTime: number;
  updateCount: number;
  timestamp: number;
}

interface NetworkInfo {
  effectiveType?: string;
  downlink?: number;
  rtt?: number;
  saveData?: boolean;
}

interface PerformanceMetrics {
  webVitals: Record<string, WebVitalsMetric>;
  apiMetrics: APIMetric[];
  componentMetrics: ComponentMetric[];
  memoryUsage: number;
  networkInfo: NetworkInfo;
}

const PerformanceDashboardComponent: React.FC<PerformanceDashboardProps> = ({
  enabled = process.env.NODE_ENV === 'development'
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [activeTab, setActiveTab] = useState<'overview' | 'webvitals' | 'api' | 'components' | 'network'>('overview');
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    webVitals: {},
    apiMetrics: [],
    componentMetrics: [],
    memoryUsage: 0,
    networkInfo: {}
  });

  const { summary: webVitalsSummary, getOverallScore } = useWebVitals();

  // Memoize the metrics update function to prevent recreation on every render
  const updateMetrics = useCallback(() => {
    setMetrics({
      webVitals: webVitalsSummary,
      apiMetrics: apiPerformanceMonitor.getStats(),
      componentMetrics: performanceProfiler.getSlowestComponents(10),
      memoryUsage: (performance as Performance & { memory?: { usedJSHeapSize: number } }).memory?.usedJSHeapSize || 0,
      networkInfo: (navigator as Navigator & { connection?: NetworkInfo }).connection || {}
    });
  }, [webVitalsSummary]);

  useEffect(() => {
    if (!enabled) return;

    updateMetrics();
    const interval = setInterval(updateMetrics, 5000); // Update every 5 seconds

    return () => clearInterval(interval);
  }, [enabled, updateMetrics]);

  // Memoize utility functions to prevent recreation on every render
  const formatBytes = useCallback((bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }, []);

  const getScoreColor = useCallback((score: number | null) => {
    if (score === null) return 'text-gray-500';
    if (score >= 80) return 'text-green-500';
    if (score >= 50) return 'text-yellow-500';
    return 'text-red-500';
  }, []);

  // Memoize computed values
  const overallScore = useMemo(() => getOverallScore(), [getOverallScore]);
  const hasIssues = useMemo(() => overallScore !== null && overallScore < 80, [overallScore]);

  if (!enabled) return null;

  return (
    <div className="fixed bottom-4 left-4 z-50">
      {/* Toggle Button */}
      <button
        onClick={() => setIsVisible(!isVisible)}
        className={`px-4 py-2 rounded-lg shadow-lg text-sm font-medium transition-colors ${
          hasIssues
            ? 'bg-red-600 hover:bg-red-700 text-white'
            : 'bg-blue-600 hover:bg-blue-700 text-white'
        }`}
        title="Toggle Performance Dashboard"
      >
        📊 Performance {overallScore !== null && `(${overallScore})`}
      </button>

      {/* Dashboard Panel */}
      {isVisible && (
        <div className="mt-2 bg-white dark:bg-gray-900 text-gray-900 dark:text-white rounded-lg shadow-xl border border-gray-200 dark:border-gray-700 w-[600px] max-h-[500px] overflow-hden">
          {/* Header */}
          <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
            <h3 className="text-lg font-semibold">Performance Dashboard</h3>
            <div className="flex items-center space-x-2">
              <div className="flex space-x-1">
                {['overview', 'webvitals', 'api', 'components', 'network'].map(tab => (
                  <button
                    key={tab}
                    onClick={() => setActiveTab(tab as any)} className={`px-3 py-1 rounded text-xs transition-colors capitalize ${
                      activeTab === tab 
                        ? 'bg-blue-600 text-white' 
                        : 'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200'
                    }`}
                  >
                    {tab}
                  </button>
                ))}
              </div>
              <button
                onClick={() => setIsVisible(false)} className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 transition-colors"
                title="Close"
              >
                ✕
              </button>
            </div>
          </div>

          {/* Content */}
          <div className="p-4 max-h-96 overflow-y-auto">
            {activeTab === 'overview' && (
              <div className="space-y-4">
                {/* Overall Score */}
                <div className="text-center p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <div className="text-sm text-gray-600 dark:text-gray-400">Overall Performance Score</div>
                  <div={`text-3xl font-bold ${getScoreColor(overallScore)}`}>
                    {overallScore !== null ? `${overallScore}/100` : 'N/A'}
                  </div>
                </div>

                {/* Quick Stats */}
                <div className="grgr-cols-2 gap-4">
                  <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg">
                    <div className="text-sm text-gray-600 dark:text-gray-400">Memory Usage</div>
                    <div className="text-lg font-semibold">{formatBytes(metrics.memoryUsage)}</div>
                  </div>
                  
                  <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg">
                    <div className="text-sm text-gray-600 dark:text-gray-400">API Calls</div>
                    <div className="text-lg font-semibold">
                      {metrics.apiMetrics.reduce((sum, api) => sum + api.totalRequests, 0)}
                    </div>
                  </div>

                  <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg">
                    <div className="text-sm text-gray-600 dark:text-gray-400">Connection</div>
                    <div className="text-lg font-semibold">
                      {metrics.networkInfo.effectiveType || 'Unknown'}
                    </div>
                  </div>

                  <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg">
                    <div className="text-sm text-gray-600 dark:text-gray-400">Components</div>
                    <div className="text-lg font-semibold">{metrics.componentMetrics.length}</div>
                  </div>
                </div>

                {/* Core Web Vitals Summary */}
                <div className="space-y-2">
                  <h4 className="font-semibold">Core Web Vitals</h4>
                  <div className="grgr-cols-3 gap-2">
                    {['LCP', 'FID', 'CLS'].map(metric => {
                      const data = metrics.webVitals[metric];
                      return (
                        <div key={metric}="bg-gray-50 dark:bg-gray-800 p-2 rounded text-center">
                          <div className="text-xs text-gray-600 dark:text-gray-400">{metric}</div>
                          <div={`text-sm font-semibold ${
                            data ? (data.rating === 'good' ? 'text-green-500' : 
                                   data.rating === 'needs-improvement' ? 'text-yellow-500' : 'text-red-500')
                                 : 'text-gray-400'
                          }`}>
                            {data ? `${Math.round(data.value)}${metric === 'CLS' ? '' : 'ms'}` : 'N/A'}
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'webvitals' && (
              <div className="space-y-3">
                {Object.entries(metrics.webVitals).map(([name, data]: [string, any]) => (
                  <div key={name}="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                    <div>
                      <div className="font-semibold">{name}</div>
                      <div className="text-sm text-gray-600 dark:text-gray-400 capitalize">
                        {data.rating?.replace('-', ' ') || 'Unknown'}
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="font-bold">
                        {Math.round(data.value)}{name === 'CLS' ? '' : 'ms'}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}

            {activeTab === 'api' && (
              <div className="space-y-3">
                {metrics.apiMetrics.map((api, index) => (
                  <div key={index}="p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                    <div className="font-semibold text-sm truncate">{api.endpoint}</div>
                    <div className="grgr-cols-3 gap-2 mt-2 text-xs">
                      <div>
                        <div className="text-gray-600 dark:text-gray-400">Avg Response</div>
                        <div className="font-semibold">{Math.round(api.averageResponseTime)}ms</div>
                      </div>
                      <div>
                        <div className="text-gray-600 dark:text-gray-400">Success Rate</div>
                        <div className="font-semibold">{Math.round(api.successRate * 100)}%</div>
                      </div>
                      <div>
                        <div className="text-gray-600 dark:text-gray-400">Requests</div>
                        <div className="font-semibold">{api.totalRequests}</div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}

            {activeTab === 'components' && (
              <div className="space-y-3">
                {metrics.componentMetrics.map((component, index) => (
                  <div key={index}="p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                    <div className="font-semibold text-sm">{component.componentName}</div>
                    <div className="grgr-cols-2 gap-2 mt-2 text-xs">
                      <div>
                        <div className="text-gray-600 dark:text-gray-400">Avg Render</div>
                        <div className="font-semibold">{component.averageRenderTime.toFixed(2)}ms</div>
                      </div>
                      <div>
                        <div className="text-gray-600 dark:text-gray-400">Renders</div>
                        <div className="font-semibold">{component.renderCount}</div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}

            {activeTab === 'network' && (
              <div className="space-y-3">
                <div className="p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <div className="font-semibold text-sm">Connection Info</div>
                  <div className="mt-2 space-y-1 text-xs">
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">Type:</span>
                      <span>{metrics.networkInfo.effectiveType || 'Unknown'}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">Downlink:</span>
                      <span>{metrics.networkInfo.downlink || 'Unknown'} Mbps</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">RTT:</span>
                      <span>{metrics.networkInfo.rtt || 'Unknown'} ms</span>
                    </div>
                  </div>
                </div>

                <div className="p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <div className="font-semibold text-sm">Browser Info</div>
                  <div className="mt-2 space-y-1 text-xs">
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">Online:</span>
                      <span>{navigator.onLine ? 'Yes' : 'No'}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">Cores:</span>
                      <span>{navigator.hardwareConcurrency || 'Unknown'}</span>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Actions */}
          <div className="border-t border-gray-200 dark:border-gray-700 p-3 flex space-x-2">
            <button
              onClick={() => {
                performanceProfiler.clear();
                apiPerformanceMonitor.clear();
              }}="bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-xs transition-colors"
            >
              Clear Data
            </button>
            <button
              onClick={() => {
                const data = {
                  webVitals: metrics.webVitals,
                  apiMetrics: metrics.apiMetrics,
                  componentMetrics: metrics.componentMetrics,
                  timestamp: new Date().toISOString()
                };
                console.log('Performance Data:', data);
              }}="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-xs transition-colors"
            >
              Export Data
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

// Memoized component for optimal performance
export const PerformanceDashboard = React.memo(PerformanceDashboardComponent, (prevProps, nextProps) => {
  // Only re-render if enabled state changes
  return prevProps.enabled === nextProps.enabled &&
         prevProps.=== nextProps.;
});

PerformanceDashboard.displayName = 'PerformanceDashboard';
