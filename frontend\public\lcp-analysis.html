<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LCP Analysis - RAG Prompt Library</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e0e0e0;
        }
        .metrics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .metric-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }
        .metric-value {
            font-size: 2em;
            font-weight: bold;
            color: #007bff;
        }
        .recommendations {
            margin-top: 30px;
        }
        .recommendation {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 10px;
        }
        .recommendation.critical {
            background: #f8d7da;
            border-color: #f5c6cb;
        }
        .recommendation.high {
            background: #fff3cd;
            border-color: #ffeaa7;
        }
        .recommendation.medium {
            background: #d1ecf1;
            border-color: #bee5eb;
        }
        .element-info {
            background: #e9ecef;
            padding: 15px;
            border-radius: 4px;
            margin: 20px 0;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .start-analysis {
            background: #007bff;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 4px;
            font-size: 16px;
            cursor: pointer;
            margin: 20px 0;
        }
        .start-analysis:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 LCP Analysis Tool</h1>
            <p>Analyze the Largest Contentful Paint element for optimization opportunities</p>
            <button class="start-analysis" onclick="startAnalysis()">Start LCP Analysis</button>
        </div>

        <div id="results" style="display: none;">
            <div class="metrics">
                <div class="metric-card">
                    <h3>LCP Time</h3>
                    <div class="metric-value" id="lcp-time">-</div>
                    <p>Target: &lt; 2.5s</p>
                </div>
                <div class="metric-card">
                    <h3>Element Type</h3>
                    <div class="metric-value" id="element-type">-</div>
                    <p>LCP Element Tag</p>
                </div>
                <div class="metric-card">
                    <h3>Element Size</h3>
                    <div class="metric-value" id="element-size">-</div>
                    <p>Rendered Size</p>
                </div>
            </div>

            <div class="element-info">
                <h3>LCP Element Details:</h3>
                <div id="element-details">Analysis in progress...</div>
            </div>

            <div class="recommendations">
                <h3>🔧 Optimization Recommendations:</h3>
                <div id="recommendations-list">No recommendations yet...</div>
            </div>
        </div>
    </div>

    <script>
        
(function() {
  let lcpData = {
    element: null,
    value: 0,
    entries: [],
    elementInfo: null,
    recommendations: []
  };

  // LCP Observer
  const lcpObserver = new PerformanceObserver((list) => {
    const entries = list.getEntries();
    const lastEntry = entries[entries.length - 1];
    
    lcpData.value = lastEntry.startTime;
    lcpData.element = lastEntry.element;
    lcpData.entries.push({
      startTime: lastEntry.startTime,
      size: lastEntry.size,
      url: lastEntry.url || 'N/A',
      tagName: lastEntry.element?.tagName || 'Unknown',
      id: lastEntry.element?.id || '',
      className: lastEntry.element?.className || '',
      loadTime: lastEntry.loadTime || 0,
      renderTime: lastEntry.renderTime || 0
    });

    // Analyze the LCP element
    if (lastEntry.element) {
      analyzeLCPElement(lastEntry.element, lastEntry);
    }
  });

  lcpObserver.observe({ type: 'largest-contentful-paint', buffered: true });

  function analyzeLCPElement(element, entry) {
    const rect = element.getBoundingClientRect();
    const computedStyle = window.getComputedStyle(element);
    
    lcpData.elementInfo = {
      tagName: element.tagName,
      id: element.id,
      className: element.className,
      textContent: element.textContent?.substring(0, 100) || '',
      src: element.src || '',
      dimensions: {
        width: rect.width,
        height: rect.height,
        area: rect.width * rect.height
      },
      position: {
        top: rect.top,
        left: rect.left,
        bottom: rect.bottom,
        right: rect.right
      },
      styles: {
        fontSize: computedStyle.fontSize,
        fontFamily: computedStyle.fontFamily,
        fontWeight: computedStyle.fontWeight,
        color: computedStyle.color,
        backgroundColor: computedStyle.backgroundColor,
        backgroundImage: computedStyle.backgroundImage,
        display: computedStyle.display,
        position: computedStyle.position
      },
      loading: {
        loadTime: entry.loadTime,
        renderTime: entry.renderTime,
        size: entry.size,
        url: entry.url
      }
    };

    // Generate optimization recommendations
    generateRecommendations(element, entry, lcpData.elementInfo);
  }

  function generateRecommendations(element, entry, info) {
    const recommendations = [];

    // Image optimization recommendations
    if (element.tagName === 'IMG') {
      if (!element.loading || element.loading !== 'eager') {
        recommendations.push({
          type: 'image-loading',
          priority: 'high',
          message: 'Add loading="eager" to LCP image for priority loading',
          fix: 'loading="eager"'
        });
      }

      if (!element.fetchPriority || element.fetchPriority !== 'high') {
        recommendations.push({
          type: 'image-priority',
          priority: 'high',
          message: 'Add fetchpriority="high" to LCP image',
          fix: 'fetchpriority="high"'
        });
      }

      if (element.src && !element.src.includes('.webp') && !element.src.includes('.avif')) {
        recommendations.push({
          type: 'image-format',
          priority: 'medium',
          message: 'Use modern image formats (WebP/AVIF) for better compression',
          fix: 'Convert to WebP or AVIF format'
        });
      }

      if (info.dimensions.width > 800 || info.dimensions.height > 600) {
        recommendations.push({
          type: 'image-size',
          priority: 'medium',
          message: 'Consider optimizing image dimensions for the display size',
          fix: 'Resize image to actual display dimensions'
        });
      }
    }

    // Text optimization recommendations
    if (element.tagName === 'H1' || element.tagName === 'H2' || element.tagName === 'P') {
      const fontFamily = info.styles.fontFamily;
      if (fontFamily.includes('Google') || fontFamily.includes('fonts.googleapis.com')) {
        recommendations.push({
          type: 'font-loading',
          priority: 'high',
          message: 'Preload critical fonts to avoid layout shift',
          fix: 'Add <link rel="preload" href="font-url" as="font" crossorigin>'
        });
      }

      if (info.styles.fontWeight !== '400' && info.styles.fontWeight !== 'normal') {
        recommendations.push({
          type: 'font-weight',
          priority: 'low',
          message: 'Consider using standard font weights for faster loading',
          fix: 'Use font-weight: 400 or 700'
        });
      }
    }

    // Layout optimization recommendations
    if (entry.startTime > 2500) {
      recommendations.push({
        type: 'lcp-timing',
        priority: 'critical',
        message: 'LCP is too slow (>2.5s). Critical optimization needed.',
        fix: 'Implement resource preloading, optimize images, reduce render-blocking resources'
      });
    }

    if (info.position.top > window.innerHeight) {
      recommendations.push({
        type: 'above-fold',
        priority: 'medium',
        message: 'LCP element is below the fold',
        fix: 'Move critical content above the fold or optimize initial viewport'
      });
    }

    lcpData.recommendations = recommendations;
  }

  // Export data for analysis
  window.lcpAnalysisData = lcpData;

  // Log results after page load
  window.addEventListener('load', () => {
    setTimeout(() => {
      console.log('🎯 LCP Analysis Results:', lcpData);
      
      if (lcpData.value > 2500) {
        console.warn('⚠️ LCP is slow:', lcpData.value + 'ms (target: <2500ms)');
      } else {
        console.log('✅ LCP is good:', lcpData.value + 'ms');
      }

      // Display recommendations
      if (lcpData.recommendations.length > 0) {
        console.group('🔧 LCP Optimization Recommendations:');
        lcpData.recommendations.forEach(rec => {
          const emoji = rec.priority === 'critical' ? '🚨' : 
                       rec.priority === 'high' ? '⚡' : 
                       rec.priority === 'medium' ? '⚠️' : 'ℹ️';
          console.log(emoji + ' ' + rec.message);
          console.log('   Fix: ' + rec.fix);
        });
        console.groupEnd();
      }
    }, 1000);
  });
})();


        function startAnalysis() {
            document.getElementById('results').style.display = 'block';
            
            // Navigate to the main app in an iframe for analysis
            const iframe = document.createElement('iframe');
            iframe.src = '/';
            iframe.style.width = '100%';
            iframe.style.height = '600px';
            iframe.style.border = '1px solid #ddd';
            iframe.style.marginTop = '20px';
            
            iframe.onload = function() {
                // Inject analysis script into iframe
                try {
                    const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                    const script = iframeDoc.createElement('script');
                    script.textContent = `
(function() {
  let lcpData = {
    element: null,
    value: 0,
    entries: [],
    elementInfo: null,
    recommendations: []
  };

  // LCP Observer
  const lcpObserver = new PerformanceObserver((list) => {
    const entries = list.getEntries();
    const lastEntry = entries[entries.length - 1];
    
    lcpData.value = lastEntry.startTime;
    lcpData.element = lastEntry.element;
    lcpData.entries.push({
      startTime: lastEntry.startTime,
      size: lastEntry.size,
      url: lastEntry.url || 'N/A',
      tagName: lastEntry.element?.tagName || 'Unknown',
      id: lastEntry.element?.id || '',
      className: lastEntry.element?.className || '',
      loadTime: lastEntry.loadTime || 0,
      renderTime: lastEntry.renderTime || 0
    });

    // Analyze the LCP element
    if (lastEntry.element) {
      analyzeLCPElement(lastEntry.element, lastEntry);
    }
  });

  lcpObserver.observe({ type: 'largest-contentful-paint', buffered: true });

  function analyzeLCPElement(element, entry) {
    const rect = element.getBoundingClientRect();
    const computedStyle = window.getComputedStyle(element);
    
    lcpData.elementInfo = {
      tagName: element.tagName,
      id: element.id,
      className: element.className,
      textContent: element.textContent?.substring(0, 100) || '',
      src: element.src || '',
      dimensions: {
        width: rect.width,
        height: rect.height,
        area: rect.width * rect.height
      },
      position: {
        top: rect.top,
        left: rect.left,
        bottom: rect.bottom,
        right: rect.right
      },
      styles: {
        fontSize: computedStyle.fontSize,
        fontFamily: computedStyle.fontFamily,
        fontWeight: computedStyle.fontWeight,
        color: computedStyle.color,
        backgroundColor: computedStyle.backgroundColor,
        backgroundImage: computedStyle.backgroundImage,
        display: computedStyle.display,
        position: computedStyle.position
      },
      loading: {
        loadTime: entry.loadTime,
        renderTime: entry.renderTime,
        size: entry.size,
        url: entry.url
      }
    };

    // Generate optimization recommendations
    generateRecommendations(element, entry, lcpData.elementInfo);
  }

  function generateRecommendations(element, entry, info) {
    const recommendations = [];

    // Image optimization recommendations
    if (element.tagName === 'IMG') {
      if (!element.loading || element.loading !== 'eager') {
        recommendations.push({
          type: 'image-loading',
          priority: 'high',
          message: 'Add loading="eager" to LCP image for priority loading',
          fix: 'loading="eager"'
        });
      }

      if (!element.fetchPriority || element.fetchPriority !== 'high') {
        recommendations.push({
          type: 'image-priority',
          priority: 'high',
          message: 'Add fetchpriority="high" to LCP image',
          fix: 'fetchpriority="high"'
        });
      }

      if (element.src && !element.src.includes('.webp') && !element.src.includes('.avif')) {
        recommendations.push({
          type: 'image-format',
          priority: 'medium',
          message: 'Use modern image formats (WebP/AVIF) for better compression',
          fix: 'Convert to WebP or AVIF format'
        });
      }

      if (info.dimensions.width > 800 || info.dimensions.height > 600) {
        recommendations.push({
          type: 'image-size',
          priority: 'medium',
          message: 'Consider optimizing image dimensions for the display size',
          fix: 'Resize image to actual display dimensions'
        });
      }
    }

    // Text optimization recommendations
    if (element.tagName === 'H1' || element.tagName === 'H2' || element.tagName === 'P') {
      const fontFamily = info.styles.fontFamily;
      if (fontFamily.includes('Google') || fontFamily.includes('fonts.googleapis.com')) {
        recommendations.push({
          type: 'font-loading',
          priority: 'high',
          message: 'Preload critical fonts to avoid layout shift',
          fix: 'Add <link rel="preload" href="font-url" as="font" crossorigin>'
        });
      }

      if (info.styles.fontWeight !== '400' && info.styles.fontWeight !== 'normal') {
        recommendations.push({
          type: 'font-weight',
          priority: 'low',
          message: 'Consider using standard font weights for faster loading',
          fix: 'Use font-weight: 400 or 700'
        });
      }
    }

    // Layout optimization recommendations
    if (entry.startTime > 2500) {
      recommendations.push({
        type: 'lcp-timing',
        priority: 'critical',
        message: 'LCP is too slow (>2.5s). Critical optimization needed.',
        fix: 'Implement resource preloading, optimize images, reduce render-blocking resources'
      });
    }

    if (info.position.top > window.innerHeight) {
      recommendations.push({
        type: 'above-fold',
        priority: 'medium',
        message: 'LCP element is below the fold',
        fix: 'Move critical content above the fold or optimize initial viewport'
      });
    }

    lcpData.recommendations = recommendations;
  }

  // Export data for analysis
  window.lcpAnalysisData = lcpData;

  // Log results after page load
  window.addEventListener('load', () => {
    setTimeout(() => {
      console.log('🎯 LCP Analysis Results:', lcpData);
      
      if (lcpData.value > 2500) {
        console.warn('⚠️ LCP is slow:', lcpData.value + 'ms (target: <2500ms)');
      } else {
        console.log('✅ LCP is good:', lcpData.value + 'ms');
      }

      // Display recommendations
      if (lcpData.recommendations.length > 0) {
        console.group('🔧 LCP Optimization Recommendations:');
        lcpData.recommendations.forEach(rec => {
          const emoji = rec.priority === 'critical' ? '🚨' : 
                       rec.priority === 'high' ? '⚡' : 
                       rec.priority === 'medium' ? '⚠️' : 'ℹ️';
          console.log(emoji + ' ' + rec.message);
          console.log('   Fix: ' + rec.fix);
        });
        console.groupEnd();
      }
    }, 1000);
  });
})();
`;
                    iframeDoc.head.appendChild(script);
                    
                    // Monitor for results
                    setTimeout(() => {
                        const lcpData = iframe.contentWindow.lcpAnalysisData;
                        if (lcpData) {
                            displayResults(lcpData);
                        }
                    }, 3000);
                } catch (e) {
                    console.error('Cross-origin iframe access blocked. Running analysis on current page.');
                    setTimeout(() => {
                        if (window.lcpAnalysisData) {
                            displayResults(window.lcpAnalysisData);
                        }
                    }, 3000);
                }
            };
            
            document.querySelector('.container').appendChild(iframe);
        }

        function displayResults(lcpData) {
            // Update metrics
            document.getElementById('lcp-time').textContent = 
                lcpData.value ? (lcpData.value / 1000).toFixed(2) + 's' : 'N/A';
            
            document.getElementById('element-type').textContent = 
                lcpData.elementInfo?.tagName || 'Unknown';
            
            document.getElementById('element-size').textContent = 
                lcpData.elementInfo?.dimensions ? 
                Math.round(lcpData.elementInfo.dimensions.area) + 'px²' : 'N/A';

            // Display element details
            if (lcpData.elementInfo) {
                document.getElementById('element-details').textContent = 
                    JSON.stringify(lcpData.elementInfo, null, 2);
            }

            // Display recommendations
            const recommendationsContainer = document.getElementById('recommendations-list');
            if (lcpData.recommendations && lcpData.recommendations.length > 0) {
                recommendationsContainer.innerHTML = lcpData.recommendations.map(rec => `
                    <div class="recommendation ${rec.priority}">
                        <strong>${rec.priority.toUpperCase()}: ${rec.message}</strong>
                        <br><em>Fix: ${rec.fix}</em>
                    </div>
                `).join('');
            } else {
                recommendationsContainer.innerHTML = '<p>No specific recommendations found.</p>';
            }
        }
    </script>
</body>
</html>